/**
 * Enrollment Service for FreeCodeLap
 * Handles both paid and manual enrollments
 */

import { db } from '@/lib/firebase';
import { 
  createEnrollment, 
  getUserEnrollment, 
  getUserEnrollments, 
  getCourseEnrollments,
  updateEnrollment,
  getPayment 
} from '@/lib/firestore';
import { Enrollment, Payment, Course } from '@/types/schema';
import { Timestamp } from 'firebase/firestore';
import { paystackService } from './paystackService';

export interface EnrollmentData {
  userId: string;
  courseId: string;
  enrollmentType: 'paid' | 'manual';
  paymentId?: string;
  adminNote?: string;
  enrolledBy?: string;
}

export interface PaidEnrollmentData {
  userId: string;
  courseId: string;
  courseTitle: string;
  courseInstructor: string;
  amount: number;
  customerEmail: string;
  customerName?: string;
  customerPhone?: string;
}

class EnrollmentService {
  /**
   * Check if user is enrolled in a course
   */
  async isUserEnrolled(userId: string, courseId: string): Promise<boolean> {
    try {
      const enrollment = await getUserEnrollment(userId, courseId);
      return enrollment !== null && enrollment.status === 'active';
    } catch (error) {
      console.error('❌ Error checking enrollment:', error);
      return false;
    }
  }

  /**
   * Get user's enrollment for a specific course
   */
  async getUserCourseEnrollment(userId: string, courseId: string): Promise<Enrollment | null> {
    try {
      return await getUserEnrollment(userId, courseId);
    } catch (error) {
      console.error('❌ Error getting user enrollment:', error);
      return null;
    }
  }

  /**
   * Get all enrollments for a user
   */
  async getUserEnrollments(userId: string): Promise<Enrollment[]> {
    try {
      return await getUserEnrollments(userId);
    } catch (error) {
      console.error('❌ Error getting user enrollments:', error);
      return [];
    }
  }

  /**
   * Get all enrollments for a course
   */
  async getCourseEnrollments(courseId: string): Promise<Enrollment[]> {
    try {
      return await getCourseEnrollments(courseId);
    } catch (error) {
      console.error('❌ Error getting course enrollments:', error);
      return [];
    }
  }

  /**
   * Initialize a paid enrollment (creates payment and returns Paystack config)
   */
  async initializePaidEnrollment(data: PaidEnrollmentData): Promise<{
    paymentId: string;
    paystackConfig: any;
  }> {
    try {
      // Check if user is already enrolled
      const existingEnrollment = await this.isUserEnrolled(data.userId, data.courseId);
      if (existingEnrollment) {
        throw new Error('User is already enrolled in this course');
      }

      // Initialize payment with Paystack
      const { paymentId, config } = await paystackService.initializePayment({
        userId: data.userId,
        courseId: data.courseId,
        courseTitle: data.courseTitle,
        courseInstructor: data.courseInstructor,
        amount: data.amount,
        currency: 'USD',
        customerEmail: data.customerEmail,
        customerName: data.customerName,
        customerPhone: data.customerPhone,
      });

      return {
        paymentId,
        paystackConfig: config,
      };
    } catch (error) {
      console.error('❌ Error initializing paid enrollment:', error);
      throw error;
    }
  }

  /**
   * Complete paid enrollment after successful payment
   */
  async completePaidEnrollment(paymentId: string): Promise<string> {
    try {
      // Get payment details
      const payment = await getPayment(paymentId);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'successful') {
        throw new Error('Payment is not successful');
      }

      // Check if enrollment already exists
      const existingEnrollment = await this.isUserEnrolled(payment.userId, payment.courseId);
      if (existingEnrollment) {
        console.log('ℹ️ User already enrolled, skipping enrollment creation');
        return 'already-enrolled';
      }

      // Create enrollment
      const enrollmentData: Omit<Enrollment, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: payment.userId,
        courseId: payment.courseId,
        enrolledAt: Timestamp.now(),
        enrollmentType: 'paid',
        status: 'active',
        paymentId: paymentId,
        progress: {
          completedLessons: [],
          totalLessons: 0, // Will be updated when course structure is loaded
          progressPercentage: 0,
          lastAccessedAt: Timestamp.now(),
        },
        certificateIssued: false,
      };

      const enrollmentId = await createEnrollment(enrollmentData);
      console.log('✅ Paid enrollment completed successfully:', enrollmentId);

      return enrollmentId;
    } catch (error) {
      console.error('❌ Error completing paid enrollment:', error);
      throw error;
    }
  }

  /**
   * Create manual enrollment (admin only)
   */
  async createManualEnrollment(data: EnrollmentData): Promise<string> {
    try {
      // Check if user is already enrolled
      const existingEnrollment = await this.isUserEnrolled(data.userId, data.courseId);
      if (existingEnrollment) {
        throw new Error('User is already enrolled in this course');
      }

      // Create enrollment
      const enrollmentData: Omit<Enrollment, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: data.userId,
        courseId: data.courseId,
        enrolledAt: Timestamp.now(),
        enrollmentType: 'manual',
        status: 'active',
        progress: {
          completedLessons: [],
          totalLessons: 0, // Will be updated when course structure is loaded
          progressPercentage: 0,
          lastAccessedAt: Timestamp.now(),
        },
        certificateIssued: false,
        adminNote: data.adminNote,
        enrolledBy: data.enrolledBy,
      };

      const enrollmentId = await createEnrollment(enrollmentData);
      console.log('✅ Manual enrollment created successfully:', enrollmentId);

      return enrollmentId;
    } catch (error) {
      console.error('❌ Error creating manual enrollment:', error);
      throw error;
    }
  }

  /**
   * Update enrollment progress
   */
  async updateProgress(
    enrollmentId: string, 
    completedLessonId: string, 
    totalLessons: number
  ): Promise<void> {
    try {
      // Get current enrollment
      const enrollment = await getUserEnrollment(enrollmentId, ''); // This needs to be fixed
      if (!enrollment) {
        throw new Error('Enrollment not found');
      }

      // Update completed lessons
      const completedLessons = [...enrollment.progress.completedLessons];
      if (!completedLessons.includes(completedLessonId)) {
        completedLessons.push(completedLessonId);
      }

      const progressPercentage = Math.round((completedLessons.length / totalLessons) * 100);

      // Update enrollment
      await updateEnrollment(enrollmentId, {
        progress: {
          completedLessons,
          totalLessons,
          progressPercentage,
          lastAccessedAt: Timestamp.now(),
          lastCompletedLessonId: completedLessonId,
        },
      });

      console.log('✅ Progress updated successfully');
    } catch (error) {
      console.error('❌ Error updating progress:', error);
      throw error;
    }
  }

  /**
   * Suspend enrollment
   */
  async suspendEnrollment(enrollmentId: string, adminNote?: string): Promise<void> {
    try {
      await updateEnrollment(enrollmentId, {
        status: 'suspended',
        adminNote: adminNote || 'Enrollment suspended',
      });

      console.log('✅ Enrollment suspended successfully');
    } catch (error) {
      console.error('❌ Error suspending enrollment:', error);
      throw error;
    }
  }

  /**
   * Reactivate enrollment
   */
  async reactivateEnrollment(enrollmentId: string): Promise<void> {
    try {
      await updateEnrollment(enrollmentId, {
        status: 'active',
      });

      console.log('✅ Enrollment reactivated successfully');
    } catch (error) {
      console.error('❌ Error reactivating enrollment:', error);
      throw error;
    }
  }

  /**
   * Mark course as completed and issue certificate
   */
  async completeCourse(enrollmentId: string, certificateUrl?: string): Promise<void> {
    try {
      const updates: Partial<Enrollment> = {
        status: 'completed',
        certificateIssued: true,
        certificateIssuedAt: Timestamp.now(),
      };

      if (certificateUrl) {
        updates.certificateUrl = certificateUrl;
      }

      await updateEnrollment(enrollmentId, updates);

      console.log('✅ Course completed and certificate issued');
    } catch (error) {
      console.error('❌ Error completing course:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const enrollmentService = new EnrollmentService();
