/**
 * Simple Currency Context for FreeCodeLap
 * Handles USD-based pricing with local currency display
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { simpleExchangeService, SupportedCurrency } from '@/services/simpleExchangeService';
import { useAuth } from '@/contexts/AuthContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface SimpleCurrencyContextType {
  // Current state
  userCurrency: string;
  supportedCurrencies: SupportedCurrency[];
  loading: boolean;
  
  // Actions
  setUserCurrency: (currency: string) => Promise<void>;
  convertFromUSD: (usdAmount: number, targetCurrency?: string) => Promise<number>;
  formatCurrency: (amount: number, currency?: string) => string;
  
  // Utilities
  detectCurrency: () => Promise<string>;
  refreshRates: () => Promise<void>;
  getCurrencyInfo: (currency: string) => SupportedCurrency | null;
}

const SimpleCurrencyContext = createContext<SimpleCurrencyContextType | undefined>(undefined);

interface SimpleCurrencyProviderProps {
  children: ReactNode;
}

export const SimpleCurrencyProvider: React.FC<SimpleCurrencyProviderProps> = ({ children }) => {
  const [userCurrency, setUserCurrencyState] = useState<string>('USD');
  const [supportedCurrencies, setSupportedCurrencies] = useState<SupportedCurrency[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useAuth();

  // Initialize on mount
  useEffect(() => {
    initializeService();
  }, []);

  // Initialize user currency
  useEffect(() => {
    initializeUserCurrency();
  }, [currentUser]);

  /**
   * Initialize the exchange service
   */
  const initializeService = async () => {
    try {
      await simpleExchangeService.initialize();
      setSupportedCurrencies(simpleExchangeService.getSupportedCurrencies());
    } catch (error) {
      console.error('Error initializing exchange service:', error);
    }
  };

  /**
   * Initialize user's currency preference
   */
  const initializeUserCurrency = async () => {
    try {
      setLoading(true);
      
      // Priority 1: User's saved preference
      if (currentUser?.preferredCurrency) {
        console.log('📱 Using saved user currency:', currentUser.preferredCurrency);
        setUserCurrencyState(currentUser.preferredCurrency);
        setLoading(false);
        return;
      }
      
      // Priority 2: Local storage
      const savedCurrency = localStorage.getItem('userDisplayCurrency');
      if (savedCurrency) {
        console.log('💾 Using saved currency from localStorage:', savedCurrency);
        setUserCurrencyState(savedCurrency);
        setLoading(false);
        return;
      }
      
      // Priority 3: Auto-detect
      const detectedCurrency = await detectCurrency();
      console.log('🌍 Auto-detected currency:', detectedCurrency);
      setUserCurrencyState(detectedCurrency);
      
      // Save to localStorage
      localStorage.setItem('userDisplayCurrency', detectedCurrency);
      
    } catch (error) {
      console.error('Error initializing user currency:', error);
      setUserCurrencyState('USD'); // Fallback
    } finally {
      setLoading(false);
    }
  };

  /**
   * Detect user's currency based on location
   */
  const detectCurrency = async (): Promise<string> => {
    try {
      return await simpleExchangeService.detectUserCurrency();
    } catch (error) {
      console.error('Error detecting currency:', error);
      return 'USD';
    }
  };

  /**
   * Set user's preferred display currency
   */
  const setUserCurrency = async (newCurrency: string): Promise<void> => {
    try {
      console.log('💱 Changing display currency to:', newCurrency);
      
      // Update state
      setUserCurrencyState(newCurrency);
      
      // Save to localStorage
      localStorage.setItem('userDisplayCurrency', newCurrency);
      
      // Save to user profile if logged in
      if (currentUser?.uid) {
        try {
          await updateDoc(doc(db, 'users', currentUser.uid), {
            preferredCurrency: newCurrency,
            currencyUpdatedAt: new Date()
          });
          console.log('✅ Currency preference saved to user profile');
        } catch (error) {
          console.error('Error saving currency to user profile:', error);
          // Don't throw - localStorage save is sufficient
        }
      }
      
      console.log('✅ Display currency changed successfully');
    } catch (error) {
      console.error('Error setting currency:', error);
      throw error;
    }
  };

  /**
   * Convert USD amount to target currency
   */
  const convertFromUSD = async (
    usdAmount: number,
    targetCurrency?: string
  ): Promise<number> => {
    const currency = targetCurrency || userCurrency;
    
    try {
      return await simpleExchangeService.convertFromUSD(usdAmount, currency);
    } catch (error) {
      console.error('Error converting from USD:', error);
      return usdAmount; // Return original amount on error
    }
  };

  /**
   * Format amount with currency symbol
   */
  const formatCurrency = (amount: number, currency?: string): string => {
    const targetCurrency = currency || userCurrency;
    return simpleExchangeService.formatCurrency(amount, targetCurrency);
  };

  /**
   * Refresh exchange rates
   */
  const refreshRates = async (): Promise<void> => {
    try {
      await simpleExchangeService.initialize();
      console.log('✅ Exchange rates refreshed');
    } catch (error) {
      console.error('Error refreshing rates:', error);
      throw error;
    }
  };

  /**
   * Get currency information
   */
  const getCurrencyInfo = (currency: string): SupportedCurrency | null => {
    return simpleExchangeService.getCurrencyInfo(currency);
  };

  const value: SimpleCurrencyContextType = {
    // State
    userCurrency,
    supportedCurrencies,
    loading,
    
    // Actions
    setUserCurrency,
    convertFromUSD,
    formatCurrency,
    
    // Utilities
    detectCurrency,
    refreshRates,
    getCurrencyInfo
  };

  return (
    <SimpleCurrencyContext.Provider value={value}>
      {children}
    </SimpleCurrencyContext.Provider>
  );
};

/**
 * Hook to use simple currency context
 */
export const useSimpleCurrency = (): SimpleCurrencyContextType => {
  const context = useContext(SimpleCurrencyContext);
  
  if (context === undefined) {
    throw new Error('useSimpleCurrency must be used within a SimpleCurrencyProvider');
  }
  
  return context;
};

/**
 * Hook for USD to local currency conversion
 */
export const useUSDConversion = (usdAmount: number, targetCurrency?: string) => {
  const { userCurrency, convertFromUSD } = useSimpleCurrency();
  const [convertedAmount, setConvertedAmount] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const currency = targetCurrency || userCurrency;
  
  useEffect(() => {
    const convert = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (currency === 'USD') {
          setConvertedAmount(usdAmount);
        } else {
          const converted = await convertFromUSD(usdAmount, currency);
          setConvertedAmount(converted);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Conversion failed');
        setConvertedAmount(usdAmount); // Fallback to USD amount
      } finally {
        setLoading(false);
      }
    };
    
    convert();
  }, [usdAmount, currency, convertFromUSD]);
  
  return { convertedAmount, loading, error };
};

/**
 * Hook for formatting USD amounts with local equivalent
 */
export const useFormattedUSDPrice = (usdAmount: number) => {
  const { userCurrency, formatCurrency } = useSimpleCurrency();
  const { convertedAmount, loading } = useUSDConversion(usdAmount);
  
  if (loading || !convertedAmount) {
    return `$${usdAmount.toFixed(2)} USD`;
  }
  
  if (userCurrency === 'USD') {
    return `$${usdAmount.toFixed(2)} USD`;
  }
  
  const localFormatted = formatCurrency(convertedAmount, userCurrency);
  return `$${usdAmount.toFixed(2)} USD (≈ ${localFormatted})`;
};
