export type UserRole = 'student' | 'admin';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;

  // Profile information
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  bio?: string;

  // Course-related
  enrolledCourses: string[]; // Course IDs
  completedCourses: string[]; // Course IDs
  certificates: string[]; // Certificate IDs

  // Payment-related
  paymentHistory: string[]; // Payment IDs
  subscriptionStatus?: 'active' | 'inactive' | 'trial';
  
  // Settings
  preferences: {
    emailNotifications: boolean;
    courseUpdates: boolean;
    marketingEmails: boolean;
    language: string;
    timezone: string;
  };
}

export interface CourseProgress {
  courseId: string;
  userId: string;
  enrolledAt: Date;
  lastAccessedAt: Date;
  completionPercentage: number;
  completedLessons: string[]; // Lesson IDs
  currentLesson?: string; // Current lesson ID
  timeSpent: number; // in minutes
  quizScores: { [quizId: string]: number };
  assignments: { [assignmentId: string]: AssignmentSubmission };
  certificateIssued?: boolean;
  certificateId?: string;
}

export interface AssignmentSubmission {
  submittedAt: Date;
  content: string;
  attachments?: string[]; // File URLs
  grade?: number;
  feedback?: string;
  gradedAt?: Date;
  gradedBy?: string; // Instructor ID
}

export interface Certificate {
  id: string;
  userId: string;
  courseId: string;
  issuedAt: Date;
  certificateUrl: string; // PDF URL
  verificationCode: string;
  courseName: string;
  instructorName: string;
  completionDate: Date;
}

export interface PaymentRecord {
  id: string;
  userId: string;
  courseId?: string;
  amount: number;
  currency: string;
  paymentMethod: 'card' | 'm-pesa' | 'bank_transfer';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paystackReference: string;
  createdAt: Date;
  completedAt?: Date;
  metadata?: {
    courseName?: string;
    paymentType: 'course_purchase' | 'subscription' | 'other';
  };
}

// Auth-related interfaces
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
}

export interface AuthState {
  currentUser: any | null; // Firebase User
  userProfile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string;
}
