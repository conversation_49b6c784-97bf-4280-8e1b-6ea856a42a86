// Course Management System Types

export interface Course {
  id: string;
  title: string;
  description: string;
  shortDescription?: string;
  thumbnail: string;
  videoUrl?: string; // Course preview video URL
  instructor: string;
  instructorId: string;
  instructorAvatar?: string;
  instructorBio?: string;
  duration: string; // e.g., "8 weeks" or "40 hours"
  totalDuration: number; // in minutes
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  category: string;
  price: number;
  originalPrice?: number;
  currency: string;
  tags: string[];
  modules: CourseModule[];
  requirements: string[];
  learningOutcomes: string[];
  targetAudience: string[];
  createdAt: Date;
  updatedAt: Date;
  isPublished: boolean;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  enrollmentCount: number;
  enrolledCount: number;
  maxEnrollments?: number;
  rating: number;
  reviewCount: number;

  // Course settings
  allowComments: boolean;
  allowDownloads: boolean;
  certificateEnabled: boolean;

  // Access control
  accessType: 'lifetime' | 'subscription' | 'limited';
  accessDuration?: number; // in days for limited access
}

export interface CourseModule {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  assignments: Assignment[];
  quizzes: Quiz[];
  isLocked: boolean;
  unlockConditions?: UnlockCondition[];
}

export interface Lesson {
  id: string;
  moduleId: string;
  title: string;
  description: string;
  type: 'video' | 'text' | 'interactive' | 'live';
  order: number;
  duration: number; // in minutes
  videoUrl?: string;
  textContent?: string;
  resources: LessonResource[];
  isPreview: boolean;
  isFree?: boolean; // Whether lesson is free to preview
  isCompleted?: boolean;
  watchTime?: number; // user's watch time in seconds
  lastWatchedAt?: Date;
}

export interface LessonResource {
  id: string;
  title: string;
  type: 'pdf' | 'zip' | 'link' | 'code';
  url: string;
  size?: number; // in bytes
  description?: string;
}

export interface Assignment {
  id: string;
  moduleId: string;
  title: string;
  description: string;
  instructions: string;
  type: 'project' | 'exercise' | 'essay' | 'code';
  maxScore: number;
  dueDate?: Date;
  allowedFileTypes: string[];
  maxFileSize: number; // in MB
  isRequired: boolean;
  order: number;
  submissions?: AssignmentSubmission[];
}

export interface AssignmentSubmission {
  id: string;
  assignmentId: string;
  userId: string;
  content: string;
  files: SubmissionFile[];
  submittedAt: Date;
  score?: number;
  feedback?: string;
  status: 'submitted' | 'graded' | 'returned' | 'late';
  gradedAt?: Date;
  gradedBy?: string;
}

export interface SubmissionFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadedAt: Date;
}

export interface Quiz {
  id: string;
  moduleId: string;
  title: string;
  description: string;
  instructions: string;
  timeLimit?: number; // in minutes
  maxAttempts: number;
  passingScore: number;
  questions: QuizQuestion[];
  isRequired: boolean;
  order: number;
  attempts?: QuizAttempt[];
}

export interface QuizQuestion {
  id: string;
  quizId: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay' | 'code';
  question: string;
  options?: string[]; // for multiple choice
  correctAnswer: string | string[]; // can be multiple for multi-select
  explanation?: string;
  points: number;
  order: number;
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  answers: QuizAnswer[];
  score: number;
  maxScore: number;
  percentage: number;
  startedAt: Date;
  completedAt?: Date;
  timeSpent: number; // in seconds
  passed: boolean;
  attemptNumber: number;
}

export interface QuizAnswer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  pointsEarned: number;
}

export interface UserProgress {
  id: string;
  userId: string;
  courseId: string;
  enrolledAt: Date;
  lastAccessedAt: Date;
  completionPercentage: number;
  completedLessons: string[];
  completedAssignments: string[];
  completedQuizzes: string[];
  totalWatchTime: number; // in seconds
  certificateEarned: boolean;
  certificateIssuedAt?: Date;
  currentModule?: string;
  currentLesson?: string;
}

export interface Certificate {
  id: string;
  userId: string;
  courseId: string;
  studentName: string;
  courseName: string;
  instructorName: string;
  completionDate: Date;
  issuedAt: Date;
  certificateNumber: string;
  verificationCode: string;
  grade?: string;
  creditsEarned?: number;
}

export interface UnlockCondition {
  type: 'lesson' | 'assignment' | 'quiz' | 'module';
  targetId: string;
  requirement: 'complete' | 'pass' | 'score';
  value?: number; // for score requirements
}

export interface VideoProgress {
  lessonId: string;
  userId: string;
  currentTime: number;
  duration: number;
  watchedSegments: TimeSegment[];
  completionPercentage: number;
  lastWatchedAt: Date;
  playbackSpeed: number;
  bookmarks: VideoBookmark[];
}

export interface TimeSegment {
  start: number;
  end: number;
}

export interface VideoBookmark {
  id: string;
  time: number;
  title: string;
  note?: string;
  createdAt: Date;
}

// Enums for better type safety
export enum CourseLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum LessonType {
  VIDEO = 'video',
  TEXT = 'text',
  INTERACTIVE = 'interactive',
  LIVE = 'live'
}

export enum AssignmentType {
  PROJECT = 'project',
  EXERCISE = 'exercise',
  ESSAY = 'essay',
  CODE = 'code'
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple-choice',
  TRUE_FALSE = 'true-false',
  SHORT_ANSWER = 'short-answer',
  ESSAY = 'essay',
  CODE = 'code'
}

export enum SubmissionStatus {
  SUBMITTED = 'submitted',
  GRADED = 'graded',
  RETURNED = 'returned',
  LATE = 'late'
}
