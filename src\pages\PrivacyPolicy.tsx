import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Shield, Eye, Lock, Mail } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PrivacyPolicy: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/')}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Privacy Policy</h1>
              <p className="text-sm text-gray-600">Last updated: December 12, 2024</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="bg-blue-50 border-b">
            <CardTitle className="flex items-center space-x-2 text-blue-900">
              <Shield className="w-6 h-6" />
              <span>FreeCodeLap Privacy Policy</span>
            </CardTitle>
            <p className="text-blue-700 mt-2">
              Your privacy is important to us. This policy explains how we collect, use, and protect your information.
            </p>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* Introduction */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Eye className="w-5 h-5 text-blue-600" />
                <span>1. Introduction</span>
              </h2>
              <div className="prose text-gray-700 space-y-4">
                <p>
                  Welcome to FreeCodeLap ("we," "our," or "us"). We are committed to protecting your personal information 
                  and your right to privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard 
                  your information when you visit our website and use our online learning services.
                </p>
                <p>
                  <strong>Contact Information:</strong><br />
                  Business Name: FreeCodeLap<br />
                  Email: <EMAIL><br />
                  Phone: +************<br />
                  Location: Kenya
                </p>
              </div>
            </section>

            {/* Information We Collect */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Information We Collect</h2>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">Personal Information You Provide:</h3>
                  <ul className="list-disc list-inside text-gray-700 space-y-1">
                    <li>Name and email address (for account creation)</li>
                    <li>Phone number (for M-Pesa payments and course notifications)</li>
                    <li>Payment information (processed securely by Paystack)</li>
                    <li>Profile information (optional: bio, profile picture)</li>
                    <li>Course progress and completion data</li>
                    <li>Communication preferences</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">Automatically Collected Information:</h3>
                  <ul className="list-disc list-inside text-gray-700 space-y-1">
                    <li>Device information (browser type, operating system)</li>
                    <li>Usage data (pages visited, time spent, course interactions)</li>
                    <li>IP address and location data (for security and analytics)</li>
                    <li>Cookies and similar tracking technologies</li>
                    <li>Learning analytics (progress, quiz scores, completion rates)</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* How We Use Information */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">3. How We Use Your Information</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">Service Delivery:</h3>
                  <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                    <li>Provide access to courses and learning materials</li>
                    <li>Track your learning progress and achievements</li>
                    <li>Process payments and manage subscriptions</li>
                    <li>Send course updates and notifications</li>
                    <li>Provide customer support</li>
                  </ul>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-900 mb-2">Improvement & Analytics:</h3>
                  <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                    <li>Analyze usage patterns to improve our platform</li>
                    <li>Personalize your learning experience</li>
                    <li>Develop new courses and features</li>
                    <li>Ensure platform security and prevent fraud</li>
                    <li>Comply with legal obligations</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Information Sharing */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">4. Information Sharing and Disclosure</h2>
              <div className="space-y-4">
                <div className="border-l-4 border-red-500 pl-4 bg-red-50 p-4 rounded">
                  <h3 className="font-medium text-red-900 mb-2">We DO NOT sell your personal information.</h3>
                  <p className="text-red-800 text-sm">
                    Your data is never sold to third parties for marketing or commercial purposes.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium text-gray-900">We may share information with:</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <div>
                        <strong>Payment Processors:</strong> Paystack (for secure payment processing)
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <div>
                        <strong>Service Providers:</strong> Firebase (for authentication and data storage)
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <div>
                        <strong>Legal Requirements:</strong> When required by law or to protect our rights
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Data Security */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Lock className="w-5 h-5 text-green-600" />
                <span>5. Data Security</span>
              </h2>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-green-900 mb-2">Technical Safeguards:</h3>
                    <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                      <li>SSL/TLS encryption for data transmission</li>
                      <li>Secure cloud storage with Firebase</li>
                      <li>Regular security updates and monitoring</li>
                      <li>Access controls and authentication</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium text-green-900 mb-2">Operational Safeguards:</h3>
                    <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                      <li>Limited access to personal data</li>
                      <li>Regular staff training on data protection</li>
                      <li>Incident response procedures</li>
                      <li>Data backup and recovery systems</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Your Rights */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">6. Your Privacy Rights</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h3 className="font-medium text-gray-900">You have the right to:</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Access your personal information</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Correct inaccurate information</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Delete your account and data</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Export your data</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Opt-out of marketing communications</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">How to Exercise Your Rights:</h3>
                  <p className="text-blue-800 text-sm mb-3">
                    Contact <NAME_EMAIL> or +************ to request:
                  </p>
                  <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                    <li>Data access or correction</li>
                    <li>Account deletion</li>
                    <li>Data export</li>
                    <li>Marketing opt-out</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Cookies and Tracking */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">7. Cookies and Tracking</h2>
              <div className="space-y-4">
                <p className="text-gray-700">
                  We use cookies and similar technologies to enhance your experience, analyze usage, and provide personalized content.
                </p>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Essential Cookies</h3>
                    <p className="text-sm text-gray-700">Required for basic site functionality and security.</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Analytics Cookies</h3>
                    <p className="text-sm text-gray-700">Help us understand how you use our platform.</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Preference Cookies</h3>
                    <p className="text-sm text-gray-700">Remember your settings and preferences.</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Data Retention */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">8. Data Retention</h2>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <ul className="space-y-2 text-sm">
                  <li><strong>Account Data:</strong> Retained while your account is active</li>
                  <li><strong>Course Progress:</strong> Retained for 3 years after course completion</li>
                  <li><strong>Payment Records:</strong> Retained for 7 years for tax and legal compliance</li>
                  <li><strong>Marketing Data:</strong> Deleted immediately upon opt-out request</li>
                  <li><strong>Analytics Data:</strong> Anonymized after 2 years</li>
                </ul>
              </div>
            </section>

            {/* Contact Information */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Mail className="w-5 h-5 text-blue-600" />
                <span>9. Contact Us</span>
              </h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <p className="text-blue-900 mb-4">
                  If you have questions about this Privacy Policy or our data practices, please contact us:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">Primary Contact:</h3>
                    <p className="text-blue-800 text-sm">
                      Email: <EMAIL><br />
                      Phone: +************<br />
                      Response Time: Within 48 hours
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">Business Information:</h3>
                    <p className="text-blue-800 text-sm">
                      Business Name: FreeCodeLap<br />
                      Location: Kenya<br />
                      Business Type: Online Education Platform
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Updates */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">10. Policy Updates</h2>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-4 rounded">
                <p className="text-blue-900 text-sm">
                  We may update this Privacy Policy from time to time. We will notify you of any material changes by:
                </p>
                <ul className="list-disc list-inside text-blue-800 text-sm mt-2 space-y-1">
                  <li>Email notification to registered users</li>
                  <li>Prominent notice on our website</li>
                  <li>In-app notification for mobile users</li>
                </ul>
                <p className="text-blue-900 text-sm mt-2">
                  <strong>Last Updated:</strong> December 12, 2024
                </p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
