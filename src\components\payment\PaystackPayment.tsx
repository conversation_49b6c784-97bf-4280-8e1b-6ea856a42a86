/**
 * Secure Paystack Payment Component
 * Handles payment processing with proper error handling
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Smartphone, Shield, Lock } from 'lucide-react';
import { paystackService } from '@/services/paystackService';
import { enrollmentService } from '@/services/enrollmentService';

// Paystack types
declare global {
  interface Window {
    PaystackPop: {
      setup: (config: any) => {
        openIframe: () => void;
      };
    };
  }
}

interface PaystackPaymentProps {
  userId: string;
  courseId: string;
  courseTitle: string;
  courseInstructor: string;
  amount: number; // USD amount
  customerEmail: string;
  customerName?: string;
  customerPhone?: string;
  onSuccess?: (enrollmentId: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

export const PaystackPayment: React.FC<PaystackPaymentProps> = ({
  userId,
  courseId,
  courseTitle,
  courseInstructor,
  amount,
  customerEmail,
  customerName,
  customerPhone,
  onSuccess,
  onError,
  onCancel,
}) => {
  const [loading, setLoading] = useState(false);
  const [paystackLoaded, setPaystackLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'mobile_money'>('card');

  // Load Paystack script
  useEffect(() => {
    if (window.PaystackPop) {
      setPaystackLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://js.paystack.co/v1/inline.js';
    script.async = true;
    script.onload = () => setPaystackLoaded(true);
    script.onerror = () => setError('Failed to load payment system');
    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  const handlePayment = async () => {
    if (!paystackLoaded) {
      setError('Payment system is not ready. Please try again.');
      return;
    }

    if (!paystackService.isConfigured()) {
      setError('Payment system is not properly configured.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Initialize payment
      const { paymentId, paystackConfig } = await enrollmentService.initializePaidEnrollment({
        userId,
        courseId,
        courseTitle,
        courseInstructor,
        amount,
        customerEmail,
        customerName,
        customerPhone,
      });

      // Override callback to handle enrollment
      const originalCallback = paystackConfig.callback;
      paystackConfig.callback = async (response: any) => {
        try {
          // Call original callback to handle payment verification
          await originalCallback(response);

          // Complete enrollment
          const enrollmentId = await enrollmentService.completePaidEnrollment(paymentId);
          
          if (enrollmentId && enrollmentId !== 'already-enrolled') {
            onSuccess?.(enrollmentId);
          } else if (enrollmentId === 'already-enrolled') {
            onError?.('You are already enrolled in this course');
          }
        } catch (error: any) {
          console.error('❌ Error completing enrollment:', error);
          onError?.(error.message || 'Failed to complete enrollment');
        } finally {
          setLoading(false);
        }
      };

      // Override onClose to handle cancellation
      paystackConfig.onClose = () => {
        setLoading(false);
        onCancel?.();
      };

      // Filter channels based on payment method
      if (paymentMethod === 'mobile_money') {
        paystackConfig.channels = ['mobile_money'];
      } else {
        paystackConfig.channels = ['card', 'bank_transfer', 'bank', 'ussd'];
      }

      // Open Paystack popup
      const handler = window.PaystackPop.setup(paystackConfig);
      handler.openIframe();

    } catch (error: any) {
      console.error('❌ Payment initialization error:', error);
      setError(error.message || 'Failed to initialize payment');
      setLoading(false);
    }
  };

  const kesAmount = Math.round(amount * 130); // USD to KES conversion

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Shield className="h-5 w-5 mr-2 text-green-400" />
          Secure Payment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Payment Amount */}
        <div className="bg-gray-700 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-gray-300">Course Price:</span>
            <div className="text-right">
              <div className="text-white font-bold">${amount} USD</div>
              <div className="text-gray-400 text-sm">≈ KES {kesAmount.toLocaleString()}</div>
            </div>
          </div>
        </div>

        {/* Payment Method Selection */}
        <div className="space-y-3">
          <label className="text-white font-medium">Choose Payment Method:</label>
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant={paymentMethod === 'card' ? 'default' : 'outline'}
              className={`h-12 ${
                paymentMethod === 'card'
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
              onClick={() => setPaymentMethod('card')}
              disabled={loading}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Card
            </Button>
            <Button
              variant={paymentMethod === 'mobile_money' ? 'default' : 'outline'}
              className={`h-12 ${
                paymentMethod === 'mobile_money'
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
              onClick={() => setPaymentMethod('mobile_money')}
              disabled={loading}
            >
              <Smartphone className="h-4 w-4 mr-2" />
              M-Pesa
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-600 bg-red-900/20">
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
        )}

        {/* Payment Button */}
        <Button
          onClick={handlePayment}
          disabled={loading || !paystackLoaded}
          className="w-full bg-blue-600 hover:bg-blue-700 h-12 text-lg font-medium"
        >
          {loading ? (
            <>
              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Lock className="h-5 w-5 mr-2" />
              Pay Securely with {paymentMethod === 'card' ? 'Card' : 'M-Pesa'}
            </>
          )}
        </Button>

        {/* Security Notice */}
        <div className="text-center text-gray-400 text-sm">
          <div className="flex items-center justify-center space-x-1">
            <Shield className="h-4 w-4" />
            <span>Secured by Paystack • PCI DSS Compliant</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
