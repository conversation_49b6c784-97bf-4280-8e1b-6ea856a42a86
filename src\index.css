
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Blue Dark Theme Variables */
:root {
  --blue-dark-950: #0c1426;
  --blue-dark-900: #1e2a4a;
  --blue-dark-800: #2d3f6f;
  --blue-dark-700: #3c5394;
  --blue-dark-600: #4b67b9;
  --blue-dark-500: #5a7bde;
  --blue-dark-400: #7b9ce8;
  --blue-dark-300: #9cbdf2;
  --blue-dark-200: #bddefc;
  --blue-dark-100: #deffff;
  --button-blue: #2563eb;
  --button-blue-hover: #1d4ed8;
  --button-pink: #ec4899;
  --button-pink-hover: #db2777;
  --button-purple: #8b5cf6;
  --button-purple-hover: #7c3aed;
}

/* Global Blue Dark Theme */
body {
  background: linear-gradient(135deg, var(--blue-dark-950) 0%, var(--blue-dark-900) 100%);
  color: var(--blue-dark-100);
  min-height: 100vh;
}

/* Consistent page background */
#root {
  background: linear-gradient(135deg, var(--blue-dark-950) 0%, var(--blue-dark-900) 100%);
  min-height: 100vh;
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-200deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.8s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.8s ease-out forwards;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Staggered Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }

/* Enhanced Card Elevation Styles */
.card-elevated {
  background: linear-gradient(145deg, var(--blue-dark-800), var(--blue-dark-700));
  border: 1px solid var(--blue-dark-600);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.4),
    0 10px 10px -5px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 16px;
}

.card-elevated:hover {
  transform: translateY(-12px) scale(1.03) rotateX(5deg);
  box-shadow:
    0 40px 80px -12px rgba(0, 0, 0, 0.5),
    0 30px 30px -5px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(75, 103, 185, 0.3);
}

/* Card Bounce Animation */
.card-bounce {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Card Slide Animations */
.card-slide-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.card-slide-right {
  animation: slideInRight 0.8s ease-out forwards;
}

/* Enhanced Button Animations */
.btn-animated {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(1px) translateZ(0);
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-animated:hover::before {
  left: 100%;
}

.btn-animated:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn-animated:active {
  transform: translateY(-2px) scale(0.98);
}

/* Blue Button Variants */
.btn-blue {
  background: linear-gradient(135deg, var(--button-blue), var(--button-blue-hover));
  color: white !important;
  border: none;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-blue:hover {
  background: linear-gradient(135deg, var(--button-blue-hover), #1e40af);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Pink Button Variants */
.btn-pink {
  background: linear-gradient(135deg, var(--button-pink), var(--button-pink-hover));
  color: white !important;
  border: none;
  box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
}

.btn-pink:hover {
  background: linear-gradient(135deg, var(--button-pink-hover), #be185d);
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);
}

/* Purple Button Variants */
.btn-purple {
  background: linear-gradient(135deg, var(--button-purple), var(--button-purple-hover));
  color: white !important;
  border: none;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-purple:hover {
  background: linear-gradient(135deg, var(--button-purple-hover), #6d28d9);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* Green Button Variants */
.btn-green {
  background: linear-gradient(135deg, #059669, #047857);
  color: white !important;
  border: none;
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.btn-green:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
}

/* Payment Form Styles */
.payment-tab-active {
  background: linear-gradient(135deg, var(--button-blue), var(--button-blue-hover));
  color: white;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.payment-tab-mpesa-active {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.payment-input {
  background: white !important;
  border: 2px solid #e5e7eb !important;
  color: #1f2937 !important;
  transition: all 0.2s ease;
  font-size: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.payment-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.payment-input:hover {
  border-color: #9ca3af;
}

.payment-input::placeholder {
  color: #9ca3af;
  font-size: 16px;
}

/* Card Input Animations */
.card-input-focused {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.card-logo-animation {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Real-time validation styles */
.input-valid {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.input-invalid {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Card type detection animation */
.card-type-detected {
  animation: cardDetected 0.5s ease;
}

@keyframes cardDetected {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.mpesa-stk-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced button styles */
.btn-payment {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-payment:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.btn-payment:disabled {
  background: #9ca3af;
  box-shadow: none;
  transform: none;
  cursor: not-allowed;
}

.btn-payment-mpesa {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-payment-mpesa:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  box-shadow: 0 6px 16px rgba(5, 150, 105, 0.4);
}

/* Ghost Button with Blue Dark Theme */
.btn-ghost {
  background: transparent;
  color: var(--blue-dark-200) !important;
  border: 2px solid var(--blue-dark-600);
  backdrop-filter: blur(10px);
}

.btn-ghost:hover {
  background: rgba(75, 103, 185, 0.2);
  color: white !important;
  border-color: var(--blue-dark-400);
}

/* Section Backgrounds - Blue Dark Theme */
.section-dark {
  background: linear-gradient(135deg, var(--blue-dark-950) 0%, var(--blue-dark-900) 100%);
}

.section-darker {
  background: linear-gradient(135deg, var(--blue-dark-900) 0%, var(--blue-dark-800) 100%);
}

.section-primary {
  background: linear-gradient(135deg, var(--blue-dark-950) 0%, var(--blue-dark-900) 100%);
}

/* Text Colors for Dark Theme */
.text-primary-dark { color: var(--dark-blue-100); }
.text-secondary-dark { color: var(--dark-blue-300); }
.text-accent { color: var(--accent-blue); }

/* Glassmorphism Effect */
.glass-effect {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 214 20% 97%; /* #F4F6F8 - Soft Gray */
    --foreground: 0 0% 10%; /* #1A1A1A - Charcoal Black */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;

    --primary: 233 44% 57%; /* #5C6BC0 - Indigo */
    --primary-foreground: 0 0% 98%;
    --primary-hover: 237 67% 47%; /* #3A2BC5 - Darker indigo for hover */

    --secondary: 214 20% 95%;
    --secondary-foreground: 0 0% 10%;

    --muted: 214 20% 95%;
    --muted-foreground: 215 16% 47%;

    --accent: 16 90% 63%; /* #FF7043 - Deep Orange */
    --accent-foreground: 0 0% 98%;

    --success: 122 39% 49%; /* #43A047 - Green */
    --success-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 214 31% 91%;
    --input: 214 31% 91%;
    --ring: 233 44% 57%;

    --radius: 0.5rem;

    --sidebar-background: 214 20% 98%;
    --sidebar-foreground: 0 0% 26%;
    --sidebar-primary: 233 44% 57%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 214 20% 96%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 214 20% 91%;
    --sidebar-ring: 233 44% 57%;

    /* Hero gradient colors */
    --hero-gradient-start: 248 89% 56%; /* #4B39EF */
    --hero-gradient-end: 258 100% 67%; /* #7C4DFF */
  }

  .dark {
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 233 44% 57%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 237 67% 47%;

    --secondary: 217 33% 18%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 16 90% 63%;
    --accent-foreground: 0 0% 98%;

    --success: 122 39% 49%;
    --success-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 233 44% 57%;
    --sidebar-background: 0 0% 10%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 233 44% 57%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 16%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 16%;
    --sidebar-ring: 233 44% 57%;

    --hero-gradient-start: 248 89% 56%;
    --hero-gradient-end: 258 100% 67%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-[hsl(var(--primary-hover))] rounded-3xl px-6 py-3 font-semibold text-base transition-colors duration-200;
  }
  
  .card-elevated {
    @apply bg-card rounded-2xl p-4 shadow-sm border border-border/50;
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, hsl(var(--hero-gradient-start)), hsl(var(--hero-gradient-end)));
  }
}
