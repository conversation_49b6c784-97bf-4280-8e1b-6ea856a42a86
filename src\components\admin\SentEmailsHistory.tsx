/**
 * FreeCodeLap Platform - Sent Emails History
 * 
 * Interface to view sent emails, delivery status, and email analytics
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Mail, 
  Search, 
  Eye, 
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  Download,
  RefreshCw,
  Send,
  AlertCircle
} from 'lucide-react';
import { realEmailService, SentEmail } from '@/services/realEmailService';

interface SentEmailsHistoryProps {
  className?: string;
}

export const SentEmailsHistory: React.FC<SentEmailsHistoryProps> = ({ className }) => {
  const [sentEmails, setSentEmails] = useState<SentEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedEmail, setSelectedEmail] = useState<SentEmail | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);

  useEffect(() => {
    loadSentEmails();
    loadStatistics();
  }, []);

  const loadSentEmails = async () => {
    try {
      setLoading(true);
      const emails = await realEmailService.getSentEmails(100);
      setSentEmails(emails);
    } catch (error) {
      console.error('Error loading sent emails:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await realEmailService.getEmailStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading email statistics:', error);
    }
  };

  const filteredEmails = sentEmails.filter(email => {
    const matchesSearch = email.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         email.sentByName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || email.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewDetails = (email: SentEmail) => {
    setSelectedEmail(email);
    setShowDetailsModal(true);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { color: 'bg-green-600', icon: CheckCircle, text: 'Completed' },
      sending: { color: 'bg-blue-600', icon: Clock, text: 'Sending' },
      pending: { color: 'bg-yellow-600', icon: Clock, text: 'Pending' },
      failed: { color: 'bg-red-600', icon: XCircle, text: 'Failed' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} text-white flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.text}
      </Badge>
    );
  };

  const exportEmailHistory = () => {
    const csvContent = [
      ['Subject', 'Sent By', 'Recipients', 'Success', 'Failed', 'Status', 'Date'].join(','),
      ...filteredEmails.map(email => [
        `"${email.subject}"`,
        `"${email.sentByName}"`,
        email.totalRecipients,
        email.successCount,
        email.failedCount,
        email.status,
        email.createdAt.toISOString()
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `email_history_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white">Sent Emails History</h3>
          <p className="text-gray-400">Track and monitor all sent email notifications</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={loadSentEmails}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={exportEmailHistory}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Send className="h-8 w-8 text-blue-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Total Emails</p>
                  <p className="text-2xl font-bold text-white">{statistics.totalEmailsSent}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Total Recipients</p>
                  <p className="text-2xl font-bold text-white">{statistics.totalRecipients}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-purple-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Success Rate</p>
                  <p className="text-2xl font-bold text-white">{statistics.successRate.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-yellow-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Recent Activity</p>
                  <p className="text-2xl font-bold text-white">{statistics.recentActivity.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by subject or sender..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="sending">Sending</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Emails Table */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Mail className="h-5 w-5 mr-2 text-gray-400" />
            Sent Emails ({filteredEmails.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">Loading email history...</div>
            </div>
          ) : filteredEmails.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Mail className="h-12 w-12 text-gray-600 mx-auto mb-2" />
                <div className="text-gray-400">No emails found</div>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Subject</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Sent By</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Recipients</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Success/Failed</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Date</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEmails.map((email) => (
                    <tr key={email.id} className="border-b border-gray-700 hover:bg-gray-750">
                      <td className="py-3 px-4">
                        <div>
                          <p className="text-white font-medium">{email.subject}</p>
                          {email.templateUsed && (
                            <p className="text-gray-400 text-sm">Template: {email.templateUsed}</p>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-gray-300">{email.sentByName}</p>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-white">{email.totalRecipients}</p>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Badge className="bg-green-600 text-white">
                            {email.successCount} sent
                          </Badge>
                          {email.failedCount > 0 && (
                            <Badge className="bg-red-600 text-white">
                              {email.failedCount} failed
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {getStatusBadge(email.status)}
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-gray-300 text-sm">{formatDate(email.createdAt)}</p>
                        {email.completedAt && (
                          <p className="text-gray-400 text-xs">
                            Completed: {formatDate(email.completedAt)}
                          </p>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-gray-400 hover:text-white"
                          onClick={() => handleViewDetails(email)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Email Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Email Details: {selectedEmail?.subject}
            </DialogTitle>
          </DialogHeader>

          {selectedEmail && (
            <div className="space-y-6">
              {/* Email Info */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">Email Information</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-gray-400">Subject:</span>
                      <p className="text-white">{selectedEmail.subject}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Sent By:</span>
                      <p className="text-white">{selectedEmail.sentByName}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Template:</span>
                      <p className="text-white">{selectedEmail.templateUsed || 'Custom'}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Status:</span>
                      <div className="mt-1">{getStatusBadge(selectedEmail.status)}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">Delivery Statistics</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-gray-400">Total Recipients:</span>
                      <p className="text-white">{selectedEmail.totalRecipients}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Successfully Sent:</span>
                      <p className="text-green-400">{selectedEmail.successCount}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Failed:</span>
                      <p className="text-red-400">{selectedEmail.failedCount}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Success Rate:</span>
                      <p className="text-white">
                        {((selectedEmail.successCount / selectedEmail.totalRecipients) * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Email Content */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Email Content</h4>
                {selectedEmail.htmlContent ? (
                  <div
                    className="bg-white p-4 rounded border max-h-64 overflow-y-auto"
                    dangerouslySetInnerHTML={{ __html: selectedEmail.htmlContent }}
                  />
                ) : (
                  <pre className="text-gray-300 bg-gray-700 p-4 rounded whitespace-pre-wrap max-h-64 overflow-y-auto">
                    {selectedEmail.content}
                  </pre>
                )}
              </div>

              {/* Recipients List */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Recipients ({selectedEmail.recipients.length})</h4>
                <div className="max-h-64 overflow-y-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 px-3 text-gray-300 font-medium">Email</th>
                        <th className="text-left py-2 px-3 text-gray-300 font-medium">Name</th>
                        <th className="text-left py-2 px-3 text-gray-300 font-medium">Status</th>
                        <th className="text-left py-2 px-3 text-gray-300 font-medium">Sent At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedEmail.recipients.map((recipient, index) => (
                        <tr key={index} className="border-b border-gray-700">
                          <td className="py-2 px-3 text-gray-300">{recipient.email}</td>
                          <td className="py-2 px-3 text-gray-300">{recipient.name}</td>
                          <td className="py-2 px-3">
                            <Badge className={`${
                              recipient.status === 'sent' ? 'bg-green-600' :
                              recipient.status === 'failed' ? 'bg-red-600' : 'bg-yellow-600'
                            } text-white text-xs`}>
                              {recipient.status}
                            </Badge>
                          </td>
                          <td className="py-2 px-3 text-gray-300 text-sm">
                            {recipient.sentAt ? formatDate(recipient.sentAt) : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Failed Recipients Details */}
              {selectedEmail.failedCount > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-red-400 mb-3 flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    Failed Deliveries ({selectedEmail.failedCount})
                  </h4>
                  <div className="bg-red-900/20 border border-red-700 rounded p-4">
                    {selectedEmail.recipients
                      .filter(r => r.status === 'failed')
                      .map((recipient, index) => (
                        <div key={index} className="mb-2">
                          <span className="text-red-400">{recipient.email}</span>
                          {recipient.error && (
                            <span className="text-gray-400 ml-2">- {recipient.error}</span>
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
