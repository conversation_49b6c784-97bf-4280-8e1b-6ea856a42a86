import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/user';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requireAuth?: boolean;
  fallbackPath?: string;
}

export function ProtectedRoute({
  children,
  requiredRole,
  requireAuth = true,
  fallbackPath = '/'
}: ProtectedRouteProps) {
  const { currentUser, userProfile, loading, isAdmin } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !currentUser) {
    // Redirect to home page with return URL
    return <Navigate to={`/?redirect=${encodeURIComponent(location.pathname)}`} replace />;
  }

  // Check if user profile is loaded (for role-based access)
  if (requireAuth && currentUser && !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Check role-based access
  if (requiredRole) {
    // Special handling for admin role
    if (requiredRole === 'admin') {
      if (!isAdmin()) {
        // If user is not admin, redirect to dashboard
        return <Navigate to="/dashboard" replace />;
      }
    } else if (userProfile && userProfile.role !== requiredRole) {
      // For other roles, check userProfile
      const redirectPath = getUserRoleRedirect(userProfile.role);
      return <Navigate to={redirectPath} replace />;
    }
  }

  // If all checks pass, render the protected content
  return <>{children}</>;
}

// Helper function to determine redirect path based on user role
function getUserRoleRedirect(role: UserRole): string {
  switch (role) {
    case 'admin':
      return '/admin';
    case 'student':
      return '/dashboard';
    default:
      return '/';
  }
}

// Convenience components for specific roles
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="admin" fallbackPath="/dashboard">
      {children}
    </ProtectedRoute>
  );
}

export function StudentRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="student" fallbackPath="/dashboard">
      {children}
    </ProtectedRoute>
  );
}

// Component for routes that require any authenticated user
export function AuthenticatedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={true}>
      {children}
    </ProtectedRoute>
  );
}

// Component for public routes (no authentication required)
export function PublicRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={false}>
      {children}
    </ProtectedRoute>
  );
}

export default ProtectedRoute;
