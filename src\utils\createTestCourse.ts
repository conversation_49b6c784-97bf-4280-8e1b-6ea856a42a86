import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * Creates a test course with proper video URLs for debugging the video player
 */
export const createTestCourse = async () => {
  const testCourseId = 'test-course-video-player';
  
  const testCourse = {
    id: testCourseId,
    title: 'Test Course - Video Player',
    description: 'A test course to verify video player functionality',
    shortDescription: 'Test course for video player debugging',
    instructor: '<PERSON>',
    instructorId: 'ahmed-takal',
    level: 'Beginner',
    category: 'Programming',
    price: 100,
    currency: 'KES',
    duration: '1 week',
    totalDuration: 60,
    thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    requirements: ['Basic computer skills'],
    learningOutcomes: ['Learn video player functionality'],
    tags: ['Test', 'Video'],
    targetAudience: ['Developers'],
    isPublished: true,
    featured: false,
    status: 'published',
    enrollmentCount: 0,
    enrolledCount: 0,
    rating: 5.0,
    reviewCount: 1,
    allowComments: true,
    allowDownloads: true,
    certificateEnabled: true,
    accessType: 'lifetime',
    createdAt: new Date(),
    updatedAt: new Date(),
    
    // Embedded modules with lessons containing video URLs
    modules: [
      {
        id: 'test-module-1',
        courseId: testCourseId,
        title: 'Introduction Module',
        description: 'Basic introduction to the course',
        order: 1,
        lessons: [
          {
            id: 'test-lesson-1',
            moduleId: 'test-module-1',
            title: 'Welcome Video - Direct MP4 (Free)',
            description: 'Welcome to the test course - Direct video file',
            type: 'video',
            order: 1,
            duration: 5,
            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
            textContent: '',
            resources: [],
            isFree: true, // This should be accessible in preview mode
            isPreview: true,
            isCompleted: false,
            watchTime: 0
          },
          {
            id: 'test-lesson-2',
            moduleId: 'test-module-1',
            title: 'YouTube Video Example (Free)',
            description: 'Example YouTube video - should use iframe player',
            type: 'video',
            order: 2,
            duration: 8,
            videoUrl: 'https://youtu.be/dQw4w9WgXcQ', // Rick Roll for testing YouTube URLs
            textContent: '',
            resources: [],
            isFree: true, // This should be accessible in preview mode
            isPreview: true,
            isCompleted: false,
            watchTime: 0
          },
          {
            id: 'test-lesson-3',
            moduleId: 'test-module-1',
            title: 'Another Direct Video (Paid)',
            description: 'Getting started with the course content - Direct video',
            type: 'video',
            order: 3,
            duration: 12,
            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
            textContent: '',
            resources: [],
            isFree: false, // This should require purchase
            isPreview: false,
            isCompleted: false,
            watchTime: 0
          }
        ],
        assignments: [],
        quizzes: [],
        isLocked: false
      },
      {
        id: 'test-module-2',
        courseId: testCourseId,
        title: 'Advanced Topics',
        description: 'Advanced course content',
        order: 2,
        lessons: [
          {
            id: 'test-lesson-4',
            moduleId: 'test-module-2',
            title: 'Vimeo Video Example (Paid)',
            description: 'Example Vimeo video - should use iframe player',
            type: 'video',
            order: 1,
            duration: 15,
            videoUrl: 'https://vimeo.com/148751763', // Sample Vimeo video
            textContent: '',
            resources: [
              {
                title: 'Course Notes',
                url: 'https://example.com/notes.pdf',
                type: 'pdf'
              }
            ],
            isFree: false, // This should require purchase
            isPreview: false,
            isCompleted: false,
            watchTime: 0
          },
          {
            id: 'test-lesson-5',
            moduleId: 'test-module-2',
            title: 'Final Project - Direct Video (Paid)',
            description: 'Build your final project - Direct video file',
            type: 'video',
            order: 2,
            duration: 20,
            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
            textContent: '',
            resources: [],
            isFree: false, // This should require purchase
            isPreview: false,
            isCompleted: false,
            watchTime: 0
          }
        ],
        assignments: [],
        quizzes: [],
        isLocked: false
      }
    ]
  };

  try {
    console.log('🔧 Creating test course with video URLs...');
    const courseRef = doc(db, 'courses', testCourseId);
    await setDoc(courseRef, testCourse);
    console.log('✅ Test course created successfully!');
    console.log('📄 Course ID:', testCourseId);
    console.log('📄 Free lessons: 2 (should work in preview mode)');
    console.log('📄 Paid lessons: 3 (should work after purchase)');
    console.log('📄 Video types included:');
    console.log('  - Direct MP4 files (Google sample videos)');
    console.log('  - YouTube video (iframe embed)');
    console.log('  - Vimeo video (iframe embed)');
    
    return testCourseId;
  } catch (error) {
    console.error('❌ Error creating test course:', error);
    throw error;
  }
};

/**
 * Utility to call from browser console for testing
 */
(window as any).createTestCourse = createTestCourse;
