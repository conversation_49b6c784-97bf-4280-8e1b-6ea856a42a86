import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Play, Users, BookOpen, Star, CheckCircle } from 'lucide-react';

const FinalCTASection = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-black/30"></div>
      <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-purple-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-400/10 rounded-full blur-2xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
            Start Learning Today
          </h2>
          <p className="text-xl md:text-2xl mb-8 text-gray-300 leading-relaxed">
            Join the future of development with No-Code and AI-powered coding. 
            Transform your career and start building real applications today!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <Button
              size="lg"
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg font-semibold transform hover:scale-105 transition-all duration-200"
              asChild
            >
              <Link to="/courses">
                <BookOpen className="w-5 h-5 mr-2" />
                Browse Courses
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>

            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 border-gray-400 text-gray-300 hover:bg-gray-800 hover:text-white text-lg font-semibold transform hover:scale-105 transition-all duration-200"
              asChild
            >
              <Link to="/live-classes">
                <Users className="w-5 h-5 mr-2" />
                Join Live Classes
              </Link>
            </Button>
          </div>

          {/* What You Get */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-8 border border-gray-700 mb-12">
            <h3 className="text-2xl font-bold mb-6">What You Get When You Join FreeCodeLap</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Lifetime access to all course materials</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Build real applications for your portfolio</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Certificate of completion</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Community support and networking</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Learn from industry expert Ahmed Takal</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">Secure payment with Paystack (M-Pesa & Cards)</span>
              </div>
            </div>
          </div>

          {/* Success Stories */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center mb-12">
            <div className="bg-blue-900/40 backdrop-blur-sm rounded-lg p-6 border border-blue-700/50">
              <div className="text-3xl font-bold text-blue-400 mb-2">2</div>
              <div className="text-gray-300">Comprehensive Courses</div>
            </div>
            <div className="bg-purple-900/40 backdrop-blur-sm rounded-lg p-6 border border-purple-700/50">
              <div className="text-3xl font-bold text-purple-400 mb-2">83+</div>
              <div className="text-gray-300">Total Lessons</div>
            </div>
            <div className="bg-blue-900/40 backdrop-blur-sm rounded-lg p-6 border border-blue-700/50">
              <div className="text-3xl font-bold text-blue-400 mb-2">Lifetime</div>
              <div className="text-gray-300">Course Access</div>
            </div>
          </div>

          {/* Final Encouragement */}
          <div className="bg-gradient-to-r from-green-900/50 to-blue-900/50 backdrop-blur-sm rounded-lg p-8 border border-green-700/50">
            <div className="flex justify-center mb-4">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-current" />
                ))}
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Career?</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Don't wait! Start your journey into the future of development today. 
              Whether you choose No-Code with FlutterFlow or AI-powered coding, 
              you'll be building real applications in no time.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                asChild
              >
                <Link to="/auth?mode=register">
                  <Play className="w-5 h-5 mr-2" />
                  Get Started Now - It's Free!
                </Link>
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="border-green-500 text-green-400 hover:bg-green-900/20"
                asChild
              >
                <Link to="/courses">
                  View Course Details
                </Link>
              </Button>
            </div>
          </div>

          {/* Money Back Guarantee */}
          <div className="mt-8 text-center">
            <p className="text-gray-400 text-sm">
              💰 <strong>14-Day Money-Back Guarantee</strong> - Not satisfied? Get a full refund within 14 days.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTASection;
