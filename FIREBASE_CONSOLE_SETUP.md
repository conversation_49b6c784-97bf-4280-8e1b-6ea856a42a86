# 🔥 Firebase Console Configuration Guide

## ⚠️ URGENT: Fix Google Sign-In Timeout Issue

The Google Sign-In timeout you're experiencing is most likely due to **domain authorization** issues in Firebase Console. Follow these steps immediately:

## 🚀 Quick Fix Steps

### 1. Firebase Console - Authorized Domains
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **`codefreelap`**
3. Navigate to **Authentication** → **Settings** → **Authorized domains**
4. Click **"Add domain"** and add these domains:
   - `localhost`
   - `localhost:5173`
   - `localhost:8080`

### 2. Google Cloud Console - OAuth Configuration
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: **`codefreelap`**
3. Navigate to **APIs & Services** → **Credentials**
4. Find your **OAuth 2.0 Client ID** (Web application)
5. Click **Edit** and add these **Authorized JavaScript origins**:
   - `http://localhost:5173`
   - `http://localhost:8080`
6. Add these **Authorized redirect URIs**:
   - `http://localhost:5173/__/auth/handler`
   - `http://localhost:8080/__/auth/handler`

### 3. OAuth Consent Screen
1. In Google Cloud Console, go to **APIs & Services** → **OAuth consent screen**
2. Ensure these are configured:
   - **Application name**: FreeCodeLap
   - **User support email**: <EMAIL>
   - **Developer contact information**: <EMAIL>

## 🔧 Detailed Configuration Steps

### Firebase Authentication Settings

#### Enable Google Sign-In
1. **Authentication** → **Sign-in method**
2. Click **Google** provider
3. Enable the toggle
4. Set **Web SDK configuration**:
   - **Project support email**: <EMAIL>

#### Authorized Domains Configuration
```
✅ localhost
✅ localhost:5173
✅ localhost:8080
✅ codefreelap.firebaseapp.com (default)
```

### Google Cloud Console Settings

#### OAuth 2.0 Client Configuration
**Authorized JavaScript origins:**
```
http://localhost:5173
http://localhost:8080
https://codefreelap.firebaseapp.com
```

**Authorized redirect URIs:**
```
http://localhost:5173/__/auth/handler
http://localhost:8080/__/auth/handler
https://codefreelap.firebaseapp.com/__/auth/handler
```

## 🧪 Test After Configuration

1. **Save all changes** in both Firebase and Google Cloud Console
2. **Wait 5-10 minutes** for changes to propagate
3. **Clear browser cache** and cookies
4. **Refresh** your application at http://localhost:5173
5. **Try Google Sign-In** again

## 🚨 Common Issues and Solutions

### Issue: "Unauthorized domain" Error
**Solution:** Add `localhost:5173` to Firebase Console authorized domains

### Issue: "Invalid origin" Error
**Solution:** Add `http://localhost:5173` to Google Cloud Console JavaScript origins

### Issue: Still Getting Timeout
**Possible Causes:**
1. Changes haven't propagated yet (wait 10 minutes)
2. Browser cache needs clearing
3. Firewall/antivirus blocking requests
4. Network connectivity issues

### Issue: "Configuration Error"
**Solution:** Verify all environment variables in `.env` file:
```
VITE_FIREBASE_API_KEY=AIzaSyABB72plhO6QQEvbCGoiJnuowDbtjjJ2OM
VITE_FIREBASE_AUTH_DOMAIN=codefreelap.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=codefreelap
```

## 📋 Verification Checklist

After making changes, verify:
- [ ] Firebase Console: Google provider enabled
- [ ] Firebase Console: localhost:5173 in authorized domains
- [ ] Google Cloud Console: OAuth client configured
- [ ] Google Cloud Console: JavaScript origins added
- [ ] Google Cloud Console: Redirect URIs added
- [ ] OAuth consent screen configured
- [ ] Environment variables correct
- [ ] Browser cache cleared
- [ ] Application refreshed

## 🎯 Expected Result

After proper configuration:
1. **Google Sign-In button** should work without timeout
2. **Popup should open** and show Google account selection
3. **Authentication should complete** within 5-10 seconds
4. **User profile should be created** in Firestore
5. **AuthDebug panel** should show successful authentication

## 📞 Still Having Issues?

If the timeout persists after configuration:

1. **Check AuthDebug panel** for specific error messages
2. **Check browser console** for detailed error logs
3. **Try incognito mode** to rule out browser extensions
4. **Try different browser** to isolate browser-specific issues
5. **Check network connectivity** and firewall settings

## 🔄 Alternative: Use Email/Password

While fixing Google Sign-In configuration, you can use email/password authentication which should work immediately:

1. Click **"Sign up for free"** in the auth modal
2. Fill in the registration form
3. Use email/password login

---

**Priority Action:** Configure Firebase Console authorized domains immediately to fix the timeout issue.