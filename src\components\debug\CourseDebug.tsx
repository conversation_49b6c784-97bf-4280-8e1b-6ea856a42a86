import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { courseService } from '@/services/courseService';
import { createTestCourse } from '@/utils/createTestCourse';
import { doc, setDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export const CourseDebug = () => {
  const [loading, setLoading] = useState(false);
  const [courses, setCourses] = useState<any[]>([]);
  const [message, setMessage] = useState('');

  const testFirebaseConnection = async () => {
    setLoading(true);
    setMessage('Testing Firebase connection...');
    try {
      console.log('🔍 CourseDebug: Testing Firebase connection...');
      console.log('🔍 CourseDebug: Firebase db instance:', db);

      // Try to read from a simple collection
      const snapshot = await getDocs(collection(db, 'courses'));
      console.log('🔍 CourseDebug: Firebase connection successful');
      console.log('🔍 CourseDebug: Raw snapshot:', snapshot);
      console.log('🔍 CourseDebug: Snapshot docs length:', snapshot.docs.length);
      console.log('🔍 CourseDebug: Snapshot empty:', snapshot.empty);

      setMessage(`✅ Firebase connected! Found ${snapshot.docs.length} documents in courses collection`);
    } catch (error) {
      setMessage(`❌ Firebase connection failed: ${error.message}`);
      console.error('❌ CourseDebug: Firebase connection error:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkCourses = async () => {
    setLoading(true);
    setMessage('Checking courses...');
    try {
      console.log('🔍 CourseDebug: Starting course check...');
      const allCourses = await courseService.getAllCourses();
      console.log('🔍 CourseDebug: Received courses:', allCourses);
      setCourses(allCourses);
      setMessage(`✅ Found ${allCourses.length} courses in Firestore`);
      console.log('📊 CourseDebug: All courses:', allCourses);
    } catch (error) {
      setMessage(`❌ Error checking courses: ${error.message}`);
      console.error('❌ CourseDebug: Error checking courses:', error);
      console.error('❌ CourseDebug: Error stack:', error.stack);
    } finally {
      setLoading(false);
    }
  };

  const createSampleCourses = async () => {
    setLoading(true);
    setMessage('Creating sample courses... (Note: This requires admin permissions or updated Firestore rules)');
    try {
      console.log('🔧 CourseDebug: Starting sample course creation...');

      // Create FlutterFlow course
      const flutterFlowCourse = {
        id: 'flutterflow-masterclass',
        title: 'FlutterFlow No-Code Development Masterclass',
        description: 'Master the art of building beautiful mobile and web applications without writing a single line of code using FlutterFlow.',
        shortDescription: 'Learn to build professional mobile apps with FlutterFlow\'s powerful no-code platform.',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        level: 'beginner',
        category: 'Development',
        price: 49.99,
        originalPrice: 99.99,
        currency: 'USD',
        duration: 480,
        thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        requirements: [
          'Basic understanding of mobile apps',
          'Computer with internet connection',
          'Willingness to learn'
        ],
        learningOutcomes: [
          'Build professional mobile apps with FlutterFlow',
          'Master advanced UI/UX design principles',
          'Integrate APIs and backend services',
          'Deploy apps to app stores'
        ],
        tags: ['FlutterFlow', 'No-Code', 'Mobile Development', 'UI/UX'],
        targetAudience: ['Beginners', 'Entrepreneurs', 'Designers'],
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 1200,
        rating: 4.8,
        reviewCount: 150,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime',
        createdAt: new Date(),
        updatedAt: new Date(),
        modules: []
      };

      // Create AI Coding course
      const aiCodingCourse = {
        id: 'ai-coding-masterclass',
        title: 'AI-Powered Coding with Cursor & VS Code',
        description: 'Learn to code 10x faster using AI tools like Cursor, GitHub Copilot, and ChatGPT. Master the future of software development.',
        shortDescription: 'Master AI-assisted coding with modern tools and techniques.',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        level: 'intermediate',
        category: 'Programming',
        price: 59.99,
        originalPrice: 119.99,
        currency: 'USD',
        duration: 360,
        thumbnail: 'https://images.unsplash.com/photo-**********-aa79dcee981c?w=600&h=400&fit=crop',
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        requirements: [
          'Basic programming knowledge',
          'VS Code or Cursor installed',
          'GitHub account'
        ],
        learningOutcomes: [
          'Master AI-assisted coding techniques',
          'Use Cursor effectively for development',
          'Build projects 10x faster',
          'Integrate AI into your workflow'
        ],
        tags: ['AI', 'Coding', 'Cursor', 'VS Code', 'GitHub Copilot'],
        targetAudience: ['Developers', 'Students', 'Tech Professionals'],
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 850,
        rating: 4.9,
        reviewCount: 95,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime',
        createdAt: new Date(),
        updatedAt: new Date(),
        modules: []
      };

      // Save courses to Firestore
      console.log('🔧 CourseDebug: Attempting to save FlutterFlow course...');
      await setDoc(doc(db, 'courses', flutterFlowCourse.id), flutterFlowCourse);
      console.log('✅ CourseDebug: FlutterFlow course saved');

      console.log('🔧 CourseDebug: Attempting to save AI Coding course...');
      await setDoc(doc(db, 'courses', aiCodingCourse.id), aiCodingCourse);
      console.log('✅ CourseDebug: AI Coding course saved');

      setMessage('✅ Sample courses created successfully!');
      console.log('✅ Created FlutterFlow and AI Coding courses');

      // Refresh course list
      await checkCourses();
    } catch (error) {
      setMessage(`❌ Error creating courses: ${error.message}. You may need to update Firestore rules or use admin authentication.`);
      console.error('❌ Error creating courses:', error);
      console.error('❌ Error details:', error.code, error.message);
    } finally {
      setLoading(false);
    }
  };

  const createTestCourseWithVideo = async () => {
    setLoading(true);
    setMessage('Creating test course with video...');
    try {
      await createTestCourse();
      setMessage('✅ Test course with video created successfully!');
      await checkCourses();
    } catch (error) {
      setMessage(`❌ Error creating test course: ${error.message}`);
      console.error('❌ Error creating test course:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Course Debug Panel</CardTitle>
        <div className="text-sm text-gray-600 space-y-2">
          <p><strong>Instructions:</strong></p>
          <p>1. First, test Firebase connection to ensure everything is working</p>
          <p>2. Check if courses exist in Firestore</p>
          <p>3. If no courses exist, you can create sample courses (requires admin permissions or updated Firestore rules)</p>
          <p>4. Alternatively, use the Admin Panel at <code>/admin</code> to create courses with proper authentication</p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4 flex-wrap">
          <Button onClick={testFirebaseConnection} disabled={loading} variant="secondary">
            Test Firebase Connection
          </Button>
          <Button onClick={checkCourses} disabled={loading}>
            Check Courses in Firestore
          </Button>
          <Button onClick={createSampleCourses} disabled={loading} variant="outline">
            Create Sample Courses
          </Button>
          <Button onClick={createTestCourseWithVideo} disabled={loading} variant="outline">
            Create Test Course with Video
          </Button>
          <Button
            onClick={() => window.open('/admin', '_blank')}
            variant="secondary"
            disabled={loading}
          >
            Open Admin Panel
          </Button>
        </div>
        
        {message && (
          <div className="p-4 bg-gray-100 rounded-lg">
            <p className="text-sm">{message}</p>
          </div>
        )}

        {courses.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Courses in Firestore:</h3>
            {courses.map((course) => (
              <div key={course.id} className="p-3 border rounded-lg">
                <div className="font-medium">{course.title}</div>
                <div className="text-sm text-gray-600">
                  ID: {course.id} | Published: {course.isPublished ? 'Yes' : 'No'} | 
                  Price: ${course.price} | Level: {course.level}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
