import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Navigation } from '../components/Navigation';
import { ComprehensiveCourseForm } from '../components/admin/ComprehensiveCourseForm';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { Button } from '../components/ui/button';

export default function ComprehensiveCourseFormPage() {
  const { courseId } = useParams();
  const navigate = useNavigate();
  const isEditing = Boolean(courseId);

  console.log('🔍 ComprehensiveCourseFormPage - courseId:', courseId, 'isEditing:', isEditing);

  return (
    <div className="min-h-screen bg-gray-900">
      <Navigation />
      
      <div className="pt-16">
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div>
                <Button
                  variant="ghost"
                  onClick={() => navigate('/admin')}
                  className="mb-4 text-gray-300 hover:text-white"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Admin
                </Button>
                <h1 className="text-3xl font-bold text-white">
                  {isEditing ? 'Edit Course' : 'Create New Course'}
                </h1>
                <p className="text-gray-400 mt-2">
                  {isEditing ? 'Update course information and settings' : 'Add a new comprehensive course to your academy'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <Card className="bg-gray-800 border-gray-700 text-white">
              <CardContent className="p-6">
                <ComprehensiveCourseForm
                  isEditing={isEditing}
                  courseId={courseId}
                  onCourseCreated={() => {
                    console.log('Course saved - redirecting to admin');
                    navigate('/admin');
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
