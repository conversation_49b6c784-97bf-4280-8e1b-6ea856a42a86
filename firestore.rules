rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow admins to read all user profiles
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Anyone can read published courses (for browsing)
    match /courses/{courseId} {
      allow read: if true; // Allow public reading for course browsing
      // Allow course creation for testing (TEMPORARY - remove in production)
      allow create: if true;
      // Only admins can update/delete courses
      allow update, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Course modules - same rules as courses
    match /courses/{courseId}/modules/{moduleId} {
      allow read: if true; // Allow public reading
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Course lessons - same rules as courses
    match /courses/{courseId}/modules/{moduleId}/lessons/{lessonId} {
      allow read: if true; // Allow public reading
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Users can read and write their own enrollments
    match /enrollments/{enrollmentId} {
      allow read, write: if request.auth != null &&
        enrollmentId.matches(request.auth.uid + '_.*');
      // Admins can read, write, and delete all enrollments
      allow read, write, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      // Allow admin to create enrollments for any user
      allow create: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Users can read and write their own progress
    match /progress/{progressId} {
      allow read, write: if request.auth != null &&
        progressId.matches(request.auth.uid + '_.*');
    }

    // Users can read and write their own payments
    match /payments/{paymentId} {
      allow read, write: if request.auth != null &&
        resource.data.userId == request.auth.uid;
      // Admins can read all payments
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Users can read and write their own certificates
    match /certificates/{certificateId} {
      allow read, write: if request.auth != null &&
        certificateId.matches(request.auth.uid + '_.*');
      // Anyone can read certificates for verification
      allow read: if resource.data.verified == true;
    }

    // Announcements - everyone can read, only admins can write
    match /announcements/{announcementId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Course Reviews - simple review system
    match /courseReviews/{reviewId} {
      allow read: if true; // Anyone can read reviews
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
      allow update, delete: if request.auth != null &&
        (resource.data.userId == request.auth.uid ||
         (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'));
    }

    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
