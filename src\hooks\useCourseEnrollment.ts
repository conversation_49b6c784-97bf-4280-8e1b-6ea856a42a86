import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface CourseEnrollmentOptions {
  courseType: 'live' | 'recorded' | 'one-on-one';
  courseName: string;
  onAuthRequired?: () => void;
}

export function useCourseEnrollment() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isEnrolling, setIsEnrolling] = useState(false);

  const enrollInCourse = async ({ courseType, courseName, onAuthRequired }: CourseEnrollmentOptions) => {
    // Check if user is authenticated
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to enroll in courses.",
        variant: "destructive",
      });

      // Trigger authentication modal
      if (onAuthRequired) {
        onAuthRequired();
      }
      return false;
    }

    setIsEnrolling(true);

    try {
      // Simulate enrollment process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For now, we'll use the first available course from Firestore
      // In a real app, you'd pass the actual courseId to this function
      const courseId = 'temp-course-id'; // This will be replaced with actual course selection
      const progressKey = `progress_${currentUser.uid}_${courseId}`;

      // Check if already enrolled
      const existingProgress = localStorage.getItem(progressKey);
      if (!existingProgress) {
        // Create new enrollment
        const newProgress = {
          id: `progress_${currentUser.uid}_${courseId}`,
          userId: currentUser.uid,
          courseId,
          enrolledAt: new Date().toISOString(),
          lastAccessedAt: new Date().toISOString(),
          completionPercentage: 0,
          completedLessons: [],
          completedAssignments: [],
          completedQuizzes: [],
          totalWatchTime: 0,
          certificateEarned: false
        };

        localStorage.setItem(progressKey, JSON.stringify(newProgress));

        toast({
          title: "Enrollment Successful!",
          description: `You've been enrolled in ${courseName}. Redirecting to course...`,
        });
      } else {
        toast({
          title: "Welcome Back!",
          description: `Continuing your progress in ${courseName}...`,
        });
      }

      // Redirect to course page
      setTimeout(() => {
        navigate(`/course/${courseId}`);
      }, 1500);

      return true;
    } catch (error) {
      toast({
        title: "Enrollment Error",
        description: "Failed to enroll in course. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsEnrolling(false);
    }
  };

  const handleStartLearning = (onAuthRequired?: () => void) => {
    return enrollInCourse({
      courseType: 'live',
      courseName: 'FlutterFlow Live Classes',
      onAuthRequired
    });
  };

  const handleAccessCourses = (onAuthRequired?: () => void) => {
    if (!currentUser) {
      // If not authenticated, show auth modal
      if (onAuthRequired) {
        onAuthRequired();
      } else {
        // Fallback: scroll to courses section
        const coursesSection = document.getElementById('courses-section');
        coursesSection?.scrollIntoView({ behavior: 'smooth' });
      }
      return;
    }

    // If authenticated, redirect to dashboard or enroll in a course
    // For "Access Recorded Courses", enroll in the recorded course
    enrollInCourse({
      courseType: 'recorded',
      courseName: 'AI Integration Masterclass',
      onAuthRequired
    });
  };

  const handleViewFreePreview = () => {
    // Free preview doesn't require authentication
    window.open('https://www.youtube.com/watch?v=dQw4w9WgXcQ', '_blank');
  };

  return {
    enrollInCourse,
    handleStartLearning,
    handleAccessCourses,
    handleViewFreePreview,
    isEnrolling,
    isAuthenticated: !!currentUser
  };
}
