/**
 * FreeCodeLap Platform - Simple Email Service
 * 
 * A simple email service that opens the user's default email client
 * This works immediately without any configuration
 */

import { UserProfile } from '@/types/user';
import { realEmailService } from './realEmailService';

export interface SimpleEmailResult {
  success: boolean;
  sentCount: number;
  failedCount: number;
  errors: string[];
  emailId: string;
}

export class SimpleEmailService {
  private static instance: SimpleEmailService;

  public static getInstance(): SimpleEmailService {
    if (!SimpleEmailService.instance) {
      SimpleEmailService.instance = new SimpleEmailService();
    }
    return SimpleEmailService.instance;
  }

  /**
   * Send emails by opening the user's email client
   */
  async sendEmailsViaClient(
    users: UserProfile[],
    subject: string,
    content: string,
    sentBy: string,
    sentByName: string
  ): Promise<SimpleEmailResult> {
    try {
      console.log(`📧 Preparing to send emails to ${users.length} recipients via email client...`);

      if (users.length === 0) {
        return {
          success: false,
          sentCount: 0,
          failedCount: 0,
          errors: ['No users provided'],
          emailId: 'no-users'
        };
      }

      // For single recipient, open email client directly
      if (users.length === 1) {
        const user = users[0];
        const mailtoLink = this.createMailtoLink(user.email, subject, content);
        
        try {
          window.open(mailtoLink, '_blank');
          console.log(`📧 Opened email client for: ${user.email}`);
          
          // Save to sent emails history
          const emailId = await this.saveToHistory(users, subject, content, sentBy, sentByName, 1, 0);
          
          return {
            success: true,
            sentCount: 1,
            failedCount: 0,
            errors: [],
            emailId
          };
        } catch (error) {
          console.error('❌ Error opening email client:', error);
          return {
            success: false,
            sentCount: 0,
            failedCount: 1,
            errors: [`Failed to open email client: ${error}`],
            emailId: 'failed'
          };
        }
      }

      // For multiple recipients, create a combined email
      const emailList = users.map(user => user.email).join(', ');
      const mailtoLink = this.createMailtoLink(emailList, subject, content);
      
      try {
        window.open(mailtoLink, '_blank');
        console.log(`📧 Opened email client for ${users.length} recipients`);
        
        // Save to sent emails history
        const emailId = await this.saveToHistory(users, subject, content, sentBy, sentByName, users.length, 0);
        
        return {
          success: true,
          sentCount: users.length,
          failedCount: 0,
          errors: [],
          emailId
        };
      } catch (error) {
        console.error('❌ Error opening email client:', error);
        return {
          success: false,
          sentCount: 0,
          failedCount: users.length,
          errors: [`Failed to open email client: ${error}`],
          emailId: 'failed'
        };
      }
    } catch (error) {
      console.error('❌ Error in email client service:', error);
      return {
        success: false,
        sentCount: 0,
        failedCount: users.length,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        emailId: 'error'
      };
    }
  }

  /**
   * Create a mailto link
   */
  private createMailtoLink(to: string, subject: string, body: string): string {
    const params = new URLSearchParams({
      subject: subject,
      body: body
    });
    
    return `mailto:${to}?${params.toString()}`;
  }

  /**
   * Save email to history using the real email service
   */
  private async saveToHistory(
    users: UserProfile[],
    subject: string,
    content: string,
    sentBy: string,
    sentByName: string,
    sentCount: number,
    failedCount: number
  ): Promise<string> {
    try {
      // Use the real email service to save to Firestore
      const result = await realEmailService.sendBulkEmails(
        users,
        subject,
        content,
        content, // Use same content for HTML
        sentBy,
        sentByName,
        'email_client'
      );
      
      return result.emailId;
    } catch (error) {
      console.error('❌ Error saving email to history:', error);
      return `manual_${Date.now()}`;
    }
  }

  /**
   * Send individual emails (opens multiple email client windows)
   */
  async sendIndividualEmails(
    users: UserProfile[],
    subject: string,
    content: string,
    sentBy: string,
    sentByName: string
  ): Promise<SimpleEmailResult> {
    try {
      console.log(`📧 Preparing to send individual emails to ${users.length} recipients...`);

      if (users.length === 0) {
        return {
          success: false,
          sentCount: 0,
          failedCount: 0,
          errors: ['No users provided'],
          emailId: 'no-users'
        };
      }

      let sentCount = 0;
      let failedCount = 0;
      const errors: string[] = [];

      // Ask user for confirmation for multiple emails
      if (users.length > 1) {
        const confirmed = confirm(
          `This will open ${users.length} email client windows. Continue?`
        );
        if (!confirmed) {
          return {
            success: false,
            sentCount: 0,
            failedCount: 0,
            errors: ['User cancelled operation'],
            emailId: 'cancelled'
          };
        }
      }

      // Open email client for each user with a delay
      for (let i = 0; i < users.length; i++) {
        const user = users[i];
        
        try {
          // Replace template variables in content for each user
          const personalizedContent = this.personalizeContent(content, user);
          const personalizedSubject = this.personalizeContent(subject, user);
          
          const mailtoLink = this.createMailtoLink(
            user.email, 
            personalizedSubject, 
            personalizedContent
          );
          
          window.open(mailtoLink, '_blank');
          sentCount++;
          console.log(`📧 Opened email client for: ${user.email}`);
          
          // Add delay between emails to avoid overwhelming the browser
          if (i < users.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          failedCount++;
          errors.push(`Failed to open email for ${user.email}: ${error}`);
          console.error(`❌ Error opening email for ${user.email}:`, error);
        }
      }

      // Save to sent emails history
      const emailId = await this.saveToHistory(users, subject, content, sentBy, sentByName, sentCount, failedCount);

      return {
        success: sentCount > 0,
        sentCount,
        failedCount,
        errors,
        emailId
      };
    } catch (error) {
      console.error('❌ Error in individual email sending:', error);
      return {
        success: false,
        sentCount: 0,
        failedCount: users.length,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        emailId: 'error'
      };
    }
  }

  /**
   * Personalize content with user variables
   */
  private personalizeContent(content: string, user: UserProfile): string {
    return content
      .replace(/{{name}}/g, user.displayName || user.firstName || 'User')
      .replace(/{{email}}/g, user.email)
      .replace(/{{firstName}}/g, user.firstName || '')
      .replace(/{{lastName}}/g, user.lastName || '')
      .replace(/{{role}}/g, user.role)
      .replace(/{{platformUrl}}/g, window.location.origin);
  }
}

export const simpleEmailService = SimpleEmailService.getInstance();
