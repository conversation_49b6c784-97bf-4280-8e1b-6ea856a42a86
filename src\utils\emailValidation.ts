/**
 * Simple and Reliable Email Validation Utility
 */

// Simple but effective email regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Simple email validation that actually works
 * @param email - Email address to validate
 * @returns boolean - true if valid, false if invalid
 */
export const isValidEmail = (email: string): boolean => {
  // Basic checks
  if (!email || typeof email !== 'string') {
    return false;
  }

  const trimmedEmail = email.trim();

  if (trimmedEmail === '') {
    return false;
  }

  // Simple regex test
  const isValid = EMAIL_REGEX.test(trimmedEmail);

  console.log('📧 Email validation:', trimmedEmail, '→', isValid ? '✅ Valid' : '❌ Invalid');

  return isValid;
};

/**
 * Normalizes email address (trims and converts to lowercase)
 */
export const normalizeEmail = (email: string): string => {
  if (!email || typeof email !== 'string') {
    return '';
  }
  return email.trim().toLowerCase();
};

/**
 * Gets email validation error message
 */
export const getEmailValidationError = (email: string): string => {
  if (!email || email.trim() === '') {
    return 'Email address is required';
  }

  if (!isValidEmail(email)) {
    return 'Please enter a valid email address';
  }

  return '';
};

// Export default validation function
export default isValidEmail;
