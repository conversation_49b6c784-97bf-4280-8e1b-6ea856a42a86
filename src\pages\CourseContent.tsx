import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useCourse } from '@/contexts/CourseContext';
import { enrollmentService } from '@/services/enrollmentService';
import { debugEnrollmentAccess } from '@/utils/debugEnrollmentAccess';
import { testEnrollmentAccess } from '@/utils/fixEnrollmentAccess';
import { SimpleVideoPlayer } from '@/components/course/SimpleVideoPlayer';
import {
  ArrowLeft,
  PlayCircle,
  Lock,
  ChevronDown,
  ChevronUp,
  Menu,
  X,
  Star,
  Clock,
  Users,
  CheckCircle,
  Gift,
  Crown,
  Shield,
  Eye,
  Zap
} from 'lucide-react';

interface ModuleSidebarProps {
  modules: any[];
  currentLessonId: string | null;
  onLessonClick: (lesson: any, moduleIndex: number, lessonIndex: number) => void;
  isEnrolled: boolean;
}

const ModuleSidebar: React.FC<ModuleSidebarProps> = ({
  modules,
  currentLessonId,
  onLessonClick,
  isEnrolled
}) => {
  const [expandedModules, setExpandedModules] = useState<Set<number>>(new Set());

  // Expand the module containing the current lesson
  useEffect(() => {
    if (currentLessonId && modules.length > 0) {
      // Find which module contains the current lesson
      const moduleIndex = modules.findIndex(module =>
        module?.lessons?.some((lesson: any) => lesson?.id === currentLessonId)
      );

      if (moduleIndex !== -1) {
        console.log('🎯 Expanding module', moduleIndex, 'for lesson', currentLessonId);
        setExpandedModules(new Set([moduleIndex]));
      }
    }
  }, [currentLessonId, modules]);

  const toggleModule = (moduleIndex: number) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleIndex)) {
      newExpanded.delete(moduleIndex);
    } else {
      newExpanded.add(moduleIndex);
    }
    setExpandedModules(newExpanded);
  };

  return (
    <div className="h-full bg-gray-800 border-r border-gray-700">
      <div className="p-4 border-b border-gray-700">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <Shield className="w-5 h-5 mr-2 text-blue-400" />
          Course Content
        </h2>

        {/* Enrollment Status */}
        {isEnrolled ? (
          <div className="mt-3 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded-lg">
            <div className="flex items-center text-green-400">
              <Crown className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">Full Access</span>
            </div>
            <p className="text-xs text-green-300 mt-1">
              You have access to all course content
            </p>
          </div>
        ) : (
          <div className="mt-3 p-3 bg-gray-700 bg-opacity-50 border border-gray-600 rounded-lg">
            <div className="flex items-center text-gray-300">
              <Lock className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">Course Locked</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              Purchase course to unlock all lessons
            </p>
          </div>
        )}

        {/* Debug and Refresh Buttons */}
        {currentUser && courseId && (
          <div className="mt-3 flex gap-2 flex-wrap">
            <Button
              onClick={checkEnrollment}
              variant="outline"
              size="sm"
              className="text-xs"
              disabled={checkingEnrollment}
            >
              {checkingEnrollment ? '🔄 Checking...' : '🔄 Refresh Access'}
            </Button>

            {!isEnrolled && (
              <Button
                onClick={async () => {
                  await testEnrollmentAccess(currentUser.uid, courseId);
                  // Refresh enrollment status after potential fix
                  setTimeout(() => checkEnrollment(), 1000);
                }}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                🔧 Fix Access
              </Button>
            )}

            {/* Debug Button (Development Only) */}
            {import.meta.env.DEV && (
              <Button
                onClick={() => debugEnrollmentAccess(currentUser.uid, courseId)}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                🔍 Debug
              </Button>
            )}
          </div>
        )}
      </div>
      
      <div className="overflow-y-auto h-full pb-20">
        {modules?.map((module, moduleIndex) => {
          const totalLessons = module?.lessons?.length || 0;
          const freeLessons = module?.lessons?.filter((lesson: any) => lesson?.isFree)?.length || 0;
          const lockedLessons = totalLessons - freeLessons;

          return (
            <div key={module?.id || moduleIndex} className="border-b border-gray-700">
              <button
                type="button"
                onClick={() => toggleModule(moduleIndex)}
                className="w-full p-4 text-left hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-white text-sm">
                      Module {moduleIndex + 1}: {module?.title || 'Module Title'}
                    </h3>

                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-400">
                      <div className="flex items-center">
                        <PlayCircle className="w-3 h-3 mr-1" />
                        {totalLessons} lessons
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {module?.duration || '30 min'}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    {expandedModules.has(moduleIndex) ? (
                      <ChevronUp className="w-4 h-4 text-blue-400" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-blue-400" />
                    )}
                  </div>
                </div>
              </button>

              {expandedModules.has(moduleIndex) && (
                <div className="bg-gray-900">
                  {module?.lessons?.map((lesson: any, lessonIndex: number) => {
                    const isFree = lesson?.isFree || false;
                    const isAccessible = isFree || isEnrolled;
                    const isActive = lesson?.id === currentLessonId;
                    const isCompleted = lesson?.isCompleted || false;

                    return (
                      <div
                        key={lesson?.id || lessonIndex}
                        className={`border-l-4 ${
                          isActive
                            ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                            : isAccessible
                              ? 'border-transparent hover:border-gray-600'
                              : 'border-yellow-500 border-opacity-50'
                        }`}
                      >
                        <button
                          type="button"
                          onClick={() => onLessonClick(lesson, moduleIndex, lessonIndex)}
                          className={`w-full p-4 text-left text-sm transition-colors flex items-center ${
                            isActive
                              ? 'text-white'
                              : isAccessible
                                ? 'hover:bg-gray-700 text-gray-300'
                                : 'text-gray-500 hover:bg-gray-800 cursor-pointer'
                          }`}
                        >
                          <div className="mr-3 flex-shrink-0">
                            {isCompleted ? (
                              <CheckCircle className="w-5 h-5 text-green-400" />
                            ) : isAccessible ? (
                              <PlayCircle className="w-5 h-5 text-blue-400" />
                            ) : (
                              <Lock className="w-5 h-5 text-gray-400" />
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="font-medium">
                              {lesson?.title || `Lesson ${lessonIndex + 1}`}
                            </div>

                            <div className="flex items-center gap-3 mt-2 text-xs opacity-75">
                              <div className="flex items-center">
                                <Clock className="w-3 h-3 mr-1" />
                                {lesson?.duration || '5 min'}
                              </div>

                              {lesson?.type && (
                                <div className="flex items-center">
                                  {lesson.type === 'video' && <PlayCircle className="w-3 h-3 mr-1" />}
                                  {lesson.type === 'text' && <BookOpen className="w-3 h-3 mr-1" />}
                                  {lesson.type === 'interactive' && <Zap className="w-3 h-3 mr-1" />}
                                  <span className="capitalize">{lesson.type}</span>
                                </div>
                              )}

                              {isCompleted && (
                                <div className="flex items-center text-green-400">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Completed
                                </div>
                              )}
                            </div>

                          </div>
                        </button>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default function CourseContent() {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { currentCourse, loadCourse, loading } = useCourse();
  
  const [currentLesson, setCurrentLesson] = useState<any>(null);
  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [checkingEnrollment, setCheckingEnrollment] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    if (courseId) {
      loadCourse(courseId);
      checkEnrollment();
    }
  }, [courseId, currentUser]);

  useEffect(() => {
    // Check if we have a specific lesson to navigate to from CourseDetail
    const navigationState = location.state as any;

    if (currentCourse?.modules?.length > 0 && !currentLesson) {
      // If we have specific lesson data from navigation
      if (navigationState?.lesson && navigationState?.moduleIndex !== undefined && navigationState?.lessonIndex !== undefined) {
        console.log('🎯 Navigating to specific lesson:', navigationState.lesson.title);
        setCurrentLesson(navigationState.lesson);
        setCurrentModuleIndex(navigationState.moduleIndex);
        setCurrentLessonIndex(navigationState.lessonIndex);
        return;
      }

      // Otherwise, auto-select first accessible lesson when course loads
      const firstModule = currentCourse.modules[0];
      if (firstModule?.lessons?.length > 0) {
        const firstLesson = firstModule.lessons[0];
        if (firstLesson?.isFree || isEnrolled) {
          console.log('🎯 Auto-selecting first lesson:', firstLesson.title);
          setCurrentLesson(firstLesson);
          setCurrentModuleIndex(0);
          setCurrentLessonIndex(0);
        }
      }
    }
  }, [currentCourse, isEnrolled, location.state]);

  const checkEnrollment = async () => {
    if (!currentUser || !courseId) {
      setCheckingEnrollment(false);
      return;
    }

    try {
      setCheckingEnrollment(true);
      console.log('🔍 CourseContent: Checking enrollment for user:', currentUser.uid, 'course:', courseId);

      // First check Firestore for actual enrollment (handles both paid and manual enrollments)
      const firestoreEnrolled = await enrollmentService.isUserEnrolled(currentUser.uid, courseId);
      console.log('📊 CourseContent: Firestore enrollment check result:', firestoreEnrolled);

      if (firestoreEnrolled) {
        console.log('✅ CourseContent: User is enrolled via Firestore');
        setIsEnrolled(true);
        setCheckingEnrollment(false);
        return;
      }

      // Fallback: Check localStorage for payment completion (for immediate access after payment)
      const progressKey = `progress_${currentUser.uid}_${courseId}`;
      const progressData = localStorage.getItem(progressKey);
      console.log('🔍 CourseContent: Checking localStorage for key:', progressKey);

      if (progressData) {
        try {
          const progress = JSON.parse(progressData);
          console.log('📊 CourseContent: Found localStorage data:', progress);

          // Only consider enrolled if there's an enrolledAt timestamp (indicating payment completion)
          const hasValidEnrollment = progress.enrolledAt &&
            progress.userId === currentUser.uid &&
            progress.courseId === courseId;

          console.log('📊 CourseContent: localStorage enrollment valid:', hasValidEnrollment);
          setIsEnrolled(hasValidEnrollment);
        } catch (error) {
          console.error('❌ CourseContent: Error parsing progress data:', error);
          setIsEnrolled(false);
        }
      } else {
        console.log('❌ CourseContent: No localStorage data found');
        setIsEnrolled(false);
      }
    } catch (error) {
      console.error('❌ CourseContent: Error checking enrollment:', error);
      setIsEnrolled(false);
    } finally {
      setCheckingEnrollment(false);
    }
  };

  const handleLessonClick = (lesson: any, moduleIndex: number, lessonIndex: number) => {
    const isFree = lesson?.isFree || false;
    const isAccessible = isFree || isEnrolled;

    if (!isAccessible) {
      // Show upgrade prompt for locked lessons
      handleLockedLessonClick(lesson);
      return;
    }

    setCurrentLesson(lesson);
    setCurrentModuleIndex(moduleIndex);
    setCurrentLessonIndex(lessonIndex);
    setSidebarOpen(false); // Close sidebar on mobile after selection
  };

  const handleLockedLessonClick = (lesson: any) => {
    const confirmed = confirm(
      `"${lesson.title}" is a premium lesson. Would you like to purchase the full course to unlock all content?`
    );

    if (confirmed) {
      navigate(`/course/${courseId}`);
    }
  };

  const handleBackToCourse = () => {
    navigate(`/course/${courseId}`);
  };

  const getNextLesson = () => {
    if (!currentCourse?.modules) return null;

    const currentModule = currentCourse.modules[currentModuleIndex];
    if (!currentModule?.lessons) return null;

    // Check if there's a next lesson in current module
    if (currentLessonIndex < currentModule.lessons.length - 1) {
      return {
        lesson: currentModule.lessons[currentLessonIndex + 1],
        moduleIndex: currentModuleIndex,
        lessonIndex: currentLessonIndex + 1
      };
    }

    // Check if there's a next module
    if (currentModuleIndex < currentCourse.modules.length - 1) {
      const nextModule = currentCourse.modules[currentModuleIndex + 1];
      if (nextModule?.lessons?.length > 0) {
        return {
          lesson: nextModule.lessons[0],
          moduleIndex: currentModuleIndex + 1,
          lessonIndex: 0
        };
      }
    }

    return null;
  };

  const getPreviousLesson = () => {
    if (!currentCourse?.modules) return null;

    // Check if there's a previous lesson in current module
    if (currentLessonIndex > 0) {
      const currentModule = currentCourse.modules[currentModuleIndex];
      return {
        lesson: currentModule.lessons[currentLessonIndex - 1],
        moduleIndex: currentModuleIndex,
        lessonIndex: currentLessonIndex - 1
      };
    }

    // Check if there's a previous module
    if (currentModuleIndex > 0) {
      const previousModule = currentCourse.modules[currentModuleIndex - 1];
      if (previousModule?.lessons?.length > 0) {
        return {
          lesson: previousModule.lessons[previousModule.lessons.length - 1],
          moduleIndex: currentModuleIndex - 1,
          lessonIndex: previousModule.lessons.length - 1
        };
      }
    }

    return null;
  };

  const handleNextLesson = () => {
    const next = getNextLesson();
    if (next) {
      handleLessonClick(next.lesson, next.moduleIndex, next.lessonIndex);
    }
  };

  const handlePreviousLesson = () => {
    const previous = getPreviousLesson();
    if (previous) {
      handleLessonClick(previous.lesson, previous.moduleIndex, previous.lessonIndex);
    }
  };

  if (loading || checkingEnrollment) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">
          {loading ? 'Loading course content...' : 'Checking enrollment status...'}
        </div>
      </div>
    );
  }

  if (!currentCourse) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">Course not found</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navigation />
      
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 pt-16">
        <div className="px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToCourse}
              className="text-gray-300 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Course
            </Button>
            
            <div className="hidden md:block">
              <h1 className="text-lg font-semibold text-white">{currentCourse.title}</h1>
              {currentLesson && (
                <p className="text-sm text-gray-400">{currentLesson.title}</p>
              )}
              {!isEnrolled && (
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="text-xs text-yellow-400 border-yellow-400">
                    Preview Mode - Free Lessons Only
                  </Badge>
                </div>
              )}
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="md:hidden text-gray-300 hover:text-white"
          >
            {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* Sidebar - Desktop */}
        <div className="hidden md:block w-80 flex-shrink-0">
          <ModuleSidebar
            modules={currentCourse.modules || []}
            currentLessonId={currentLesson?.id}
            onLessonClick={handleLessonClick}
            isEnrolled={isEnrolled}
          />
        </div>

        {/* Sidebar - Mobile Overlay */}
        {sidebarOpen && (
          <div className="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
            <div className="w-80 h-full">
              <ModuleSidebar
                modules={currentCourse.modules || []}
                currentLessonId={currentLesson?.id}
                onLessonClick={handleLessonClick}
                isEnrolled={isEnrolled}
              />
            </div>
          </div>
        )}

        {/* Video Player Area */}
        <div className="flex-1 bg-black">
          {currentLesson?.videoUrl ? (
            <SimpleVideoPlayer
              videoUrl={currentLesson.videoUrl}
              title={currentLesson.title}
              onProgress={(progress) => {
                // Handle progress tracking
                console.log('Video progress:', progress);
              }}
              onComplete={() => {
                // Handle lesson completion and auto-advance to next lesson
                console.log('Lesson completed:', currentLesson.title);
                const next = getNextLesson();
                if (next) {
                  setTimeout(() => {
                    handleLessonClick(next.lesson, next.moduleIndex, next.lessonIndex);
                  }, 2000); // Auto-advance after 2 seconds
                }
              }}
              onNext={handleNextLesson}
              onPrevious={handlePreviousLesson}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-400 p-8">
              <div className="text-center max-w-2xl">
                {currentLesson ? (
                  // Lesson selected but no video
                  <div>
                    <PlayCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg mb-4">No video available for this lesson</p>
                  </div>
                ) : !isEnrolled ? (
                  // Not enrolled - show upgrade prompt
                  <div className="bg-gradient-to-br from-blue-900 to-purple-900 border border-blue-600 rounded-xl p-8">
                    <div className="flex items-center justify-center mb-6">
                      <Crown className="w-16 h-16 text-yellow-400" />
                    </div>

                    <h3 className="text-3xl font-bold text-white mb-4">
                      Unlock Full Course Access
                    </h3>

                    <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                      You're currently in preview mode with access to free lessons only.
                      Purchase the full course to unlock all premium content, assignments,
                      quizzes, and certificates.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div className="bg-green-900 bg-opacity-30 border border-green-600 rounded-lg p-6">
                        <div className="flex items-center text-green-400 mb-4">
                          <Gift className="w-6 h-6 mr-3" />
                          <span className="font-semibold text-lg">Free Preview</span>
                        </div>
                        <ul className="text-green-300 space-y-2">
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Limited free lessons
                          </li>
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Basic video content
                          </li>
                          <li className="flex items-center text-gray-400">
                            <X className="w-4 h-4 mr-2" />
                            No certificates
                          </li>
                          <li className="flex items-center text-gray-400">
                            <X className="w-4 h-4 mr-2" />
                            No assignments
                          </li>
                        </ul>
                      </div>

                      <div className="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-6">
                        <div className="flex items-center text-blue-400 mb-4">
                          <Crown className="w-6 h-6 mr-3" />
                          <span className="font-semibold text-lg">Full Access</span>
                        </div>
                        <ul className="text-blue-300 space-y-2">
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            All premium lessons
                          </li>
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Assignments & quizzes
                          </li>
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Course certificates
                          </li>
                          <li className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Lifetime access
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button
                        onClick={() => navigate(`/course/${courseId}`)}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-1 py-3 text-lg"
                      >
                        <Crown className="w-5 h-5 mr-2" />
                        Purchase Full Course
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => {
                          // Find first free lesson and navigate to it
                          const firstFreeLesson = currentCourse?.modules
                            ?.flatMap(m => m.lessons || [])
                            ?.find(l => l.isFree);
                          if (firstFreeLesson) {
                            const moduleIndex = currentCourse.modules.findIndex(m =>
                              m.lessons?.some(l => l.id === firstFreeLesson.id)
                            );
                            const lessonIndex = currentCourse.modules[moduleIndex]?.lessons?.findIndex(l =>
                              l.id === firstFreeLesson.id
                            );
                            handleLessonClick(firstFreeLesson, moduleIndex, lessonIndex);
                          }
                        }}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700 py-3 text-lg"
                      >
                        <Eye className="w-5 h-5 mr-2" />
                        Try Free Lessons
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Enrolled but no lesson selected
                  <div>
                    <Shield className="w-16 h-16 mx-auto mb-4 text-blue-400" />
                    <h3 className="text-2xl font-semibold text-white mb-4">Welcome to Your Course!</h3>
                    <p className="text-gray-300 mb-6 text-lg">
                      Select a lesson from the sidebar to start learning
                    </p>
                    <Button
                      onClick={() => {
                        // Navigate to first lesson
                        const firstLesson = currentCourse?.modules?.[0]?.lessons?.[0];
                        if (firstLesson) {
                          handleLessonClick(firstLesson, 0, 0);
                        }
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg"
                    >
                      <PlayCircle className="w-5 h-5 mr-2" />
                      Start First Lesson
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
