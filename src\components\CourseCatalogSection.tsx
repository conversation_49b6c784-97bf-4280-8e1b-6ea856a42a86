import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { courseService } from '@/services/courseService';
import { Course } from '@/types/course';
import { coursesService } from '@/services/firestoreService';
import { useAuth } from '@/contexts/AuthContext';
import {
  BookOpen,
  Clock,
  Users,
  Star,
  PlayCircle,
  Award,
  CheckCircle
} from 'lucide-react';

const CourseCatalogSection = () => {
  const { currentUser } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    console.log('🎯 Setting up real-time course listener...');

    // Set loading timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('⏰ Course loading timeout - falling back to manual load');
        setLoading(false);
        loadFallbackCourses();
      }
    }, 10000); // 10 second timeout

    // Set up real-time listener for courses with error handling
    try {
      const unsubscribe = coursesService.subscribe((coursesData) => {
        console.log('🔄 Real-time update: Courses changed in Firestore');
        console.log('📊 New courses data:', coursesData);
        console.log('📊 Number of courses received:', coursesData.length);
        console.log('📊 Course IDs:', coursesData.map(c => c.id));
        clearTimeout(loadingTimeout);
        setIsConnected(true);

        if (coursesData.length === 0) {
          console.log('❌ No courses found in real-time update - loading fallback courses');
          loadFallbackCourses();
          setLoading(false);
          return;
        }

      // Process courses with proper typing and defaults
      const processedCourses: Course[] = coursesData.map((courseData: any) => {
        console.log('📄 Processing real-time course:', courseData.id, courseData);

        return {
          id: courseData.id,
          title: courseData.title || 'Untitled Course',
          description: courseData.description || 'No description available',
          instructor: courseData.instructor || 'Ahmed Takal',
          instructorId: courseData.instructorId || 'ahmed-takal',
          level: (courseData.level || 'beginner') as 'beginner' | 'intermediate' | 'advanced',
          price: Number(courseData.price) || 3500,
          currency: courseData.currency || 'KES',
          duration: Number(courseData.duration) || 480,
          thumbnail: courseData.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
          requirements: Array.isArray(courseData.requirements) ? courseData.requirements : [
            'Basic understanding of mobile apps',
            'Computer with internet connection'
          ],
          learningOutcomes: Array.isArray(courseData.learningOutcomes) ? courseData.learningOutcomes : [
            'Build professional mobile apps with FlutterFlow',
            'Master UI/UX design principles',
            'Learn app development'
          ],
          tags: Array.isArray(courseData.tags) ? courseData.tags : ['FlutterFlow', 'Mobile Development'],
          isPublished: courseData.isPublished !== false, // Show unless explicitly false
          enrollmentCount: Number(courseData.enrollmentCount) || 1200,
          rating: Number(courseData.rating) || 4.8,
          reviewCount: Number(courseData.reviewCount) || 150,
          modules: [], // Will be loaded separately when needed
          createdAt: courseData.createdAt?.toDate ? courseData.createdAt.toDate() : new Date(),
          updatedAt: courseData.updatedAt?.toDate ? courseData.updatedAt.toDate() : new Date(),
        } as Course;
      });

      console.log('✅ Real-time processed courses:', processedCourses);
      console.log('🎉 Auto-updating courses state with', processedCourses.length, 'courses');
      setCourses(processedCourses);
      setLoading(false);
    });

      // Cleanup listener on unmount
      return () => {
        console.log('🧹 Cleaning up real-time course listener');
        clearTimeout(loadingTimeout);
        unsubscribe();
      };
    } catch (error) {
      console.error('❌ Error setting up course listener:', error);
      clearTimeout(loadingTimeout);
      loadFallbackCourses();
      setLoading(false);
      setIsConnected(false);
    }
  }, []);

  // Fallback course loading function
  const loadFallbackCourses = () => {
    console.log('📚 Loading fallback courses...');
    const fallbackCourses: Course[] = [
      {
        id: 'fallback-1',
        title: 'FlutterFlow Masterclass - Build Apps Without Code',
        description: 'Learn to build professional mobile apps using FlutterFlow\'s powerful no-code platform. Perfect for beginners and experienced developers.',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        level: 'beginner' as const,
        price: 3500,
        currency: 'KES',
        duration: 480,
        thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
        requirements: [
          'Basic understanding of mobile apps',
          'Computer with internet connection',
          'Willingness to learn'
        ],
        learningOutcomes: [
          'Build professional mobile apps with FlutterFlow',
          'Master UI/UX design principles',
          'Learn app development without coding',
          'Deploy apps to app stores'
        ],
        tags: ['FlutterFlow', 'Mobile Development', 'No-Code', 'UI/UX'],
        isPublished: true,
        enrollmentCount: 1200,
        rating: 4.8,
        reviewCount: 150,
        modules: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'fallback-2',
        title: 'AI Integration in FlutterFlow Apps',
        description: 'Advanced course on integrating AI features into your FlutterFlow applications. Learn to build smart, AI-powered mobile apps.',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        level: 'intermediate' as const,
        price: 4500,
        currency: 'KES',
        duration: 360,
        thumbnail: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop',
        requirements: [
          'Basic FlutterFlow knowledge',
          'Understanding of APIs',
          'Interest in AI technology'
        ],
        learningOutcomes: [
          'Integrate AI APIs into FlutterFlow apps',
          'Build chatbots and AI assistants',
          'Implement machine learning features',
          'Create intelligent user experiences'
        ],
        tags: ['AI', 'FlutterFlow', 'Machine Learning', 'APIs'],
        isPublished: true,
        enrollmentCount: 850,
        rating: 4.9,
        reviewCount: 95,
        modules: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    ];

    setCourses(fallbackCourses);
    console.log('✅ Fallback courses loaded:', fallbackCourses.length);
  };

  // Manual refresh function (only used by create test course)
  const refreshCourses = async () => {
    try {
      console.log('🔄 Manual refresh triggered...');
      setLoading(true);

      // Load all courses from your Firestore collections
      const allCourses = await coursesService.getAll();
      console.log('📊 Manual refresh: Found', allCourses.length, 'courses');

      if (allCourses.length === 0) {
        console.log('❌ No courses found in manual refresh');
        setCourses([]);
        return;
      }

      // Process courses with proper typing and defaults
      const processedCourses: Course[] = allCourses.map((courseData: any) => {
        return {
          id: courseData.id,
          title: courseData.title || 'Untitled Course',
          description: courseData.description || 'No description available',
          instructor: courseData.instructor || 'Ahmed Takal',
          instructorId: courseData.instructorId || 'ahmed-takal',
          level: (courseData.level || 'beginner') as 'beginner' | 'intermediate' | 'advanced',
          price: Number(courseData.price) || 3500,
          currency: courseData.currency || 'KES',
          duration: Number(courseData.duration) || 480,
          thumbnail: courseData.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
          requirements: Array.isArray(courseData.requirements) ? courseData.requirements : [
            'Basic understanding of mobile apps',
            'Computer with internet connection'
          ],
          learningOutcomes: Array.isArray(courseData.learningOutcomes) ? courseData.learningOutcomes : [
            'Build professional mobile apps with FlutterFlow',
            'Master UI/UX design principles',
            'Learn app development'
          ],
          tags: Array.isArray(courseData.tags) ? courseData.tags : ['FlutterFlow', 'Mobile Development'],
          isPublished: courseData.isPublished !== false,
          enrollmentCount: Number(courseData.enrollmentCount) || 1200,
          rating: Number(courseData.rating) || 4.8,
          reviewCount: Number(courseData.reviewCount) || 150,
          modules: [],
          createdAt: courseData.createdAt?.toDate ? courseData.createdAt.toDate() : new Date(),
          updatedAt: courseData.updatedAt?.toDate ? courseData.updatedAt.toDate() : new Date(),
        } as Course;
      });

      console.log('✅ Manual refresh complete:', processedCourses.length, 'courses');
      setCourses(processedCourses);

    } catch (error) {
      console.error('❌ Error in manual refresh:', error);
      setCourses([]);
    } finally {
      setLoading(false);
    }
  };

  const createTestCourse = async () => {
    setCreating(true);
    try {
      console.log('📝 Creating test course using dynamic Firestore service...');

      const testCourse = {
        title: 'FlutterFlow Mastery Course',
        description: 'Learn to build professional mobile apps with FlutterFlow without coding. Master UI/UX design, API integration, and app deployment.',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        level: 'beginner',
        price: 3500,
        currency: 'KES',
        duration: 480,
        thumbnail: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop',
        requirements: [
          'Basic understanding of mobile apps',
          'Computer with internet connection',
          'Willingness to learn'
        ],
        learningOutcomes: [
          'Build professional mobile apps with FlutterFlow',
          'Master advanced UI/UX design principles',
          'Integrate APIs and backend services',
          'Deploy apps to app stores'
        ],
        tags: ['FlutterFlow', 'No-Code', 'Mobile Development'],
        isPublished: true,
        enrollmentCount: 1200,
        rating: 4.8,
        reviewCount: 150
      };

      const courseId = await coursesService.create(testCourse);
      console.log('✅ Test course created with ID:', courseId);
      console.log('🔄 Course will appear automatically via real-time listener');
      console.log('📝 Created course data:', testCourse);

      // Add a small delay to ensure Firestore has processed the write
      setTimeout(() => {
        console.log('⏰ Checking if course appeared in UI after 2 seconds...');
        console.log('📊 Current courses in state:', courses.length);
      }, 2000);

    } catch (error) {
      console.error('❌ Error creating test course:', error);
    } finally {
      setCreating(false);
    }
  };

  const handleViewCourse = (courseId: string) => {
    // Navigate to course detail page
    window.location.href = `/course/${courseId}`;
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    return `${hours}+ hours`;
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <section id="courses-section" className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center space-x-2 bg-blue-900/50 text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4 border border-blue-700">
            <BookOpen className="w-4 h-4" />
            <span>Premium Course Collection</span>
            {isConnected && (
              <div className="flex items-center space-x-1 ml-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-400">Live</span>
              </div>
            )}
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Master <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">FlutterFlow</span> Development
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
            Transform your ideas into powerful mobile apps with our comprehensive, hands-on courses.
            From beginner fundamentals to advanced AI integrations - everything you need to become a FlutterFlow expert.
          </p>
        </div>

        {/* Course Grid - 2 Columns for Larger, More Detailed Cards */}
        <div className="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 2 }).map((_, index) => (
              <Card key={index} className="overflow-hidden bg-gray-800 border border-gray-700 shadow-xl rounded-xl">
                <div className="h-80 bg-gray-700 animate-pulse"></div>
                <CardHeader className="p-8">
                  <div className="h-8 bg-gray-700 rounded animate-pulse mb-4"></div>
                  <div className="h-5 bg-gray-700 rounded animate-pulse w-3/4 mb-3"></div>
                  <div className="h-5 bg-gray-700 rounded animate-pulse w-1/2"></div>
                </CardHeader>
                <CardContent className="p-8 pt-0">
                  <div className="space-y-4">
                    <div className="h-5 bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-5 bg-gray-700 rounded animate-pulse w-2/3"></div>
                    <div className="h-14 bg-gray-700 rounded animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : courses.length > 0 ? (
            courses.map((course) => (
            <Card key={course.id} className="group overflow-hidden bg-gray-800 border border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 rounded-xl">
              <div className="relative overflow-hidden">
                <div className="h-80 overflow-hidden bg-gradient-to-br from-blue-600 to-purple-700">
                  <img
                    src={course.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=600&fit=crop'}
                    alt={course.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                    <div className="bg-white/10 backdrop-blur-sm rounded-full p-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-75 group-hover:scale-100 border border-white/20">
                      <PlayCircle className="w-12 h-12 text-white" />
                    </div>
                  </div>
                </div>

                {/* Badges */}
                <div className="absolute top-6 left-6">
                  <Badge className={`${getLevelColor(course.level)} font-medium shadow-lg text-sm px-3 py-1`}>
                    {course.level.charAt(0).toUpperCase() + course.level.slice(1)}
                  </Badge>
                </div>
                <div className="absolute top-6 right-6">
                  <div className="flex items-center bg-black/50 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-medium shadow-lg border border-white/20">
                    <Star className="w-4 h-4 mr-1 fill-yellow-400 text-yellow-400" />
                    {course.rating || '4.8'}
                  </div>
                </div>

                {/* Price Badge */}
                <div className="absolute bottom-6 right-6">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full shadow-xl border border-white/20">
                    <div className="flex flex-col items-center">
                      <div className="font-bold text-xl">
                        {course.price === 0 ? 'Free' : `KES ${course.price?.toLocaleString() || '0'}`}
                      </div>
                      {course.originalPrice && course.originalPrice > (course.price || 0) && (
                        <div className="text-sm text-gray-300 line-through">
                          KES {course.originalPrice.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-8">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-white mb-3 line-clamp-2 group-hover:text-blue-400 transition-colors duration-300">
                    {course.title}
                  </h3>
                  {/* Use short description if available, fallback to description */}
                  <p className="text-gray-300 text-base line-clamp-4 leading-relaxed">
                    {course.shortDescription || course.description}
                  </p>
                </div>

                {/* Course Stats */}
                <div className="flex items-center justify-between text-sm text-gray-400 mb-6 pb-6 border-b border-gray-700">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 mr-2 text-blue-400" />
                      <span className="font-medium text-white">{formatDuration(course.duration || 180)}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="w-5 h-5 mr-2 text-green-400" />
                      <span className="font-medium text-white">{(course.enrollmentCount || 1234).toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="w-5 h-5 mr-2 text-purple-400" />
                    <span className="font-medium text-white">{course.modules?.length || 8} modules</span>
                  </div>
                </div>

                {/* Instructor */}
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white text-lg font-bold">
                      {(course.instructor || 'Ahmed Takal').charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-base font-medium text-white">
                      {course.instructor || 'Ahmed Takal'}
                    </p>
                    <p className="text-sm text-gray-400">FlutterFlow Expert</p>
                  </div>
                </div>
              </div>

              <div className="px-8 pb-8">
                {/* Learning Outcomes Preview */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-4 text-white flex items-center text-lg">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
                    What you'll learn:
                  </h4>
                  <ul className="space-y-3">
                    {(course.learningOutcomes || [
                      'Build professional mobile apps with FlutterFlow',
                      'Master advanced UI/UX design principles',
                      'Integrate APIs and backend services',
                      'Deploy apps to App Store and Google Play'
                    ]).slice(0, 4).map((outcome, index) => (
                      <li key={index} className="flex items-start text-base text-gray-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                        {outcome}
                      </li>
                    ))}
                    {(course.learningOutcomes || []).length > 4 && (
                      <li className="text-base text-gray-400 font-medium">
                        +{(course.learningOutcomes || []).length - 4} more skills
                      </li>
                    )}
                  </ul>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-3 mb-8">
                  {(course.tags || ['FlutterFlow', 'Mobile Development', 'No-Code', 'UI/UX']).slice(0, 4).map((tag) => (
                    <span key={tag} className="px-4 py-2 bg-gray-700 text-gray-200 text-sm font-medium rounded-full border border-gray-600">
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Enroll Button */}
                <Button
                  onClick={() => handleViewCourse(course.id)}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl text-lg"
                >
                  <PlayCircle className="w-6 h-6 mr-3" />
                  Start Learning Now
                </Button>

                {/* Course Features */}
                <div className="mt-6 flex items-center justify-center space-x-6 text-sm text-gray-400">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                    <span>Lifetime Access</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                    <span>Certificate</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-purple-400 rounded-full mr-2"></div>
                    <span>Mobile Friendly</span>
                  </div>
                </div>
              </div>
            </Card>
          ))
          ) : (
            // No courses available
            <div className="col-span-2 text-center py-20">
              <div className="bg-gray-700 rounded-full w-32 h-32 flex items-center justify-center mx-auto mb-8">
                <BookOpen className="w-16 h-16 text-gray-400" />
              </div>
              <h3 className="text-3xl font-bold text-white mb-4">No Courses Available Yet</h3>
              <p className="text-gray-300 mb-10 max-w-lg mx-auto text-lg">
                We're working hard to bring you amazing FlutterFlow courses. Check back soon for exciting content!
              </p>
              <div className="space-y-4">
                <Button
                  onClick={() => window.location.href = '/collections-debug'}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3"
                >
                  📊 View All Collections
                </Button>
                <Button
                  onClick={createTestCourse}
                  disabled={creating}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3"
                >
                  {creating ? '⏳ Creating...' : '🚀 Create Test Course Now'}
                </Button>
                {currentUser && (
                  <Button
                    onClick={() => window.location.href = '/admin'}
                    variant="outline"
                    className="border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-3"
                  >
                    Admin Panel
                  </Button>
                )}
                <div className="text-center">
                  <p className="text-gray-400 text-sm">
                    💡 Courses will appear automatically when created in Firestore or Admin Panel
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default CourseCatalogSection;
