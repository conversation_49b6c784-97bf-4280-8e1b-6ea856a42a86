/**
 * Email Service for FreeCodeLap
 * Handles sending enrollment notifications and other emails
 */

// Environment detection utility
const getEnvironment = () => {
  const hostname = window.location.hostname;
  return {
    isDevelopment: hostname === 'localhost' ||
                  hostname === '127.0.0.1' ||
                  hostname.includes('localhost') ||
                  hostname.includes('dev'),
    isProduction: hostname.includes('freelapcode.com') ||
                 hostname.includes('freecodelap.com'),
    hostname
  };
};

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

export interface EmailTemplate {
  enrollment: (userName: string, courseTitle: string, adminNote?: string) => EmailData;
  welcome: (userName: string) => EmailData;
  courseCompletion: (userName: string, courseTitle: string) => EmailData;
}

class EmailService {
  private apiKey: string | null = null;
  private fromEmail: string = '<EMAIL>';
  private environment: ReturnType<typeof getEnvironment>;

  constructor() {
    // Get environment information
    this.environment = getEnvironment();

    // In production, get API key from environment variables
    // For now, we'll use a placeholder since we're not integrating with a real email service yet
    this.apiKey = null; // Will be set when integrating with actual email service

    console.log('📧 Email Service initialized:', {
      environment: this.environment.isDevelopment ? 'development' : 'production',
      hostname: this.environment.hostname
    });
  }

  /**
   * Email templates for different types of notifications
   */
  templates: EmailTemplate = {
    enrollment: (userName: string, courseTitle: string, adminNote?: string) => ({
      to: '', // Will be set when sending
      subject: `Welcome! You've been enrolled in ${courseTitle}`,
      html: this.generateEnrollmentEmailHTML(userName, courseTitle, adminNote),
      from: this.fromEmail
    }),

    welcome: (userName: string) => ({
      to: '', // Will be set when sending
      subject: 'Welcome to FreeCodeLap!',
      html: this.generateWelcomeEmailHTML(userName),
      from: this.fromEmail
    }),

    courseCompletion: (userName: string, courseTitle: string) => ({
      to: '', // Will be set when sending
      subject: `Congratulations! You've completed ${courseTitle}`,
      html: this.generateCompletionEmailHTML(userName, courseTitle),
      from: this.fromEmail
    })
  };

  /**
   * Send enrollment notification email
   */
  async sendEnrollmentNotification(
    userEmail: string,
    userName: string,
    courseTitle: string,
    adminNote?: string
  ): Promise<boolean> {
    try {
      const emailData = this.templates.enrollment(userName, courseTitle, adminNote);
      emailData.to = userEmail;

      console.log('📧 Preparing enrollment notification email:', {
        to: userEmail,
        subject: emailData.subject,
        courseTitle,
        userName
      });

      // For development: Log email content
      if (this.environment.isDevelopment) {
        console.log('📧 Email HTML Preview (Development Mode):', {
          to: emailData.to,
          subject: emailData.subject,
          htmlLength: emailData.html.length
        });
        console.log('📧 Development Mode: Email simulation successful');

        // In development, show a more detailed preview
        if (window.console && window.console.groupCollapsed) {
          console.groupCollapsed('📧 Email HTML Content Preview');
          console.log(emailData.html);
          console.groupEnd();
        }

        return true; // Simulate successful send in development
      }

      // In production, integrate with actual email service
      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('❌ Error sending enrollment notification:', error);
      return false;
    }
  }

  /**
   * Send email using configured email service
   */
  private async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      // TODO: Integrate with actual email service
      // Examples:
      // - SendGrid: await sgMail.send(emailData)
      // - AWS SES: await ses.sendEmail(emailData)
      // - Nodemailer: await transporter.sendMail(emailData)
      
      console.log('📧 Email would be sent in production:', emailData);
      return true;
    } catch (error) {
      console.error('❌ Error sending email:', error);
      return false;
    }
  }

  /**
   * Generate HTML for enrollment notification email
   */
  private generateEnrollmentEmailHTML(userName: string, courseTitle: string, adminNote?: string): string {
    const baseUrl = this.getBaseUrl();
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🎉 Welcome to FreeCodeLap!</h1>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName}!</h2>
          
          <p style="color: #555; font-size: 16px; line-height: 1.6;">
            Great news! You have been enrolled in <strong>${courseTitle}</strong>.
          </p>
          
          ${adminNote && adminNote !== 'Manually enrolled by admin' ? `
            <div style="background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;">
              <p style="margin: 0; color: #1976d2;"><strong>Note from admin:</strong></p>
              <p style="margin: 5px 0 0 0; color: #555;">${adminNote}</p>
            </div>
          ` : ''}
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e0e0e0;">
            <h3 style="color: #333; margin-top: 0;">What's Next?</h3>
            <ul style="color: #555; line-height: 1.6;">
              <li>Log in to your FreeCodeLap account</li>
              <li>Navigate to "My Courses" to access your new course</li>
              <li>Start learning at your own pace</li>
              <li>Complete assignments and track your progress</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${baseUrl}/my-courses" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold;
                      display: inline-block;">
              Access My Courses
            </a>
          </div>
          
          <p style="color: #777; font-size: 14px; text-align: center; margin-top: 30px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
        
        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 FreeCodeLap. All rights reserved.
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Get base URL for email links
   */
  private getBaseUrl(): string {
    try {
      return window.location.origin;
    } catch (error) {
      // Fallback for environments where window is not available
      return 'https://freelapcode.com';
    }
  }

  /**
   * Generate HTML for welcome email
   */
  private generateWelcomeEmailHTML(userName: string): string {
    const baseUrl = this.getBaseUrl();
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to FreeCodeLap!</h1>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName}!</h2>
          
          <p style="color: #555; font-size: 16px; line-height: 1.6;">
            Welcome to FreeCodeLap! We're excited to have you join our community of learners.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${baseUrl}/courses" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold;
                      display: inline-block;">
              Explore Courses
            </a>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate HTML for course completion email
   */
  private generateCompletionEmailHTML(userName: string, courseTitle: string): string {
    const baseUrl = this.getBaseUrl();
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName}!</h2>
          
          <p style="color: #555; font-size: 16px; line-height: 1.6;">
            Congratulations on completing <strong>${courseTitle}</strong>! 
            You've achieved something amazing.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${baseUrl}/my-courses" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold;
                      display: inline-block;">
              View Certificate
            </a>
          </div>
        </div>
      </div>
    `;
  }
}

export const emailService = new EmailService();
