/**
 * FreeCodeLap Platform - Firestore Schema Types
 * 
 * This file defines the TypeScript interfaces for the Firestore database schema.
 */

import { Timestamp } from "firebase/firestore";

/**
 * User profile stored in /users/{userId}
 */
export interface User {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'student';
  createdAt: Timestamp;
  lastLoginAt?: Timestamp;
  bio?: string;
  phoneNumber?: string;
  country?: string;
  profession?: string;
  interests?: string[];
  socialLinks?: {
    website?: string;
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
}

/**
 * Course stored in /courses/{courseId}
 */
export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  price: number;
  currency: string; // Default: 'KES'
  skillLevel: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  videoUrl?: string; // Intro video URL
  duration: string; // e.g., "8 weeks"
  lessons: number; // Total number of lessons
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string; // User ID of creator
  published: boolean;
  featured: boolean;
  enrollments: number; // Counter for total enrollments
  rating?: number; // Average rating
  reviews?: number; // Number of reviews
}

/**
 * Module stored in /courses/{courseId}/modules/{moduleId}
 */
export interface Module {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number; // For sorting
  lessons: number; // Number of lessons in this module
  duration: string; // e.g., "2 hours"
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Lesson stored in /courses/{courseId}/modules/{moduleId}/lessons/{lessonId}
 */
export interface Lesson {
  id: string;
  moduleId: string;
  courseId: string;
  title: string;
  description: string;
  videoUrl: string; // Backblaze video URL
  text?: string; // Lesson content in markdown
  order: number; // For sorting
  duration: string; // e.g., "15 minutes"
  resources?: {
    title: string;
    url: string;
    type: 'pdf' | 'link' | 'code' | 'other';
  }[];
  quiz?: {
    questions: {
      question: string;
      options: string[];
      correctOption: number;
    }[];
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Enrollment stored in /enrollments/{enrollmentId}
 */
export interface Enrollment {
  id: string; // Auto-generated document ID
  userId: string; // Firebase Auth UID or email for manual enrollments
  courseId: string;
  enrolledAt: Timestamp;
  enrollmentType: 'paid' | 'manual'; // How the user was enrolled
  status: 'active' | 'suspended' | 'completed';

  // Payment information (only for paid enrollments)
  paymentId?: string; // Reference to payment document

  // Progress tracking
  progress: {
    completedLessons: string[]; // Array of completed lesson IDs
    totalLessons: number; // Total lessons in course
    progressPercentage: number; // 0-100
    lastAccessedAt: Timestamp;
    lastCompletedLessonId?: string;
  };

  // Certificate information
  certificateIssued: boolean;
  certificateUrl?: string;
  certificateIssuedAt?: Timestamp;

  // Admin fields (for manual enrollments)
  adminNote?: string; // Note from admin who enrolled the user
  enrolledBy?: string; // Admin user ID who enrolled this user

  // Metadata
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Progress tracking stored in /progress/{userId}_{lessonId}
 */
export interface Progress {
  id: string; // userId_lessonId
  userId: string;
  courseId: string;
  moduleId: string;
  lessonId: string;
  completed: boolean;
  completedAt?: Timestamp;
  watchTime: number; // In seconds
  lastPosition: number; // Video position in seconds
  notes?: string;
  quizResults?: {
    score: number;
    totalQuestions: number;
    completedAt: Timestamp;
  };
}

/**
 * Announcement stored in /announcements/{announcementId}
 */
export interface Announcement {
  id: string;
  title: string;
  content: string;
  courseId?: string; // If null, sent to all users
  createdAt: Timestamp;
  createdBy: string; // User ID of creator
  sentToUsers: number; // Counter for number of users notified
  priority: 'low' | 'medium' | 'high';
}

/**
 * Certificate stored in /certificates/{userId}_{courseId}
 */
export interface Certificate {
  id: string; // userId_courseId
  userId: string;
  courseId: string;
  userName: string;
  courseName: string;
  issuedAt: Timestamp;
  certificateUrl: string;
  issuedBy: string; // User ID of issuer
  verified: boolean;
  verificationCode: string;
}

/**
 * Payment stored in /payments/{paymentId}
 */
export interface Payment {
  id: string; // Paystack reference or auto-generated ID
  userId: string; // Firebase Auth UID
  courseId: string;

  // Payment amounts
  amount: number; // Amount in smallest currency unit (kobo for KES, cents for USD)
  currency: string; // 'KES', 'USD', etc.
  displayAmount: number; // Human-readable amount (e.g., 35.00)

  // Payment status and method
  status: 'pending' | 'successful' | 'failed' | 'cancelled';
  paymentMethod: 'card' | 'mobile_money' | 'bank_transfer' | 'bank' | 'ussd' | 'qr';
  paymentProvider: 'paystack';

  // Paystack-specific details
  paystackDetails: {
    reference: string; // Paystack transaction reference
    transactionId?: string; // Paystack transaction ID
    authorizationCode?: string; // For recurring payments
    channel?: string; // Payment channel used
    cardType?: string; // visa, mastercard, etc.
    last4?: string; // Last 4 digits of card
    bank?: string; // Bank name for bank transfers
    accountNumber?: string; // Account number for bank transfers
  };

  // Customer information
  customerEmail: string;
  customerName?: string;
  customerPhone?: string;

  // Course information
  courseTitle: string;
  courseInstructor: string;

  // Metadata and tracking
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;

  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  paidAt?: Timestamp; // When payment was successfully completed
}

/**
 * Review stored in /reviews/{userId}_{courseId}
 */
export interface Review {
  id: string; // userId_courseId
  userId: string;
  courseId: string;
  rating: number; // 1-5
  comment: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  helpful: number; // Number of users who found this review helpful
}
