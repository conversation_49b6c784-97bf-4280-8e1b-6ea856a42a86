import React, { useState } from 'react';
import { doc, setDoc, collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareCourseForFirestore, sanitizeDocumentId } from '@/utils/firestoreUtils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Save, 
  Plus, 
  X, 
  Upload,
  AlertCircle,
  CheckCircle,
  Loader2,
  BookOpen,
  Video,
  DollarSign,
  Clock,
  Users,
  Star
} from 'lucide-react';

interface CourseFormData {
  title: string;
  description: string;
  shortDescription: string;
  thumbnail: string;
  videoUrl: string;
  instructor: string;
  instructorId: string;
  instructorAvatar: string;
  instructorBio: string;
  duration: string;
  totalDuration: number;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  category: string;
  price: number;
  originalPrice: number;
  currency: string;
  tags: string[];
  requirements: string[];
  learningOutcomes: string[];
  targetAudience: string[];
  isPublished: boolean;
  featured: boolean;
  allowComments: boolean;
  allowDownloads: boolean;
  certificateEnabled: boolean;
  accessType: 'lifetime' | 'subscription' | 'limited';
  accessDuration?: number;
}

interface CreateCourseFormProps {
  onCourseCreated?: () => void;
}

export const CreateCourseForm = ({ onCourseCreated }: CreateCourseFormProps) => {
  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    shortDescription: '',
    thumbnail: '',
    videoUrl: '',
    instructor: 'Ahmed Takal',
    instructorId: 'ahmed-takal',
    instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    instructorBio: 'Expert instructor with years of experience in technology education.',
    duration: '',
    totalDuration: 0,
    level: 'Beginner',
    category: '',
    price: 0,
    originalPrice: 0,
    currency: 'USD',
    tags: [],
    requirements: [],
    learningOutcomes: [],
    targetAudience: [],
    isPublished: false,
    featured: false,
    allowComments: true,
    allowDownloads: true,
    certificateEnabled: true,
    accessType: 'lifetime',
    accessDuration: undefined
  });

  const [currentTag, setCurrentTag] = useState('');
  const [currentRequirement, setCurrentRequirement] = useState('');
  const [currentOutcome, setCurrentOutcome] = useState('');
  const [currentAudience, setCurrentAudience] = useState('');
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const categories = [
    'No-Code Development',
    'AI Development', 
    'Web Development',
    'Mobile Development',
    'Data Science',
    'Machine Learning',
    'Backend Development',
    'Frontend Development',
    'DevOps',
    'Cybersecurity',
    'UI/UX Design',
    'Digital Marketing',
    'Business',
    'Programming Fundamentals'
  ];

  const handleInputChange = (field: keyof CourseFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addRequirement = () => {
    if (currentRequirement.trim() && !formData.requirements.includes(currentRequirement.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, currentRequirement.trim()]
      }));
      setCurrentRequirement('');
    }
  };

  const removeRequirement = (reqToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter(req => req !== reqToRemove)
    }));
  };

  const addOutcome = () => {
    if (currentOutcome.trim() && !formData.learningOutcomes.includes(currentOutcome.trim())) {
      setFormData(prev => ({
        ...prev,
        learningOutcomes: [...prev.learningOutcomes, currentOutcome.trim()]
      }));
      setCurrentOutcome('');
    }
  };

  const removeOutcome = (outcomeToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      learningOutcomes: prev.learningOutcomes.filter(outcome => outcome !== outcomeToRemove)
    }));
  };

  const addAudience = () => {
    if (currentAudience.trim() && !formData.targetAudience.includes(currentAudience.trim())) {
      setFormData(prev => ({
        ...prev,
        targetAudience: [...prev.targetAudience, currentAudience.trim()]
      }));
      setCurrentAudience('');
    }
  };

  const removeAudience = (audienceToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      targetAudience: prev.targetAudience.filter(audience => audience !== audienceToRemove)
    }));
  };

  const generateCourseId = (title: string): string => {
    return sanitizeDocumentId(title);
  };

  const validateForm = (): string | null => {
    if (!formData.title.trim()) return 'Course title is required';
    if (!formData.description.trim()) return 'Course description is required';
    if (!formData.category.trim()) return 'Course category is required';
    if (formData.price < 0) return 'Price cannot be negative';
    if (formData.totalDuration < 0) return 'Duration cannot be negative';
    if (formData.learningOutcomes.length === 0) return 'At least one learning outcome is required';

    // Validate access duration for limited access
    if (formData.accessType === 'limited' && (!formData.accessDuration || formData.accessDuration <= 0)) {
      return 'Access duration is required and must be greater than 0 for limited access courses';
    }

    // Validate URLs if provided
    if (formData.thumbnail && !isValidUrl(formData.thumbnail)) {
      return 'Please provide a valid thumbnail URL';
    }

    if (formData.videoUrl && !isValidUrl(formData.videoUrl)) {
      return 'Please provide a valid video URL';
    }

    return null;
  };

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const courseId = generateCourseId(formData.title);

      // Prepare the course data with proper cleaning and validation
      const rawCourseData = {
        id: courseId,
        ...formData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // If course has a videoUrl but no modules, create a default module and lesson
      if (formData.videoUrl && (!formData.modules || formData.modules.length === 0)) {
        console.log('📝 Creating default module and lesson for course with videoUrl');
        rawCourseData.modules = [
          {
            id: `${courseId}-module-1`,
            courseId: courseId,
            title: 'Course Content',
            description: 'Main course content',
            order: 1,
            lessons: [
              {
                id: `${courseId}-lesson-1`,
                moduleId: `${courseId}-module-1`,
                title: formData.title,
                description: formData.description,
                type: 'video',
                order: 1,
                duration: formData.totalDuration || 60,
                videoUrl: formData.videoUrl,
                textContent: '',
                resources: [],
                isFree: false,
                isPreview: false,
                isCompleted: false,
                watchTime: 0
              }
            ],
            assignments: [],
            quizzes: [],
            isLocked: false
          }
        ];
      }

      // Use utility function to clean and validate the data
      const courseData = prepareCourseForFirestore(rawCourseData);

      console.log('📝 Creating course with data:', courseData);

      // Write to Firestore
      const courseRef = doc(db, 'courses', courseId);
      await setDoc(courseRef, courseData);

      setSuccess(`✅ Course "${formData.title}" created successfully with ID: ${courseId}`);

      // Call callback to refresh course list
      if (onCourseCreated) {
        onCourseCreated();
      }

      // Reset form
      setFormData({
        title: '',
        description: '',
        shortDescription: '',
        thumbnail: '',
        videoUrl: '',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Expert instructor with years of experience in technology education.',
        duration: '',
        totalDuration: 0,
        level: 'Beginner',
        category: '',
        price: 0,
        originalPrice: 0,
        currency: 'USD',
        tags: [],
        requirements: [],
        learningOutcomes: [],
        targetAudience: [],
        isPublished: false,
        featured: false,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime',
        accessDuration: undefined
      });

      console.log('✅ Course created successfully:', courseData);

    } catch (error: any) {
      console.error('❌ Error creating course:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to create course';
      if (error.message.includes('invalid data')) {
        errorMessage = 'Invalid data provided. Please check all fields and try again.';
      } else if (error.message.includes('permission')) {
        errorMessage = 'Permission denied. Please check your admin access.';
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card className="bg-gray-800 border-gray-700 text-white">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Create New Course
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="bg-red-900/20 border-red-700 mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-300">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-900/20 border-green-700 mb-6">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-300">{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Basic Information</h3>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Course Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter course title"
                  className="bg-gray-700 border-gray-600 text-white"
                  required
                />
              </div>
              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter detailed course description"
                className="bg-gray-700 border-gray-600 text-white"
                rows={4}
                required
              />
            </div>

            <div>
              <Label htmlFor="shortDescription">Short Description</Label>
              <Input
                id="shortDescription"
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                placeholder="Brief course summary (for cards)"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          {/* Media */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Media</h3>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="thumbnail">Thumbnail URL</Label>
                <Input
                  id="thumbnail"
                  value={formData.thumbnail}
                  onChange={(e) => handleInputChange('thumbnail', e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="videoUrl">Preview Video URL</Label>
                <Input
                  id="videoUrl"
                  value={formData.videoUrl}
                  onChange={(e) => handleInputChange('videoUrl', e.target.value)}
                  placeholder="https://youtube.com/embed/..."
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
            </div>
          </div>

          {/* Course Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Course Details</h3>
            
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="level">Level</Label>
                <Select value={formData.level} onValueChange={(value: any) => handleInputChange('level', value)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    <SelectItem value="Beginner">Beginner</SelectItem>
                    <SelectItem value="Intermediate">Intermediate</SelectItem>
                    <SelectItem value="Advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  value={formData.duration}
                  onChange={(e) => handleInputChange('duration', e.target.value)}
                  placeholder="e.g., 8 weeks, 40 hours"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="totalDuration">Total Video Duration</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Input
                      type="number"
                      value={Math.floor(formData.totalDuration / 60)}
                      onChange={(e) => {
                        const hours = parseInt(e.target.value) || 0;
                        const minutes = formData.totalDuration % 60;
                        handleInputChange('totalDuration', hours * 60 + minutes);
                      }}
                      placeholder="8"
                      className="bg-gray-700 border-gray-600 text-white"
                      min="0"
                    />
                    <p className="text-xs text-gray-400 mt-1">Hours</p>
                  </div>
                  <div>
                    <Input
                      type="number"
                      value={formData.totalDuration % 60}
                      onChange={(e) => {
                        const minutes = parseInt(e.target.value) || 0;
                        const hours = Math.floor(formData.totalDuration / 60);
                        handleInputChange('totalDuration', hours * 60 + minutes);
                      }}
                      placeholder="30"
                      className="bg-gray-700 border-gray-600 text-white"
                      min="0"
                      max="59"
                    />
                    <p className="text-xs text-gray-400 mt-1">Minutes</p>
                  </div>
                </div>
                <p className="text-xs text-gray-400 mt-1">Total: {Math.floor(formData.totalDuration / 60)}h {formData.totalDuration % 60}m</p>
              </div>
            </div>
          </div>

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Pricing</h3>
            
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="price">Price (USD)</Label>
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', Number(e.target.value))}
                  placeholder="49.99"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="originalPrice">Original Price (USD)</Label>
                <Input
                  id="originalPrice"
                  type="number"
                  value={formData.originalPrice}
                  onChange={(e) => handleInputChange('originalPrice', Number(e.target.value))}
                  placeholder="6999"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="accessType">Access Type</Label>
                <Select value={formData.accessType} onValueChange={(value: any) => handleInputChange('accessType', value)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    <SelectItem value="lifetime">Lifetime Access</SelectItem>
                    <SelectItem value="subscription">Subscription</SelectItem>
                    <SelectItem value="limited">Limited Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Access Duration - only show for limited access */}
            {formData.accessType === 'limited' && (
              <div>
                <Label htmlFor="accessDuration">Access Duration (days)</Label>
                <Input
                  id="accessDuration"
                  type="number"
                  value={formData.accessDuration || ''}
                  onChange={(e) => handleInputChange('accessDuration', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="30"
                  className="bg-gray-700 border-gray-600 text-white"
                />
                <p className="text-xs text-gray-400 mt-1">Number of days students will have access to the course</p>
              </div>
            )}
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Tags</h3>

            <div className="flex gap-2">
              <Input
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                placeholder="Add a tag"
                className="bg-gray-700 border-gray-600 text-white"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="bg-blue-600">
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-2 hover:text-red-300"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Requirements</h3>

            <div className="flex gap-2">
              <Input
                value={currentRequirement}
                onChange={(e) => setCurrentRequirement(e.target.value)}
                placeholder="Add a requirement"
                className="bg-gray-700 border-gray-600 text-white"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRequirement())}
              />
              <Button type="button" onClick={addRequirement} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <ul className="space-y-2">
              {formData.requirements.map((req, index) => (
                <li key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                  <span>{req}</span>
                  <button
                    type="button"
                    onClick={() => removeRequirement(req)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Learning Outcomes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Learning Outcomes *</h3>

            <div className="flex gap-2">
              <Input
                value={currentOutcome}
                onChange={(e) => setCurrentOutcome(e.target.value)}
                placeholder="Add a learning outcome"
                className="bg-gray-700 border-gray-600 text-white"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addOutcome())}
              />
              <Button type="button" onClick={addOutcome} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <ul className="space-y-2">
              {formData.learningOutcomes.map((outcome, index) => (
                <li key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                  <span>{outcome}</span>
                  <button
                    type="button"
                    onClick={() => removeOutcome(outcome)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Target Audience */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Target Audience</h3>

            <div className="flex gap-2">
              <Input
                value={currentAudience}
                onChange={(e) => setCurrentAudience(e.target.value)}
                placeholder="Add target audience"
                className="bg-gray-700 border-gray-600 text-white"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAudience())}
              />
              <Button type="button" onClick={addAudience} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <ul className="space-y-2">
              {formData.targetAudience.map((audience, index) => (
                <li key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                  <span>{audience}</span>
                  <button
                    type="button"
                    onClick={() => removeAudience(audience)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Course Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Course Settings</h3>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="isPublished">Published</Label>
                  <Switch
                    id="isPublished"
                    checked={formData.isPublished}
                    onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="featured">Featured Course</Label>
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowComments">Allow Comments</Label>
                  <Switch
                    id="allowComments"
                    checked={formData.allowComments}
                    onCheckedChange={(checked) => handleInputChange('allowComments', checked)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowDownloads">Allow Downloads</Label>
                  <Switch
                    id="allowDownloads"
                    checked={formData.allowDownloads}
                    onCheckedChange={(checked) => handleInputChange('allowDownloads', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="certificateEnabled">Certificate Enabled</Label>
                  <Switch
                    id="certificateEnabled"
                    checked={formData.certificateEnabled}
                    onCheckedChange={(checked) => handleInputChange('certificateEnabled', checked)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Instructor Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-300">Instructor Information</h3>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="instructor">Instructor Name</Label>
                <Input
                  id="instructor"
                  value={formData.instructor}
                  onChange={(e) => handleInputChange('instructor', e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="instructorAvatar">Instructor Avatar URL</Label>
                <Input
                  id="instructorAvatar"
                  value={formData.instructorAvatar}
                  onChange={(e) => handleInputChange('instructorAvatar', e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="instructorBio">Instructor Bio</Label>
              <Textarea
                id="instructorBio"
                value={formData.instructorBio}
                onChange={(e) => handleInputChange('instructorBio', e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
                rows={3}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-6 border-t border-gray-700">
            <Button type="submit" disabled={saving} className="bg-green-600 hover:bg-green-700">
              {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
              {saving ? 'Creating Course...' : 'Create Course'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
