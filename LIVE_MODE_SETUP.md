# 🚀 FreeCodeLap Live Mode Payment Setup

## ⚠️ IMPORTANT: Live Mode Checklist

Before switching to live mode, ensure you have completed ALL of the following:

### 📋 Paystack Business Verification
- [ ] **Business Registration**: Submitted business documents
- [ ] **Bank Account**: Verified settlement account (Equity Bank)
- [ ] **KYC Completed**: Identity verification approved
- [ ] **Business Approved**: Paystack has approved your business

### 🔑 API Keys Setup
- [ ] **Live Keys Obtained**: From Paystack dashboard (Live mode)
- [ ] **Keys Updated**: In `.env` file and backend
- [ ] **Keys Tested**: Verified they work correctly

### 🛡️ Security & Compliance
- [ ] **SSL Certificate**: HTTPS enabled on production domain
- [ ] **Webhook Security**: Signature verification implemented
- [ ] **Error Monitoring**: Logging and alerting set up
- [ ] **Backup Systems**: Payment failure handling ready

---

## 🔧 Step-by-Step Live Mode Setup

### Step 1: Get Live API Keys

1. **Login to Paystack Dashboard**
   - Visit: https://dashboard.paystack.com
   - Login with your FreeCodeLap account

2. **Switch to Live Mode**
   - Toggle the "Live/Test" switch to "Live"
   - Ensure your business is verified

3. **Get API Keys**
   - Go to Settings → API Keys & Webhooks
   - Copy your Live keys:
     ```
     Live Public Key: pk_live_xxxxxxxxxxxxxxxxxx
     Live Secret Key: sk_live_xxxxxxxxxxxxxxxxxx
     ```

### Step 2: Update Environment Variables

Replace the keys in your `.env` file:

```env
# Paystack Configuration (Live Keys)
VITE_PAYSTACK_PUBLIC_KEY=pk_live_YOUR_ACTUAL_LIVE_PUBLIC_KEY
VITE_PAYSTACK_SECRET_KEY=sk_live_YOUR_ACTUAL_LIVE_SECRET_KEY

# Keep test keys commented for development
# VITE_PAYSTACK_PUBLIC_KEY=pk_test_89ae2fc67b744d6b6fe3be4e2354bd7c287b8515
# VITE_PAYSTACK_SECRET_KEY=sk_test_c85ff96695f9bab79686fbb12783eaa57d55948a
```

### Step 3: Update Backend Configuration

Update your backend `.env` file:

```env
PAYSTACK_SECRET_KEY=sk_live_YOUR_ACTUAL_LIVE_SECRET_KEY
FRONTEND_URL=https://your-production-domain.com
PORT=3001
```

### Step 4: Test Live Mode

1. **Small Test Payment**
   - Use a real card with small amount (KES 10-50)
   - Verify payment processes correctly
   - Check Paystack dashboard for transaction

2. **M-Pesa Test**
   - Use your real phone number
   - Test with small amount (KES 10-50)
   - Verify STK push and completion

3. **Enrollment Test**
   - Verify course enrollment works
   - Check user gets course access
   - Test progress tracking

---

## 🎯 Live Mode Features

### ✅ What Works in Live Mode
- **Real card payments** (Visa, Mastercard, Verve)
- **Real M-Pesa payments** with STK push
- **Automatic course enrollment**
- **Real-time payment verification**
- **Course access after payment**

### 💰 Transaction Fees (Live Mode)
- **Local Cards**: 2.9% + KES 20
- **International Cards**: 3.9% + KES 20
- **M-Pesa**: 1.5%
- **Bank Transfer**: 1.5%

### 🔒 Security Features
- **PCI DSS Compliance**: Paystack handles card security
- **3D Secure**: Additional authentication for cards
- **Fraud Detection**: Automatic fraud prevention
- **Webhook Verification**: Secure payment notifications

---

## 🚨 Live Mode Safety Guidelines

### Before Going Live
1. **Test Everything**: Complete end-to-end testing
2. **Small Amounts**: Start with KES 10-100 transactions
3. **Monitor Closely**: Watch first few transactions
4. **Have Support Ready**: Be available for customer issues

### During Live Operation
1. **Monitor Dashboard**: Check Paystack dashboard regularly
2. **Handle Disputes**: Respond to customer payment issues
3. **Track Enrollments**: Ensure course access works
4. **Backup Plans**: Have manual enrollment process ready

### Emergency Procedures
1. **Payment Issues**: Contact Paystack support immediately
2. **Failed Enrollments**: Manual course access process
3. **Refunds**: Use Paystack dashboard for refunds
4. **Disputes**: Follow Paystack dispute resolution

---

## 📊 Monitoring & Analytics

### Key Metrics to Track
- **Payment Success Rate**: Should be >95%
- **Course Enrollment Rate**: Should match payments
- **Customer Support Tickets**: Payment-related issues
- **Revenue**: Daily/weekly payment totals

### Paystack Dashboard
- **Transactions**: View all payments
- **Analytics**: Payment trends and insights
- **Disputes**: Handle customer disputes
- **Settlements**: Track money transfers to your bank

---

## 🆘 Troubleshooting

### Common Issues

#### "Invalid API Key" Error
- **Solution**: Verify live keys are correct
- **Check**: Keys start with `pk_live_` and `sk_live_`

#### "Business Not Verified" Error
- **Solution**: Complete Paystack business verification
- **Contact**: Paystack support for verification status

#### Payment Succeeds but No Course Access
- **Solution**: Check enrollment service logs
- **Manual Fix**: Manually enroll user in course

#### M-Pesa STK Push Not Received
- **Solution**: Verify phone number format (254XXXXXXXXX)
- **Check**: User has sufficient M-Pesa balance

---

## 📞 Support Contacts

### Paystack Support
- **Email**: <EMAIL>
- **Phone**: +234 1 888 7888
- **Dashboard**: Live chat in dashboard

### FreeCodeLap Support
- **Email**: <EMAIL>
- **Phone**: +************

---

## ✅ Go-Live Checklist

Before switching to live mode, confirm:

- [ ] ✅ Paystack business verification complete
- [ ] ✅ Live API keys obtained and updated
- [ ] ✅ SSL certificate installed on domain
- [ ] ✅ Backend deployed with live keys
- [ ] ✅ Small test payments successful
- [ ] ✅ Course enrollment working
- [ ] ✅ Error monitoring in place
- [ ] ✅ Customer support ready
- [ ] ✅ Refund process documented
- [ ] ✅ Team trained on live operations

**🚀 Ready to go live? Update your API keys and start accepting real payments!**
