import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Smartphone, 
  Lock, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  Loader2 
} from 'lucide-react';

interface SecurePaymentFormProps {
  amount: number;
  currency: string;
  email: string;
  courseTitle: string;
  courseId: string;
  onSuccess: (response: any) => void;
  onError: (error: string) => void;
}

const SecurePaymentForm: React.FC<SecurePaymentFormProps> = ({
  amount,
  currency,
  email,
  courseTitle,
  courseId,
  onSuccess,
  onError,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [paystackLoaded, setPaystackLoaded] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'card' | 'mpesa'>('card');
  const [securityChecks, setSecurityChecks] = useState({
    ssl: false,
    paystack: false,
    encryption: false,
  });

  // Check security features
  useEffect(() => {
    // Check if we're on HTTPS
    const isSecure = window.location.protocol === 'https:' || 
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1';
    
    setSecurityChecks(prev => ({
      ...prev,
      ssl: isSecure,
      encryption: true, // Always true for Paystack
    }));

    // Load Paystack script securely
    const script = document.createElement('script');
    script.src = 'https://js.paystack.co/v1/inline.js';
    script.async = true;
    script.crossOrigin = 'anonymous';
    script.onload = () => {
      setPaystackLoaded(true);
      setSecurityChecks(prev => ({ ...prev, paystack: true }));
    };
    script.onerror = () => {
      console.error('Failed to load Paystack script');
      setPaystackLoaded(false);
      // Don't call onError here, just log the error
    };
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [onError]);

  const handleSecurePayment = () => {
    console.log('🔄 Starting secure payment process...');

    if (!paystackLoaded) {
      console.error('❌ Paystack not loaded');
      onError('Payment processor not loaded. Please refresh the page and try again.');
      return;
    }

    // Allow localhost for development
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1';

    if (!securityChecks.ssl && !isLocalhost) {
      console.error('❌ SSL required for production');
      onError('Secure connection required for payment processing.');
      return;
    }

    setIsLoading(true);

    // Use live Paystack keys for production
    const PAYSTACK_PUBLIC_KEY = 'pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3';

    const config = {
      key: PAYSTACK_PUBLIC_KEY,
      email: email,
      amount: amount * 100, // Convert to kobo/cents
      currency: currency,
      channels: selectedMethod === 'mpesa' ? ['mobile_money'] : ['card'],
      callback: (response: any) => {
        setIsLoading(false);
        console.log('✅ Secure payment successful:', response);
        onSuccess(response);
      },
      onClose: () => {
        setIsLoading(false);
        console.log('⚠️ Payment cancelled by user');
        // Don't show error for user cancellation
      },
      metadata: {
        custom_fields: [
          {
            display_name: 'Course',
            variable_name: 'course_name',
            value: courseTitle,
          },
          {
            display_name: 'Course ID',
            variable_name: 'course_id',
            value: courseId,
          },
          {
            display_name: 'Business',
            variable_name: 'business_name',
            value: 'FreeCodeLap',
          },
          {
            display_name: 'Instructor',
            variable_name: 'instructor',
            value: 'Ahmed Takal',
          },
        ],
      },
    };

    try {
      console.log('🚀 Initializing Paystack with config:', config);

      if (!(window as any).PaystackPop) {
        throw new Error('PaystackPop not available');
      }

      const handler = (window as any).PaystackPop.setup(config);
      handler.openIframe();
    } catch (error) {
      setIsLoading(false);
      console.error('❌ Payment initialization error:', error);
      onError('Failed to initialize payment. Please try again or contact support.');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <Card className="bg-gray-800 border-gray-700 shadow-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-white flex items-center">
          <Shield className="w-6 h-6 mr-2 text-green-400" />
          Secure Payment
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Security Status */}
        <div className="bg-gray-700/50 border border-gray-600 rounded-lg p-4">
          <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
            <Lock className="w-5 h-5 mr-2 text-green-400" />
            Security Status
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">SSL Encryption</span>
              {securityChecks.ssl ? (
                <Badge className="bg-green-600 text-white">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Secure
                </Badge>
              ) : (
                <Badge className="bg-red-600 text-white">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Insecure
                </Badge>
              )}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Payment Processor</span>
              {securityChecks.paystack ? (
                <Badge className="bg-green-600 text-white">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Loaded
                </Badge>
              ) : (
                <Badge className="bg-yellow-600 text-white">
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Loading
                </Badge>
              )}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Data Encryption</span>
              <Badge className="bg-green-600 text-white">
                <CheckCircle className="w-3 h-3 mr-1" />
                Active
              </Badge>
            </div>
          </div>
        </div>

        {/* Payment Method Selection */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Select Payment Method</h4>
          <div className="grid grid-cols-2 gap-4">
            <Button
              onClick={() => setSelectedMethod('card')}
              className={`p-4 h-auto flex flex-col items-center space-y-2 ${
                selectedMethod === 'card' 
                  ? 'bg-blue-600 text-white border-blue-500' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <CreditCard className="w-6 h-6" />
              <span className="font-medium">Credit/Debit Card</span>
              <span className="text-xs opacity-75">Visa, Mastercard, Verve</span>
            </Button>
            
            {currency === 'KES' && (
              <Button
                onClick={() => setSelectedMethod('mpesa')}
                className={`p-4 h-auto flex flex-col items-center space-y-2 ${
                  selectedMethod === 'mpesa' 
                    ? 'bg-green-600 text-white border-green-500' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                <Smartphone className="w-6 h-6" />
                <span className="font-medium">M-Pesa</span>
                <span className="text-xs opacity-75">Mobile Money</span>
              </Button>
            )}
          </div>
        </div>

        {/* Payment Details */}
        <div className="bg-gray-700/50 border border-gray-600 rounded-lg p-4">
          <h4 className="text-lg font-semibold text-white mb-3">Payment Details</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-300">Course:</span>
              <span className="text-white font-medium">{courseTitle}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Instructor:</span>
              <span className="text-white font-medium">Ahmed Takal</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Amount:</span>
              <span className="text-white font-bold text-lg">{formatCurrency(amount, currency)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Access:</span>
              <span className="text-green-400 font-medium">Lifetime</span>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        {!securityChecks.ssl && window.location.hostname !== 'localhost' && (
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-400">
              <strong>Security Warning:</strong> This connection is not secure. Payment autofill has been disabled for your protection.
              Please ensure you're using HTTPS for secure payment processing.
            </AlertDescription>
          </Alert>
        )}

        {/* Development Notice */}
        {window.location.hostname === 'localhost' && (
          <Alert className="border-blue-500/50 bg-blue-500/10">
            <AlertTriangle className="h-4 w-4 text-blue-400" />
            <AlertDescription className="text-blue-400">
              <strong>Development Mode:</strong> You're in development mode. Payment processing is enabled for testing purposes.
            </AlertDescription>
          </Alert>
        )}

        {/* Payment Button */}
        <Button
          onClick={handleSecurePayment}
          disabled={isLoading || !paystackLoaded || (!securityChecks.ssl && window.location.hostname !== 'localhost')}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Processing Secure Payment...
            </>
          ) : (
            <>
              <Lock className="w-5 h-5 mr-2" />
              Pay {formatCurrency(amount, currency)} Securely
            </>
          )}
        </Button>

        {/* Security Footer */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-400">
            <Shield className="w-4 h-4 text-green-400" />
            <span>Secured by Paystack • PCI DSS Compliant</span>
          </div>
          <p className="text-xs text-gray-500">
            Your payment information is encrypted and never stored on our servers
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SecurePaymentForm;
