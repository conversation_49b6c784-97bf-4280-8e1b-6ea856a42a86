# 🔧 Google Sign-In Fix Guide

## Issues Found & Solutions

### 1. **Port Configuration Mismatch**
- Your Vite server runs on port **8080** (not 5173)
- Firebase Console needs to be configured for the correct port

### 2. **Firebase Console Configuration Required**

#### Step 1: Firebase Console - Authorized Domains
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: **freecodelap**
3. Go to **Authentication** → **Settings** → **Authorized domains**
4. Add these domains:
   ```
   localhost
   localhost:8080
   freecodelap.firebaseapp.com
   ```

#### Step 2: Google Cloud Console - OAuth Configuration
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: **freecodelap**
3. Go to **APIs & Services** → **Credentials**
4. Find your OAuth 2.0 Client ID and edit it
5. Add **Authorized JavaScript origins**:
   ```
   http://localhost:8080
   https://freecodelap.firebaseapp.com
   ```
6. Add **Authorized redirect URIs**:
   ```
   http://localhost:8080/__/auth/handler
   https://freecodelap.firebaseapp.com/__/auth/handler
   ```

#### Step 3: OAuth Consent Screen
1. In Google Cloud Console → **APIs & Services** → **OAuth consent screen**
2. Ensure these are configured:
   - **Application name**: FreeCodeLap
   - **User support email**: <EMAIL>
   - **Developer contact**: <EMAIL>
   - **Authorized domains**: Add `freecodelap.firebaseapp.com`

### 3. **Enable Required APIs**
1. In Google Cloud Console → **APIs & Services** → **Library**
2. Enable these APIs:
   - **Google+ API** (if available)
   - **People API**
   - **Identity and Access Management (IAM) API**

## Testing Steps

1. **Clear Browser Data**:
   - Clear cookies and cache for localhost:8080
   - Try in incognito mode

2. **Check Browser Console**:
   - Open Developer Tools → Console
   - Look for specific error messages

3. **Test Authentication**:
   - Try Google Sign-in
   - Check for timeout or configuration errors

## Common Error Messages & Solutions

- **"unauthorized-domain"** → Add localhost:8080 to Firebase authorized domains
- **"popup-blocked"** → Allow popups or use redirect method
- **"internal-error"** → Check OAuth consent screen configuration
- **"timeout"** → Usually indicates domain authorization issue

## Quick Verification Checklist

- [ ] Firebase project: `freecodelap` selected
- [ ] Authentication → Sign-in method → Google enabled
- [ ] Authorized domains include `localhost` and `localhost:8080`
- [ ] OAuth client configured with correct origins and redirects
- [ ] OAuth consent screen properly configured
- [ ] Browser allows popups for localhost:8080
- [ ] No ad blockers interfering

## If Still Not Working

1. **Check Network Tab**: Look for failed requests to Google APIs
2. **Try Different Browser**: Test in Chrome, Firefox, Safari
3. **Check Firewall**: Ensure Google APIs aren't blocked
4. **Contact Firebase Support**: If configuration looks correct

The most common cause is missing domain authorization in Firebase Console.
