import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Course } from '@/types/course';
import { Clock, Users, Star, BookOpen, User } from 'lucide-react';

interface CourseCardProps {
  course: Course;
  showEnrollButton?: boolean;
  isEnrolled?: boolean;
  className?: string;
}

const CourseCard: React.FC<CourseCardProps> = ({
  course,
  showEnrollButton = true,
  isEnrolled = false,
  className = ''
}) => {
  const navigate = useNavigate();

  const skillLevelColors = {
    Beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    Intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    Advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  };

  const handleCardClick = () => {
    navigate(`/course/${course.id}`);
  };

  return (
    <Card
      className={`group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer ${className}`}
      onClick={handleCardClick}
    >
      {/* Course Thumbnail */}
      <div className="relative overflow-hidden rounded-t-lg">
        <img
          src={course.thumbnail}
          alt={course.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {course.featured && (
          <Badge className="absolute top-3 left-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
            Featured
          </Badge>
        )}
        <div className="absolute top-3 right-3">
          <Badge className={skillLevelColors[course.level] || skillLevelColors.Beginner}>
            {course.level ? course.level.charAt(0).toUpperCase() + course.level.slice(1) : 'Beginner'}
          </Badge>
        </div>
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors">
            {course.title}
          </h3>
          {course.rating && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>{course.rating.toFixed(1)}</span>
            </div>
          )}
        </div>
        {/* Use short description if available, fallback to description */}
        <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
          {course.shortDescription || course.description}
        </p>

        {/* Instructor */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <User className="h-4 w-4" />
          <span>{course.instructor || 'Ahmed Takal'}</span>
        </div>
      </CardHeader>

      <CardContent className="pb-3">
        {/* Course Stats */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>{course.duration || 'Self-paced'}</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="h-4 w-4" />
            <span>{course.modules?.length || 0} modules</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>{course.enrollmentCount || course.enrolledCount || 0} students</span>
          </div>
        </div>

        {/* Course Tags */}
        <div className="flex flex-wrap gap-1 mb-3">
          {course.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {course.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{course.tags.length - 3} more
            </Badge>
          )}
        </div>

        {/* Price */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            {course.price === 0 ? (
              <div className="text-2xl font-bold text-green-600">
                Free
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-primary">
                  ${course.price.toFixed(2)} USD
                </div>
                {course.originalPrice && course.originalPrice > course.price && (
                  <div className="text-lg text-muted-foreground line-through">
                    ${course.originalPrice.toFixed(2)} USD
                  </div>
                )}
              </div>
            )}
            {course.price > 0 && (
              <div className="text-xs text-muted-foreground">
                One-time payment
              </div>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-0">
        <div className="w-full">
          {isEnrolled ? (
            <Button
              asChild
              className="w-full"
              variant="default"
              onClick={(e) => e.stopPropagation()}
            >
              <Link to={`/courses/${course.id}`}>
                Continue Learning
              </Link>
            </Button>
          ) : showEnrollButton ? (
            <Button
              asChild
              className="w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <Link to={`/course/${course.id}`}>
                Enroll Now
              </Link>
            </Button>
          ) : (
            <div className="text-center text-sm text-muted-foreground py-2">
              Click to view course details
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default CourseCard;
