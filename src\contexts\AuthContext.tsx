import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification,
  UserCredential,
  GoogleAuthProvider,
  signInWithPopup
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { UserProfile, UserRole, LoginCredentials, RegisterData, AuthState } from '@/types/user';
import { RoleService } from '@/services/roleService';

interface AuthContextType extends AuthState {
  // Authentication methods
  loginWithEmail: (credentials: LoginCredentials) => Promise<UserCredential>;
  registerWithEmail: (data: RegisterData) => Promise<UserCredential>;
  loginWithGoogle: () => Promise<UserCredential>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;

  // Profile management
  updateUserProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshUserProfile: () => Promise<void>;

  // Utility methods
  isAdmin: () => boolean;
  isUser: () => boolean;
  hasRole: (role: UserRole) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Utility function to remove undefined values from objects
  const cleanFirestoreData = (obj: any): any => {
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      if (obj[key] !== undefined && obj[key] !== null) {
        if (typeof obj[key] === 'object' && !Array.isArray(obj[key]) && !(obj[key] instanceof Date)) {
          cleaned[key] = cleanFirestoreData(obj[key]);
        } else {
          cleaned[key] = obj[key];
        }
      }
    });
    return cleaned;
  };

  // Utility function to wait for auth state to be ready
  const waitForAuthState = async (user: User, maxAttempts = 5): Promise<boolean> => {
    for (let i = 0; i < maxAttempts; i++) {
      if (currentUser?.uid === user.uid) {
        console.log(`✅ Auth state ready after ${i + 1} attempts`);
        return true;
      }
      console.log(`⏳ Waiting for auth state... attempt ${i + 1}/${maxAttempts}`);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    console.warn('⚠️ Auth state not ready after maximum attempts');
    return false;
  };

  // Create user profile in Firestore
  const createUserProfile = async (user: User, additionalData?: Partial<UserProfile>) => {
    if (!user) return;

    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      const { displayName, email, photoURL } = user;
      // Determine role based on email
      const userRole = RoleService.getDefaultRole(email!);

      // Create profile data, filtering out undefined values
      const profileData: any = {
        uid: user.uid,
        email: email!,
        displayName: displayName || email!.split('@')[0],
        role: userRole, // Role based on email
        enrolledCourses: [],
        completedCourses: [],
        certificates: [],
        paymentHistory: [],
        preferences: {
          emailNotifications: true,
          courseUpdates: true,
          marketingEmails: false,
          language: 'en',
          timezone: 'Africa/Nairobi'
        },
        ...additionalData
      };

      console.log(`👤 Creating user profile with role: ${userRole} for email: ${email}`);

      if (userRole === 'admin') {
        console.log('🔑 Admin user detected - granting admin privileges');
      }

      // Only add profilePictureUrl if it exists
      if (photoURL) {
        profileData.profilePictureUrl = photoURL;
      }

      // Only add optional fields if they exist
      if (additionalData?.firstName) profileData.firstName = additionalData.firstName;
      if (additionalData?.lastName) profileData.lastName = additionalData.lastName;
      if (additionalData?.bio) profileData.bio = additionalData.bio;
      if (additionalData?.phoneNumber) profileData.phoneNumber = additionalData.phoneNumber;
      if (additionalData?.country) profileData.country = additionalData.country;
      if (additionalData?.profession) profileData.profession = additionalData.profession;
      if (additionalData?.interests) profileData.interests = additionalData.interests;
      if (additionalData?.socialLinks) profileData.socialLinks = additionalData.socialLinks;

      try {
        const cleanedData = cleanFirestoreData({
          ...profileData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });

        await setDoc(userRef, cleanedData);
        console.log('✅ User profile created successfully');
      } catch (error) {
        console.error('❌ Error creating user profile:', error);
        throw error;
      }
    } else {
      // Update existing profile with Google photo if available and not already set
      const existingData = userSnap.data() as UserProfile;
      if (user.photoURL && !existingData.profilePictureUrl) {
        try {
          await updateDoc(userRef, {
            profilePictureUrl: user.photoURL,
            updatedAt: serverTimestamp()
          });
          console.log('✅ Profile picture updated from Google');
        } catch (error) {
          console.error('❌ Error updating profile picture:', error);
        }
      }
    }
  };

  // Load user profile from Firestore
  const loadUserProfile = async (user: User) => {
    try {
      console.log('📄 Loading profile for user:', user.email);
      const userRef = doc(db, 'users', user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        console.log('✅ User profile found in Firestore');
        const data = userSnap.data();

        // Create profile with safe defaults for missing fields
        const profile: UserProfile = {
          uid: user.uid,
          email: user.email || data.email || '',
          displayName: user.displayName || data.displayName || '',
          role: data.role || 'student',
          createdAt: (data.createdAt as any)?.toDate?.() || new Date(),
          updatedAt: (data.updatedAt as any)?.toDate?.() || new Date(),
          enrolledCourses: data.enrolledCourses || [],
          completedCourses: data.completedCourses || [],
          certificates: data.certificates || [],
          paymentHistory: data.paymentHistory || [],
          preferences: data.preferences || {
            emailNotifications: true,
            courseUpdates: true,
            marketingEmails: false,
            language: 'en',
            timezone: 'Africa/Nairobi'
          },
          // Optional fields - only include if they exist
          ...(data.firstName && { firstName: data.firstName }),
          ...(data.lastName && { lastName: data.lastName }),
          ...(data.phoneNumber && { phoneNumber: data.phoneNumber }),
          ...(data.profilePictureUrl && { profilePictureUrl: data.profilePictureUrl }),
          ...(data.bio && { bio: data.bio }),
          ...(data.country && { country: data.country }),
          ...(data.profession && { profession: data.profession }),
          ...(data.interests && { interests: data.interests }),
          ...(data.socialLinks && { socialLinks: data.socialLinks }),
          ...(data.lastLoginAt && { lastLoginAt: (data.lastLoginAt as any)?.toDate?.() })
        };

        console.log('👤 Setting user profile:', { email: profile.email, role: profile.role });
        setUserProfile(profile);
        return profile;
      } else {
        console.log('⚠️ User profile not found, creating new profile');
        // Create profile if it doesn't exist
        await createUserProfile(user);
        return await loadUserProfile(user);
      }
    } catch (error) {
      console.error('❌ Error loading user profile:', error);
      setError('Failed to load user profile');
      return null;
    }
  };

  // Register with email and password
  const registerWithEmail = async (data: RegisterData): Promise<UserCredential> => {
    try {
      setError(null);
      console.log('🔐 Starting user registration for:', data.email);
      
      const result = await createUserWithEmailAndPassword(auth, data.email, data.password);
      console.log('✅ Firebase user created:', result.user.email);
      
      // Update Firebase Auth profile
      await updateProfile(result.user, {
        displayName: data.displayName
      });
      console.log('✅ Firebase Auth profile updated');

      // Send email verification
      await sendEmailVerification(result.user);
      console.log('✅ Email verification sent');

      // Determine role based on email
      const userRole = RoleService.getDefaultRole(data.email);

      // Create Firestore profile with only defined values
      const profileData: any = {
        role: userRole // Role based on email
      };

      console.log(`👤 Registering user with role: ${userRole} for email: ${data.email}`);

      if (data.displayName) profileData.displayName = data.displayName;
      if (data.firstName) profileData.firstName = data.firstName;
      if (data.lastName) profileData.lastName = data.lastName;

      await createUserProfile(result.user, profileData);
      console.log('✅ Firestore user profile created');

      console.log('✅ User registration completed successfully');
      return result;
    } catch (error: any) {
      console.error('❌ Registration error:', error);
      
      // Handle specific registration errors
      let errorMessage = 'Registration failed. Please try again.';
      
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'An account with this email already exists. Please sign in instead.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password is too weak. Please use at least 6 characters.';
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = 'Email/password registration is not enabled. Please contact support.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your connection and try again.';
      }
      
      setError(errorMessage);
      throw error;
    }
  };

  // Login with email and password
  const loginWithEmail = async (credentials: LoginCredentials): Promise<UserCredential> => {
    try {
      setError(null);
      console.log('🔐 Starting email/password login for:', credentials.email);

      const result = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);
      console.log('✅ Email/password login successful:', result.user.email);

      // Set the current user immediately to avoid "No user logged in" errors
      setCurrentUser(result.user);

      // Small delay to ensure auth state is updated
      await new Promise(resolve => setTimeout(resolve, 100));

      // Force load user profile immediately after login using the result.user
      console.log('🔄 Force loading user profile after login');
      await loadUserProfile(result.user);

      // Update last login time using the result.user directly
      console.log('🔄 Updating last login time');
      try {
        const userRef = doc(db, 'users', result.user.uid);
        const cleanedUpdates = cleanFirestoreData({
          lastLoginAt: new Date(),
          updatedAt: serverTimestamp()
        });
        await updateDoc(userRef, cleanedUpdates);
        console.log('✅ Last login time updated');
      } catch (updateError) {
        console.warn('⚠️ Failed to update last login time:', updateError);
        // Don't throw error for this non-critical operation
      }

      console.log('✅ Login process completed successfully');
      return result;
    } catch (error: any) {
      console.error('❌ Email/password login error:', error);

      // Handle specific email/password login errors
      let errorMessage = 'Login failed. Please try again.';

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address.';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect password. Please try again.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled. Please contact support.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many failed attempts. Please try again later.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      setError(errorMessage);
      throw error;
    }
  };

  // Login with Google
  const loginWithGoogle = async (): Promise<UserCredential> => {
    try {
      setError(null);
      console.log('🔐 Starting Google login...');
      console.log('🌐 Current URL:', window.location.href);
      console.log('🔧 Firebase Auth Domain:', auth.app.options.authDomain);

      // Check if Firebase is properly initialized
      if (!auth.app) {
        throw new Error('Firebase app not initialized');
      }

      if (!auth.app.options.apiKey) {
        throw new Error('Firebase API key not configured');
      }

      // Create Google Auth Provider
      const provider = new GoogleAuthProvider();

      // Add additional scopes if needed
      provider.addScope('email');
      provider.addScope('profile');

      // Set custom parameters for better popup experience
      provider.setCustomParameters({
        prompt: 'select_account',
        access_type: 'online',
        include_granted_scopes: 'true'
      });

      console.log('🚀 Attempting popup authentication...');

      // Sign in with popup
      const result = await signInWithPopup(auth, provider);

      console.log('✅ Google popup authentication successful');
      console.log('👤 Google user info:', {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL,
        emailVerified: result.user.emailVerified
      });
      console.log('👤 Google user info:', {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL
      });

      // The user profile will be created/updated automatically by the onAuthStateChanged listener
      // through the createOrUpdateUserProfile function

      // Update last login time
      try {
        const userRef = doc(db, 'users', result.user.uid);
        const cleanedUpdates = cleanFirestoreData({
          lastLoginAt: new Date(),
          updatedAt: serverTimestamp()
        });
        await updateDoc(userRef, cleanedUpdates);
        console.log('✅ Last login time updated for Google user');
      } catch (updateError) {
        console.warn('⚠️ Failed to update last login time for Google user:', updateError);
        // Don't throw error for this non-critical operation
      }

      console.log('✅ Google login process completed successfully');
      return result;
    } catch (error: any) {
      console.error('❌ Google login error:', error);

      // Handle specific Google login errors
      let errorMessage = 'Google login failed. Please try again.';

      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = 'Google login was cancelled. Please try again.';
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = 'Popup was blocked by your browser. Please allow popups and try again.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = 'An account already exists with the same email address but different sign-in credentials.';
      } else if (error.code === 'auth/cancelled-popup-request') {
        errorMessage = 'Google login was cancelled. Please try again.';
      }

      setError(errorMessage);
      throw error;
    }
  };

  // Logout
  const logout = async (): Promise<void> => {
    try {
      await signOut(auth);
      setUserProfile(null);
      console.log('✅ User logged out successfully');
    } catch (error: any) {
      console.error('❌ Logout error:', error);
      setError(error.message);
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string): Promise<void> => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
      console.log('✅ Password reset email sent');
    } catch (error: any) {
      console.error('❌ Password reset error:', error);
      setError(error.message);
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (updates: Partial<UserProfile>, userId?: string): Promise<void> => {
    const targetUserId = userId || currentUser?.uid;

    if (!targetUserId) {
      console.warn('⚠️ No user ID available for profile update');
      throw new Error('No user logged in');
    }

    try {
      const userRef = doc(db, 'users', targetUserId);

      // Clean the updates to remove undefined values
      const cleanedUpdates = cleanFirestoreData({
        ...updates,
        updatedAt: serverTimestamp()
      });

      await updateDoc(userRef, cleanedUpdates);

      // Update local state only if it's the current user
      if (targetUserId === currentUser?.uid && userProfile) {
        setUserProfile({ ...userProfile, ...updates, updatedAt: new Date() });
      }

      console.log('✅ User profile updated for:', targetUserId);
    } catch (error: any) {
      console.error('❌ Profile update error:', error);
      setError(error.message);
      throw error;
    }
  };

  // Refresh user profile
  const refreshUserProfile = async (): Promise<void> => {
    if (!currentUser) return;
    await loadUserProfile(currentUser);
  };

  // Role checking utilities
  const isAdmin = () => {
    // Check if user profile has admin role
    if (userProfile?.role === 'admin') {
      return true;
    }

    // Check if user email is a designated admin email
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (currentUser?.email && adminEmails.includes(currentUser.email.toLowerCase())) {
      return true;
    }

    // Check if email contains 'admin' (for development)
    if (currentUser?.email?.toLowerCase().includes('admin')) {
      return true;
    }

    return false;
  };

  const isUser = () => userProfile?.role === 'student';
  const hasRole = (role: UserRole) => userProfile?.role === role;



  // Auth state listener
  useEffect(() => {


    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('🔐 Auth state changed:', user ? `User: ${user.email}` : 'No user');
      setCurrentUser(user);
      setError(null);

      if (user) {
        console.log('👤 Loading user profile for:', user.email);
        // Load user profile
        try {
          await loadUserProfile(user);
          console.log('✅ User profile loaded successfully');
        } catch (error) {
          console.error('❌ Failed to load user profile:', error);
        }
      } else {
        console.log('👤 No user, clearing profile');
        setUserProfile(null);
      }

      setLoading(false);
      console.log('🔐 Auth loading complete');
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    currentUser,
    userProfile,
    loading,
    error,
    loginWithEmail,
    registerWithEmail,
    loginWithGoogle,
    logout,
    resetPassword,
    updateUserProfile,
    refreshUserProfile,
    isAdmin,
    isUser,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
