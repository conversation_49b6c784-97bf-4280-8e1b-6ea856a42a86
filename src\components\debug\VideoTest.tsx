import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Simple video test component to verify video URLs work
 */
export const VideoTest: React.FC = () => {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [customVideos, setCustomVideos] = useState<Array<{name: string, url: string, type: string}>>([]);

  // Test video URLs (static array)
  const defaultTestVideos = [
    {
      name: '<PERSON> <PERSON> (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'video/mp4'
    },
    {
      name: 'Elephants Dream (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      type: 'video/mp4'
    },
    {
      name: 'For Bigger Blazes (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      type: 'video/mp4'
    },
    {
      name: 'Sample WebM',
      url: 'https://www.w3schools.com/html/mov_bbb.mp4',
      type: 'video/mp4'
    }
  ];

  // Combine default and custom videos
  const testVideos = [...defaultTestVideos, ...customVideos];

  // Safe access to current video with fallback
  const currentVideo = testVideos[currentVideoIndex] || defaultTestVideos[0];

  // Additional safety check
  if (!currentVideo) {
    return (
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-6">
          <div className="text-center text-white">
            <p>Error: No video data available</p>
            <Button onClick={() => setCurrentVideoIndex(0)} className="mt-2">
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">🎥 Video Player Test</CardTitle>
        <p className="text-gray-400 text-sm">
          Test different video URLs to verify the video player is working correctly.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Selection */}
        <div className="flex flex-wrap gap-2">
          {testVideos.map((video, index) => (
            <Button
              key={`${video.name}-${index}`}
              variant={currentVideoIndex === index ? "default" : "outline"}
              size="sm"
              onClick={() => {
                if (index >= 0 && index < testVideos.length) {
                  setCurrentVideoIndex(index);
                }
              }}
              className={currentVideoIndex === index ? "bg-blue-600" : ""}
            >
              {video?.name || `Video ${index + 1}`}
            </Button>
          ))}
        </div>
        
        {/* Current Video Info */}
        <div className="bg-gray-900 p-3 rounded-lg">
          <p className="text-white font-medium">{currentVideo?.name || 'Unknown Video'}</p>
          <p className="text-gray-400 text-sm break-all">{currentVideo?.url || 'No URL'}</p>
          <p className="text-gray-500 text-xs">Type: {currentVideo?.type || 'Unknown'}</p>
          <p className="text-gray-500 text-xs">Index: {currentVideoIndex} / {testVideos.length - 1}</p>
        </div>
        
        {/* Video Player */}
        <div className="bg-black rounded-lg overflow-hidden">
          {currentVideo?.url ? (
            <video
              key={currentVideo.url}
              className="w-full h-64 object-contain"
              controls
              preload="metadata"
              playsInline
              onLoadStart={() => console.log('🔄 Test video loading:', currentVideo.url)}
              onLoadedData={() => console.log('✅ Test video loaded:', currentVideo.name)}
              onError={(e) => {
                console.error('❌ Test video error:', currentVideo.url);
                const videoElement = e.target as HTMLVideoElement;
                if (videoElement.error) {
                  console.error('❌ Error details:', videoElement.error.code, videoElement.error.message);
                }
              }}
              onCanPlay={() => console.log('✅ Test video can play:', currentVideo.name)}
            >
              <source src={currentVideo.url} type={currentVideo.type || 'video/mp4'} />
              <p className="text-white p-4">
                Your browser does not support the video tag.
                <br />
                <a
                  href={currentVideo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 underline"
                >
                  Open video in new tab
                </a>
              </p>
            </video>
          ) : (
            <div className="w-full h-64 flex items-center justify-center text-white">
              <div className="text-center">
                <p className="text-xl mb-2">⚠️</p>
                <p>No video URL available</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Test Results */}
        <div className="bg-gray-900 p-3 rounded-lg">
          <h4 className="text-white font-medium mb-2">Test Instructions:</h4>
          <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
            <li>Click different video buttons above</li>
            <li>Check if videos load and play</li>
            <li>Open browser console (F12) to see debug logs</li>
            <li>If videos work here but not in course player, the issue is in the course data flow</li>
            <li>If videos don't work here, the issue is with the video player or network</li>
          </ol>
        </div>
        
        {/* Manual URL Test */}
        <div className="bg-gray-900 p-3 rounded-lg">
          <h4 className="text-white font-medium mb-2">Manual URL Test:</h4>
          <input
            type="url"
            placeholder="Enter video URL to test..."
            className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const url = (e.target as HTMLInputElement).value.trim();
                if (url) {
                  const customVideo = {
                    name: `Custom URL ${customVideos.length + 1}`,
                    url,
                    type: 'video/mp4'
                  };
                  const newCustomVideos = [...customVideos, customVideo];
                  setCustomVideos(newCustomVideos);
                  setCurrentVideoIndex(defaultTestVideos.length + newCustomVideos.length - 1);
                  (e.target as HTMLInputElement).value = ''; // Clear input
                }
              }
            }}
          />
          <p className="text-gray-500 text-xs mt-1">Press Enter to test the URL</p>
        </div>
      </CardContent>
    </Card>
  );
};
