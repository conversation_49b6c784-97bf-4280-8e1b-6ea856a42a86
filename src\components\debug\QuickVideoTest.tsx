import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ReactPlayer from 'react-player';
import { Play, Pause, RotateCcw, ExternalLink, CheckCircle, XCircle, Clock } from 'lucide-react';

export const QuickVideoTest: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [testResults, setTestResults] = useState<any>({});
  const [testing, setTesting] = useState(false);
  const [playerState, setPlayerState] = useState({
    ready: false,
    playing: false,
    error: false,
    canPlay: false,
    duration: 0
  });

  const playerRef = useRef<ReactPlayer>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const quickTestUrls = [
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://vimeo.com/90509568'
  ];

  const resetPlayerState = () => {
    setPlayerState({
      ready: false,
      playing: false,
      error: false,
      canPlay: false,
      duration: 0
    });
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const testVideoUrl = async (url: string) => {
    if (!url.trim()) return;
    
    setTesting(true);
    resetPlayerState();
    
    const results: any = {
      url: url,
      timestamp: new Date().toLocaleTimeString(),
      reactPlayerCanPlay: false,
      events: [],
      finalStatus: 'testing'
    };

    try {
      // Test 1: ReactPlayer compatibility
      results.reactPlayerCanPlay = ReactPlayer.canPlay(url);
      results.events.push(`ReactPlayer.canPlay: ${results.reactPlayerCanPlay}`);
      
      // Test 2: URL accessibility
      try {
        const response = await fetch(url, { method: 'HEAD' });
        results.urlAccessible = response.ok;
        results.events.push(`URL accessible: ${response.ok} (${response.status})`);
      } catch (error) {
        results.urlAccessible = false;
        results.events.push(`URL error: ${error.message}`);
      }

      // Set a timeout for the player test
      timeoutRef.current = setTimeout(() => {
        if (!playerState.ready) {
          results.events.push('Player timeout after 10 seconds');
          results.finalStatus = 'timeout';
          setTestResults(results);
          setTesting(false);
        }
      }, 10000);

    } catch (error) {
      results.events.push(`Test error: ${error.message}`);
      results.finalStatus = 'error';
      setTestResults(results);
      setTesting(false);
    }

    setTestResults(results);
  };

  // Monitor player state changes
  useEffect(() => {
    if (playerState.ready && testing) {
      const updatedResults = {
        ...testResults,
        events: [...testResults.events, 'Player ready successfully'],
        finalStatus: 'success',
        duration: playerState.duration
      };
      setTestResults(updatedResults);
      setTesting(false);
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }
  }, [playerState.ready]);

  useEffect(() => {
    if (playerState.error && testing) {
      const updatedResults = {
        ...testResults,
        events: [...testResults.events, 'Player error occurred'],
        finalStatus: 'error'
      };
      setTestResults(updatedResults);
      setTesting(false);
    }
  }, [playerState.error]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-400" />;
      case 'timeout':
        return <Clock className="w-5 h-5 text-yellow-400" />;
      default:
        return <Clock className="w-5 h-5 text-blue-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-500 bg-green-900/20';
      case 'error':
        return 'border-red-500 bg-red-900/20';
      case 'timeout':
        return 'border-yellow-500 bg-yellow-900/20';
      default:
        return 'border-blue-500 bg-blue-900/20';
    }
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">⚡ Quick Video Test</CardTitle>
        <p className="text-gray-400 text-sm">
          Instantly test if a video URL will work in your player
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        
        {/* URL Input */}
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="Paste video URL here"
              className="bg-gray-700 border-gray-600 text-white"
              onKeyPress={(e) => e.key === 'Enter' && testVideoUrl(testUrl)}
            />
            <Button 
              onClick={() => testVideoUrl(testUrl)}
              disabled={testing || !testUrl.trim()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {testing ? 'Testing...' : 'Test'}
            </Button>
          </div>
          
          {/* Quick URLs */}
          <div className="flex flex-wrap gap-2">
            {quickTestUrls.map((url, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => {
                  setTestUrl(url);
                  testVideoUrl(url);
                }}
                className="border-gray-600 text-gray-300 hover:bg-gray-700 text-xs"
              >
                Test {index + 1}
              </Button>
            ))}
          </div>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className={`border rounded-lg p-4 ${getStatusColor(testResults.finalStatus)}`}>
            <div className="flex items-center gap-2 mb-3">
              {getStatusIcon(testResults.finalStatus)}
              <span className="font-medium text-white">
                Test Result: {testResults.finalStatus.toUpperCase()}
              </span>
              <span className="text-gray-400 text-sm ml-auto">
                {testResults.timestamp}
              </span>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm">
                <span className="text-gray-400">URL:</span>
                <span className="text-white ml-2 break-all">{testResults.url}</span>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-400">ReactPlayer Support:</span>
                <span className={`ml-2 ${testResults.reactPlayerCanPlay ? 'text-green-400' : 'text-red-400'}`}>
                  {testResults.reactPlayerCanPlay ? '✅ Yes' : '❌ No'}
                </span>
              </div>
              
              {testResults.duration > 0 && (
                <div className="text-sm">
                  <span className="text-gray-400">Duration:</span>
                  <span className="text-white ml-2">{Math.round(testResults.duration)} seconds</span>
                </div>
              )}
              
              <div className="text-sm">
                <span className="text-gray-400">Events:</span>
                <div className="mt-1 space-y-1">
                  {testResults.events.map((event: string, index: number) => (
                    <div key={index} className="text-gray-300 text-xs bg-gray-900/50 p-1 rounded">
                      {event}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hidden Player for Testing */}
        {testUrl && testing && (
          <div className="hidden">
            <ReactPlayer
              ref={playerRef}
              url={testUrl}
              width="100%"
              height="200px"
              onReady={() => {
                console.log('✅ Test player ready');
                setPlayerState(prev => ({ ...prev, ready: true }));
              }}
              onError={(error) => {
                console.error('❌ Test player error:', error);
                setPlayerState(prev => ({ ...prev, error: true }));
              }}
              onDuration={(duration) => {
                console.log('📏 Test player duration:', duration);
                setPlayerState(prev => ({ ...prev, duration }));
              }}
              onCanPlay={() => {
                console.log('✅ Test player can play');
                setPlayerState(prev => ({ ...prev, canPlay: true }));
              }}
            />
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-900 p-3 rounded-lg">
          <h4 className="text-white font-medium mb-2">How to Use</h4>
          <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
            <li>Paste your video URL in the input field</li>
            <li>Click "Test" or press Enter</li>
            <li>Wait for the test results (max 10 seconds)</li>
            <li>If successful, the URL should work in your course player</li>
            <li>If failed, try the suggested fixes or use a different URL</li>
          </ol>
        </div>

        {/* Recommendations */}
        <div className="bg-gray-900 p-3 rounded-lg">
          <h4 className="text-white font-medium mb-2">Recommendations</h4>
          <div className="text-gray-300 text-sm space-y-1">
            <div>✅ <strong>Best:</strong> Direct MP4 files with HTTPS</div>
            <div>✅ <strong>Good:</strong> YouTube watch URLs (not embed)</div>
            <div>✅ <strong>Good:</strong> Vimeo direct URLs</div>
            <div>❌ <strong>Avoid:</strong> URLs requiring authentication</div>
            <div>❌ <strong>Avoid:</strong> HTTP URLs (use HTTPS)</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickVideoTest;
