/**
 * New Enrollment Management Component
 * Handles both paid and manual enrollments with the new system
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  UserPlus,
  Search,
  Trash2,
  Eye,
  Calendar,
  BookOpen,
  Users,
  Loader2,
  CreditCard,
  UserCheck,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';

import { enrollmentService } from '@/services/enrollmentService';
import { courseService } from '@/services/courseService';
import { useAuth } from '@/contexts/AuthContext';
import { Course } from '@/types/course';
import { Enrollment } from '@/types/schema';

interface NewEnrollmentManagementProps {
  className?: string;
}

export const NewEnrollmentManagement: React.FC<NewEnrollmentManagementProps> = ({ className }) => {
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  
  // Manual enrollment modal state
  const [showManualEnrollModal, setShowManualEnrollModal] = useState(false);
  const [manualEnrollLoading, setManualEnrollLoading] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [selectedCourseForEnroll, setSelectedCourseForEnroll] = useState('');
  const [adminNote, setAdminNote] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load courses
      const coursesData = await courseService.getAllCourses();
      setCourses(coursesData);
      
      // Load all enrollments (this would need to be implemented in the service)
      // For now, we'll load enrollments for each course
      const allEnrollments: Enrollment[] = [];
      for (const course of coursesData) {
        const courseEnrollments = await enrollmentService.getCourseEnrollments(course.id);
        allEnrollments.push(...courseEnrollments);
      }
      
      setEnrollments(allEnrollments);
    } catch (error: any) {
      console.error('❌ Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load enrollment data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManualEnroll = async () => {
    if (!userEmail || !selectedCourseForEnroll) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setManualEnrollLoading(true);

      await enrollmentService.createManualEnrollment({
        userId: userEmail, // Using email as userId for manual enrollments
        courseId: selectedCourseForEnroll,
        enrollmentType: 'manual',
        adminNote: adminNote || 'Manually enrolled by admin',
        enrolledBy: currentUser?.uid,
      });

      toast({
        title: "Success",
        description: "User enrolled successfully",
      });

      // Reset form and reload data
      setUserEmail('');
      setSelectedCourseForEnroll('');
      setAdminNote('');
      setShowManualEnrollModal(false);
      loadData();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to enroll user",
        variant: "destructive",
      });
    } finally {
      setManualEnrollLoading(false);
    }
  };

  const getStatusBadge = (enrollment: Enrollment) => {
    const statusColors = {
      active: 'bg-green-600',
      suspended: 'bg-yellow-600',
      completed: 'bg-blue-600',
    };

    return (
      <Badge className={`${statusColors[enrollment.status]} text-white`}>
        {enrollment.status}
      </Badge>
    );
  };

  const getTypeBadge = (enrollment: Enrollment) => {
    return enrollment.enrollmentType === 'paid' ? (
      <Badge className="bg-blue-600 text-white">
        <CreditCard className="h-3 w-3 mr-1" />
        Paid
      </Badge>
    ) : (
      <Badge className="bg-gray-600 text-white">
        <UserCheck className="h-3 w-3 mr-1" />
        Manual
      </Badge>
    );
  };

  const filteredEnrollments = enrollments.filter(enrollment => {
    const matchesSearch = enrollment.userId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse = selectedCourse === 'all' || enrollment.courseId === selectedCourse;
    const matchesType = selectedType === 'all' || enrollment.enrollmentType === selectedType;
    
    return matchesSearch && matchesCourse && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Enrollment Management</h2>
          <p className="text-gray-400">Manage course enrollments and payments</p>
        </div>
        
        <Dialog open={showManualEnrollModal} onOpenChange={setShowManualEnrollModal}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Manual Enroll
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-gray-800 border-gray-700">
            <DialogHeader>
              <DialogTitle className="text-white">Manual Enrollment</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="userEmail" className="text-white">User Email</Label>
                <Input
                  id="userEmail"
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              
              <div>
                <Label htmlFor="course" className="text-white">Course</Label>
                <Select value={selectedCourseForEnroll} onValueChange={setSelectedCourseForEnroll}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue placeholder="Select a course" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    {courses.map((course) => (
                      <SelectItem key={course.id} value={course.id} className="text-white">
                        {course.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="adminNote" className="text-white">Admin Note (Optional)</Label>
                <Textarea
                  id="adminNote"
                  value={adminNote}
                  onChange={(e) => setAdminNote(e.target.value)}
                  placeholder="Reason for manual enrollment..."
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              
              <Button
                onClick={handleManualEnroll}
                disabled={manualEnrollLoading}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {manualEnrollLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Enrolling...
                  </>
                ) : (
                  'Enroll User'
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label className="text-white">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by user email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-gray-700 border-gray-600 text-white"
                />
              </div>
            </div>
            
            <div>
              <Label className="text-white">Course</Label>
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="all" className="text-white">All Courses</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id} className="text-white">
                      {course.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label className="text-white">Type</Label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="all" className="text-white">All Types</SelectItem>
                  <SelectItem value="paid" className="text-white">Paid</SelectItem>
                  <SelectItem value="manual" className="text-white">Manual</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button onClick={loadData} variant="outline" className="border-gray-600 text-gray-300">
                <Loader2 className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enrollments Table */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Enrollments ({filteredEnrollments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredEnrollments.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No enrollments found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300">User</th>
                    <th className="text-left py-3 px-4 text-gray-300">Course</th>
                    <th className="text-left py-3 px-4 text-gray-300">Type</th>
                    <th className="text-left py-3 px-4 text-gray-300">Status</th>
                    <th className="text-left py-3 px-4 text-gray-300">Progress</th>
                    <th className="text-left py-3 px-4 text-gray-300">Enrolled</th>
                    <th className="text-left py-3 px-4 text-gray-300">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEnrollments.map((enrollment) => {
                    const course = courses.find(c => c.id === enrollment.courseId);
                    return (
                      <tr key={enrollment.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                        <td className="py-3 px-4 text-white">{enrollment.userId}</td>
                        <td className="py-3 px-4 text-white">{course?.title || 'Unknown Course'}</td>
                        <td className="py-3 px-4">{getTypeBadge(enrollment)}</td>
                        <td className="py-3 px-4">{getStatusBadge(enrollment)}</td>
                        <td className="py-3 px-4 text-white">{enrollment.progress.progressPercentage}%</td>
                        <td className="py-3 px-4 text-gray-400">
                          {enrollment.enrolledAt.toDate().toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" className="border-gray-600">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="border-gray-600 text-red-400">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
