/**
 * FreeCodeLap Platform - User Management Modals
 * 
 * Modal components for user creation, editing, viewing, and bulk operations
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Shield, 
  BookOpen,
  Award,
  CreditCard,
  Settings,
  Save,
  X
} from 'lucide-react';
import { UserProfile, UserRole } from '@/types/user';
import { userManagementService, BulkUserOperation } from '@/services/userManagementService';
import { RoleBadge } from '@/components/auth/RoleBasedAccess';

// Create User Modal
interface CreateUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserCreated: () => void;
}

export const CreateUserModal: React.FC<CreateUserModalProps> = ({
  isOpen,
  onClose,
  onUserCreated
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    displayName: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    role: 'student' as UserRole
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password || !formData.displayName) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      await userManagementService.createUser(formData);
      
      toast({
        title: "Success",
        description: "User created successfully.",
      });
      
      onUserCreated();
      onClose();
      
      // Reset form
      setFormData({
        email: '',
        password: '',
        displayName: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        role: 'student'
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create user.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Create New User
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" className="text-gray-300">First Name</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                className="bg-gray-700 border-gray-600 text-white"
                placeholder="John"
              />
            </div>
            <div>
              <Label htmlFor="lastName" className="text-gray-300">Last Name</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                className="bg-gray-700 border-gray-600 text-white"
                placeholder="Doe"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="displayName" className="text-gray-300">Display Name *</Label>
            <Input
              id="displayName"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="John Doe"
              required
            />
          </div>

          <div>
            <Label htmlFor="email" className="text-gray-300">Email *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <Label htmlFor="password" className="text-gray-300">Password *</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="••••••••"
              required
              minLength={6}
            />
          </div>

          <div>
            <Label htmlFor="phoneNumber" className="text-gray-300">Phone Number</Label>
            <Input
              id="phoneNumber"
              value={formData.phoneNumber}
              onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="+254 700 000 000"
            />
          </div>

          <div>
            <Label htmlFor="role" className="text-gray-300">Role *</Label>
            <Select value={formData.role} onValueChange={(value) => 
              setFormData(prev => ({ ...prev, role: value as UserRole }))
            }>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="student">Student</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Creating...' : 'Create User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

// Edit User Modal
interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated: () => void;
  user: UserProfile | null;
}

export const EditUserModal: React.FC<EditUserModalProps> = ({
  isOpen,
  onClose,
  onUserUpdated,
  user
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    bio: '',
    role: 'student' as UserRole
  });

  useEffect(() => {
    if (user) {
      setFormData({
        displayName: user.displayName || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phoneNumber: user.phoneNumber || '',
        bio: user.bio || '',
        role: user.role
      });
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !formData.displayName) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      // Update user profile
      await userManagementService.updateUser(user.uid, formData);
      
      // Update role if changed
      if (formData.role !== user.role) {
        await userManagementService.changeUserRole(user.uid, formData.role);
      }
      
      toast({
        title: "Success",
        description: "User updated successfully.",
      });
      
      onUserUpdated();
      onClose();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update user.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Edit User: {user.displayName}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="editFirstName" className="text-gray-300">First Name</Label>
              <Input
                id="editFirstName"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <div>
              <Label htmlFor="editLastName" className="text-gray-300">Last Name</Label>
              <Input
                id="editLastName"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="editDisplayName" className="text-gray-300">Display Name *</Label>
            <Input
              id="editDisplayName"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              required
            />
          </div>

          <div>
            <Label htmlFor="editPhoneNumber" className="text-gray-300">Phone Number</Label>
            <Input
              id="editPhoneNumber"
              value={formData.phoneNumber}
              onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>

          <div>
            <Label htmlFor="editBio" className="text-gray-300">Bio</Label>
            <Textarea
              id="editBio"
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
              className="bg-gray-700 border-gray-600 text-white"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="editRole" className="text-gray-300">Role *</Label>
            <Select value={formData.role} onValueChange={(value) => 
              setFormData(prev => ({ ...prev, role: value as UserRole }))
            }>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="student">Student</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Updating...' : 'Update User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

// User Details Modal
interface UserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserProfile | null;
}

export const UserDetailsModal: React.FC<UserDetailsModalProps> = ({
  isOpen,
  onClose,
  user
}) => {
  if (!user) return null;

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            User Details: {user.displayName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <User className="h-4 w-4 mr-2" />
                Basic Information
              </h3>

              <div className="space-y-2">
                <div>
                  <Label className="text-gray-400">Display Name</Label>
                  <p className="text-white">{user.displayName}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Email</Label>
                  <p className="text-white">{user.email}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Role</Label>
                  <div className="mt-1">
                    <RoleBadge role={user.role} />
                  </div>
                </div>

                {user.phoneNumber && (
                  <div>
                    <Label className="text-gray-400">Phone</Label>
                    <p className="text-white">{user.phoneNumber}</p>
                  </div>
                )}

                {user.bio && (
                  <div>
                    <Label className="text-gray-400">Bio</Label>
                    <p className="text-white">{user.bio}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Account Information
              </h3>

              <div className="space-y-2">
                <div>
                  <Label className="text-gray-400">Created</Label>
                  <p className="text-white">{formatDate(user.createdAt)}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Last Updated</Label>
                  <p className="text-white">{formatDate(user.updatedAt)}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Last Login</Label>
                  <p className="text-white">{formatDate(user.lastLoginAt)}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Subscription Status</Label>
                  <Badge className={`mt-1 ${
                    user.subscriptionStatus === 'active' ? 'bg-green-600' : 'bg-gray-600'
                  }`}>
                    {user.subscriptionStatus || 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Course Information */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                Course Progress
              </h3>

              <div className="space-y-2">
                <div>
                  <Label className="text-gray-400">Enrolled Courses</Label>
                  <p className="text-white text-2xl font-bold">{user.enrolledCourses.length}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Completed Courses</Label>
                  <p className="text-white text-2xl font-bold">{user.completedCourses.length}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Certificates Earned</Label>
                  <p className="text-white text-2xl font-bold">{user.certificates.length}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Preferences
              </h3>

              <div className="space-y-2">
                <div>
                  <Label className="text-gray-400">Email Notifications</Label>
                  <Badge className={user.preferences.emailNotifications ? 'bg-green-600' : 'bg-red-600'}>
                    {user.preferences.emailNotifications ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>

                <div>
                  <Label className="text-gray-400">Course Updates</Label>
                  <Badge className={user.preferences.courseUpdates ? 'bg-green-600' : 'bg-red-600'}>
                    {user.preferences.courseUpdates ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>

                <div>
                  <Label className="text-gray-400">Marketing Emails</Label>
                  <Badge className={user.preferences.marketingEmails ? 'bg-green-600' : 'bg-red-600'}>
                    {user.preferences.marketingEmails ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>

                <div>
                  <Label className="text-gray-400">Language</Label>
                  <p className="text-white">{user.preferences.language.toUpperCase()}</p>
                </div>

                <div>
                  <Label className="text-gray-400">Timezone</Label>
                  <p className="text-white">{user.preferences.timezone}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            onClick={onClose}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Bulk Operations Modal
interface BulkOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedUserIds: string[];
  onOperationComplete: () => void;
}

export const BulkOperationsModal: React.FC<BulkOperationsModalProps> = ({
  isOpen,
  onClose,
  selectedUserIds,
  onOperationComplete
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [operation, setOperation] = useState<string>('');
  const [newRole, setNewRole] = useState<UserRole>('student');

  const handleBulkOperation = async () => {
    if (!operation) {
      toast({
        title: "Error",
        description: "Please select an operation.",
        variant: "destructive",
      });
      return;
    }

    const confirmMessage = `Are you sure you want to ${operation} ${selectedUserIds.length} user(s)?`;
    if (!confirm(confirmMessage)) return;

    try {
      setLoading(true);

      const bulkOperation: BulkUserOperation = {
        userIds: selectedUserIds,
        operation: operation as any,
        data: operation === 'changeRole' ? { role: newRole } : undefined
      };

      await userManagementService.performBulkOperation(bulkOperation);

      toast({
        title: "Success",
        description: `Bulk ${operation} completed successfully.`,
      });

      onOperationComplete();
      onClose();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to perform bulk ${operation}.`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Bulk Operations
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-gray-300">
            Selected {selectedUserIds.length} user(s) for bulk operation.
          </p>

          <div>
            <Label className="text-gray-300">Operation</Label>
            <Select value={operation} onValueChange={setOperation}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Select operation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="activate">Activate Users</SelectItem>
                <SelectItem value="suspend">Suspend Users</SelectItem>
                <SelectItem value="changeRole">Change Role</SelectItem>
                <SelectItem value="delete">Delete Users</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {operation === 'changeRole' && (
            <div>
              <Label className="text-gray-300">New Role</Label>
              <Select value={newRole} onValueChange={(value) => setNewRole(value as UserRole)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="student">Student</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleBulkOperation}
            disabled={loading || !operation}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? 'Processing...' : 'Execute Operation'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
