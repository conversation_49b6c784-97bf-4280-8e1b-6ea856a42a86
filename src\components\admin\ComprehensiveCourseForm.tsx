import React, { useState, useEffect, useCallback } from 'react';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';
import { prepareCourseForFirestore, sanitizeDocumentId } from '@/utils/firestoreUtils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Save,
  Loader2,
  BookOpen,
  AlertCircle,
  CheckCircle,
  Plus,
  Trash2,
  User,
  Target,
  GraduationCap,
  List,
  Settings,
  Eye,
  Clock,
  DollarSign,
  Star,
  FileText,
  Tag,
  Image,
  Video,
  Award,
  Lock,
  Unlock
} from 'lucide-react';

interface ComprehensiveCourseFormProps {
  isEditing?: boolean;
  courseId?: string | null;
  onCourseCreated?: () => void;
}

export const ComprehensiveCourseForm: React.FC<ComprehensiveCourseFormProps> = ({ isEditing = false, courseId, onCourseCreated }) => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('basic');
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Basic Course Information
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    shortDescription: '',
    category: '',
    level: 'Beginner' as 'Beginner' | 'Intermediate' | 'Advanced',
    price: 0,
    originalPrice: 0,
    duration: '',
    thumbnail: '',
    videoUrl: '',
    tags: [] as string[],
    featured: false,
    isPublished: true,
    allowComments: true,
    allowDownloads: true,
    certificateEnabled: true
  });

  // What You'll Learn
  const [learningOutcomes, setLearningOutcomes] = useState<string[]>(['']);

  // Target Audience
  const [targetAudience, setTargetAudience] = useState<string[]>(['']);

  // Prerequisites
  const [prerequisites, setPrerequisites] = useState<string[]>(['']);

  // New tag input
  const [newTag, setNewTag] = useState<string>('');

  // Course Features
  const [courseFeatures, setCourseFeatures] = useState({
    totalDuration: 480, // in minutes
    moduleCount: 1,
    certificateEnabled: true,
    lifetimeAccess: true,
    downloadableResources: true,
    mobileAccess: true,
    assignments: true,
    quizzes: true
  });

  // Course Modules
  const [modules, setModules] = useState([{
    id: '1',
    title: 'Getting Started',
    description: '',
    duration: 30, // in minutes
    lessons: [{
      id: '1',
      title: 'Introduction',
      duration: 15,
      videoUrl: '',
      description: '',
      resources: [],
      isFree: true // First lesson is free by default
    }, {
      id: '2',
      title: 'Setup',
      duration: 15,
      videoUrl: '',
      description: '',
      resources: [],
      isFree: false // Other lessons are locked by default
    }]
  }]);

  // Instructor Information
  const [instructor, setInstructor] = useState({
    name: 'Ahmed Takal',
    bio: 'Expert instructor with years of experience in technology education and software development.',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    expertise: ['FlutterFlow', 'No-Code Development', 'Mobile Apps', 'Web Development'],
    experience: '5+ years',
    credentials: ['Certified FlutterFlow Developer', 'Google Developer Expert'],
    stats: {
      rating: 4.9,
      reviews: 2847,
      students: 15432,
      courses: 12
    }
  });

  const categories = [
    'No-Code Development',
    'AI Development', 
    'Web Development',
    'Mobile Development',
    'Data Science',
    'Machine Learning',
    'Backend Development',
    'Frontend Development',
    'DevOps',
    'Cybersecurity',
    'UI/UX Design',
    'Digital Marketing',
    'Business',
    'Programming Fundamentals'
  ];

  const loadCourse = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Loading course for editing:', courseId);
      const course = await courseService.getCourseWithModules(courseId!);

      if (course) {


        // Load basic course data
        // Ensure category matches available options
        const courseCategory = course.category || '';
        const validCategory = categories.includes(courseCategory) ? courseCategory : '';





        setFormData({
          title: course.title || '',
          description: course.description || '',
          shortDescription: course.shortDescription || '',
          category: validCategory,
          level: (course.level || 'Beginner') as 'Beginner' | 'Intermediate' | 'Advanced',
          price: course.price || 0,
          originalPrice: course.originalPrice || course.price || 0,
          duration: course.duration?.toString() || '',
          thumbnail: course.thumbnail || '',
          videoUrl: course.videoUrl || '',
          tags: course.tags || [],
          featured: course.featured || false,
          isPublished: course.isPublished !== undefined ? course.isPublished : true,
          allowComments: course.allowComments !== undefined ? course.allowComments : true,
          allowDownloads: course.allowDownloads !== undefined ? course.allowDownloads : true,
          certificateEnabled: course.certificateEnabled !== undefined ? course.certificateEnabled : true
        });

        // Load learning outcomes
        setLearningOutcomes(course.learningOutcomes && course.learningOutcomes.length > 0 ? course.learningOutcomes : ['']);

        // Load target audience
        setTargetAudience(course.targetAudience && course.targetAudience.length > 0 ? course.targetAudience : ['']);

        // Load prerequisites
        setPrerequisites(course.requirements && course.requirements.length > 0 ? course.requirements : ['']);

        // Load course features with defaults
        setCourseFeatures({
          totalDuration: course.totalDuration || 480,
          moduleCount: course.modules?.length || 1,
          certificateEnabled: course.certificateEnabled !== undefined ? course.certificateEnabled : true,
          lifetimeAccess: true,
          downloadableResources: course.allowDownloads !== undefined ? course.allowDownloads : true,
          mobileAccess: true,
          assignments: true,
          quizzes: true
        });

        // Load modules - convert CourseModule to expected format
        if (course.modules && course.modules.length > 0) {
          const convertedModules = course.modules.map(module => ({
            id: module.id,
            title: module.title,
            description: module.description,
            duration: module.lessons?.reduce((total, lesson) => total + lesson.duration, 0) || 0,
            lessons: module.lessons?.map(lesson => ({
              id: lesson.id,
              title: lesson.title,
              duration: lesson.duration,
              videoUrl: lesson.videoUrl,
              description: lesson.description,
              resources: lesson.resources || [],
              isFree: (lesson as any).isFree || false
            })) || []
          }));
          setModules(convertedModules);
        }

        // Load instructor information
        setInstructor({
          name: course.instructor || 'Ahmed Takal',
          bio: course.instructorBio || 'Expert instructor with years of experience in technology education and software development.',
          avatar: course.instructorAvatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          expertise: ['FlutterFlow', 'No-Code Development', 'Mobile Apps', 'Web Development'],
          experience: '5+ years',
          credentials: ['Certified FlutterFlow Developer', 'Google Developer Expert'],
          stats: {
            rating: 4.9,
            reviews: 2847,
            students: 15432,
            courses: 12
          }
        });

        console.log('✅ Course loaded successfully for editing');
      } else {
        setError('Course not found');
      }
    } catch (error) {
      console.error('❌ Error loading course:', error);
      setError('Failed to load course data');
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  // Load course data for editing
  useEffect(() => {
    console.log('🔍 useEffect triggered - isEditing:', isEditing, 'courseId:', courseId);
    if (isEditing && courseId) {
      loadCourse();
    }
  }, [isEditing, courseId, loadCourse]);

  // Helper functions for dynamic arrays
  const addArrayItem = (setter: React.Dispatch<React.SetStateAction<string[]>>) => {
    setter(prev => [...prev, '']);
  };

  const removeArrayItem = (setter: React.Dispatch<React.SetStateAction<string[]>>, index: number) => {
    setter(prev => prev.filter((_, i) => i !== index));
  };

  const updateArrayItem = (setter: React.Dispatch<React.SetStateAction<string[]>>, index: number, value: string) => {
    setter(prev => prev.map((item, i) => i === index ? value : item));
  };

  // Module management functions
  const addModule = () => {
    const newModule = {
      id: Date.now().toString(),
      title: '',
      description: '',
      duration: 30,
      lessons: [{
        id: Date.now().toString() + '_1',
        title: '',
        duration: 15,
        videoUrl: '',
        description: '',
        resources: [],
        isFree: false
      }]
    };
    setModules(prev => [...prev, newModule]);
  };

  const removeModule = (moduleIndex: number) => {
    setModules(prev => prev.filter((_, i) => i !== moduleIndex));
  };

  const updateModule = (moduleIndex: number, field: string, value: any) => {
    setModules(prev => prev.map((module, i) =>
      i === moduleIndex ? { ...module, [field]: value } : module
    ));
  };

  const addLesson = (moduleIndex: number) => {
    const newLesson = {
      id: Date.now().toString(),
      title: '',
      duration: 15,
      videoUrl: '',
      description: '',
      resources: [],
      isFree: false // New lessons are locked by default
    };
    setModules(prev => prev.map((module, i) =>
      i === moduleIndex
        ? { ...module, lessons: [...module.lessons, newLesson] }
        : module
    ));
  };

  const removeLesson = (moduleIndex: number, lessonIndex: number) => {
    setModules(prev => prev.map((module, i) =>
      i === moduleIndex
        ? { ...module, lessons: module.lessons.filter((_, j) => j !== lessonIndex) }
        : module
    ));
  };

  const updateLesson = (moduleIndex: number, lessonIndex: number, field: string, value: any) => {
    setModules(prev => prev.map((module, i) =>
      i === moduleIndex
        ? {
            ...module,
            lessons: module.lessons.map((lesson, j) =>
              j === lessonIndex ? { ...lesson, [field]: value } : lesson
            )
          }
        : module
    ));
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFeatureChange = (field: string, value: any) => {
    setCourseFeatures(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTagAdd = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }));
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.title.trim()) return 'Course title is required';
    if (!formData.description.trim()) return 'Course description is required';
    if (!formData.category.trim()) return 'Course category is required';
    if (learningOutcomes.filter(outcome => outcome.trim()).length === 0) return 'At least one learning outcome is required';
    if (!instructor.bio.trim()) return 'Instructor bio is required';
    if (modules.length === 0) return 'At least one module is required';

    // Validate modules
    for (let i = 0; i < modules.length; i++) {
      const module = modules[i];
      if (!module.title.trim()) return `Module ${i + 1} title is required`;
      if (module.lessons.length === 0) return `Module ${i + 1} must have at least one lesson`;

      // Validate lessons
      for (let j = 0; j < module.lessons.length; j++) {
        const lesson = module.lessons[j];
        if (!lesson.title.trim()) return `Module ${i + 1}, Lesson ${j + 1} title is required`;
      }
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Use existing courseId for editing, or generate new one for creating
      const targetCourseId = isEditing ? courseId! : sanitizeDocumentId(formData.title);

      // Calculate total duration from modules
      const totalDuration = modules.reduce((total, module) =>
        total + module.lessons.reduce((moduleTotal, lesson) => moduleTotal + lesson.duration, 0), 0
      );

      // If no modules but has videoUrl, create a default module and lesson
      let finalModules = modules;
      if (modules.length === 0 && formData.videoUrl) {
        console.log('📝 Creating default module and lesson for course with videoUrl');
        const defaultDuration = 60; // Default to 60 minutes
        finalModules = [
          {
            id: `${targetCourseId}-module-1`,
            title: 'Course Content',
            description: 'Main course content',
            duration: defaultDuration,
            lessons: [
              {
                id: `${targetCourseId}-lesson-1`,
                title: formData.title,
                duration: defaultDuration,
                videoUrl: formData.videoUrl,
                description: formData.description,
                resources: [],
                isFree: false
              }
            ]
          }
        ];
      }

      const rawCourseData = {
        id: targetCourseId,
        title: formData.title,
        description: formData.description,
        shortDescription: formData.shortDescription,
        category: formData.category,
        level: formData.level,
        price: formData.price,
        originalPrice: formData.originalPrice || formData.price,
        duration: formData.duration,
        totalDuration: totalDuration,
        thumbnail: formData.thumbnail,
        videoUrl: formData.videoUrl,

        // Course Features
        courseFeatures: {
          totalDuration: totalDuration,
          moduleCount: modules.length,
          certificateEnabled: courseFeatures.certificateEnabled,
          lifetimeAccess: courseFeatures.lifetimeAccess,
          downloadableResources: courseFeatures.downloadableResources,
          mobileAccess: courseFeatures.mobileAccess,
          assignments: courseFeatures.assignments,
          quizzes: courseFeatures.quizzes
        },

        // Modules and Lessons
        modules: finalModules.map(module => ({
          id: module.id,
          title: module.title,
          description: module.description,
          duration: module.lessons.reduce((total, lesson) => total + lesson.duration, 0),
          lessons: module.lessons.map(lesson => ({
            id: lesson.id,
            title: lesson.title,
            duration: lesson.duration,
            videoUrl: lesson.videoUrl,
            description: lesson.description,
            resources: lesson.resources,
            isFree: lesson.isFree || false
          }))
        })),

        // Instructor Information
        instructor: instructor.name,
        instructorId: 'ahmed-takal',
        instructorAvatar: instructor.avatar,
        instructorBio: instructor.bio,
        instructorExpertise: instructor.expertise.filter(Boolean),
        instructorExperience: instructor.experience,
        instructorCredentials: instructor.credentials.filter(Boolean),
        instructorStats: instructor.stats,

        currency: 'USD',
        tags: formData.tags,
        requirements: prerequisites.filter(Boolean),
        learningOutcomes: learningOutcomes.filter(Boolean),
        targetAudience: targetAudience.filter(Boolean),
        isPublished: formData.isPublished,
        featured: formData.featured,
        allowComments: formData.allowComments,
        allowDownloads: formData.allowDownloads,
        certificateEnabled: formData.certificateEnabled,
        accessType: 'lifetime',
        enrollmentCount: 0,
        enrolledCount: 0,
        rating: 0,
        reviewCount: 0,
        createdBy: currentUser?.uid || 'unknown',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const courseData = prepareCourseForFirestore(rawCourseData);



      if (isEditing) {
        // Update existing course
        console.log('📝 Updating course with data:', courseData);
        await courseService.updateCourse(targetCourseId, courseData);
        setSuccess(`✅ Course "${formData.title}" updated successfully!`);
      } else {
        // Create new course
        console.log('📝 Creating comprehensive course with data:', courseData);
        const courseRef = doc(db, 'courses', targetCourseId);
        await setDoc(courseRef, courseData);
        setSuccess(`✅ Course "${formData.title}" created successfully with ID: ${targetCourseId}`);

        // Reset form only for new courses
        setFormData({
        title: '',
        description: '',
        shortDescription: '',
        category: '',
        level: 'Beginner',
        price: 0,
        originalPrice: 0,
        duration: '',
        thumbnail: '',
        videoUrl: '',
        tags: [],
        featured: false,
        isPublished: true,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true
      });
      setLearningOutcomes(['']);
      setTargetAudience(['']);
      setPrerequisites(['']);
      setNewTag('');
      setInstructor({
        name: 'Ahmed Takal',
        bio: '',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        expertise: [''],
        experience: '',
        credentials: [''],
        stats: {
          rating: 4.9,
          reviews: 2847,
          students: 15432,
          courses: 12
        }
      });
      setActiveTab('basic');

      // Call onCourseCreated callback for new courses
      if (onCourseCreated) {
        onCourseCreated();
      }
      }

    } catch (error: any) {
      console.error('❌ Error creating course:', error);
      setError(`Failed to create course: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // Show loading state while loading course for editing
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading course data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert className="bg-red-900/20 border-red-700">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-300">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-900/20 border-green-700">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-green-300">{success}</AlertDescription>
        </Alert>
      )}

      <Card className="bg-gray-800 border-gray-700 text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            {isEditing ? 'Edit Course' : 'Create Comprehensive Course'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="bg-gray-700 border-gray-600 grid grid-cols-7 w-full">
                <TabsTrigger value="basic" className="data-[state=active]:bg-blue-600">
                  <FileText className="h-4 w-4 mr-2" />
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="features" className="data-[state=active]:bg-blue-600">
                  <Star className="h-4 w-4 mr-2" />
                  Features
                </TabsTrigger>
                <TabsTrigger value="modules" className="data-[state=active]:bg-blue-600">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Modules
                </TabsTrigger>
                <TabsTrigger value="learning" className="data-[state=active]:bg-blue-600">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Learning
                </TabsTrigger>
                <TabsTrigger value="audience" className="data-[state=active]:bg-blue-600">
                  <Target className="h-4 w-4 mr-2" />
                  Audience
                </TabsTrigger>
                <TabsTrigger value="instructor" className="data-[state=active]:bg-blue-600">
                  <User className="h-4 w-4 mr-2" />
                  Instructor
                </TabsTrigger>
                <TabsTrigger value="settings" className="data-[state=active]:bg-blue-600">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </TabsTrigger>
              </TabsList>

              {/* Basic Info Tab */}
              <TabsContent value="basic" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Course Title *</label>
                    <Input
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="Enter course title"
                      className="bg-gray-700 border-gray-600 text-white"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Category *</label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleInputChange('category', value)}
                    >
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-700 border-gray-600">
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Course Description *</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter detailed course description"
                    className="bg-gray-700 border-gray-600 text-white"
                    rows={4}
                    required
                  />
                </div>

                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Level</label>
                    <Select value={formData.level} onValueChange={(value: any) => handleInputChange('level', value)}>
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-700 border-gray-600">
                        <SelectItem value="Beginner">Beginner</SelectItem>
                        <SelectItem value="Intermediate">Intermediate</SelectItem>
                        <SelectItem value="Advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Price (USD)</label>
                    <Input
                      type="number"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', Number(e.target.value))}
                      placeholder="49.99"
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Course Duration</label>
                    <Input
                      value={formData.duration}
                      onChange={(e) => handleInputChange('duration', e.target.value)}
                      placeholder="e.g., 8 weeks, 3 months"
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                    <p className="text-xs text-gray-400 mt-1">Human-readable duration (e.g., "8 weeks", "3 months")</p>
                  </div>
                </div>
              </TabsContent>

              {/* Course Features Tab */}
              <TabsContent value="features" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Course Details</h3>
                    <div>
                      <label className="block text-sm font-medium mb-2">Total Video Duration</label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Input
                            type="number"
                            value={Math.floor(courseFeatures.totalDuration / 60)}
                            onChange={(e) => {
                              const hours = parseInt(e.target.value) || 0;
                              const minutes = courseFeatures.totalDuration % 60;
                              handleFeatureChange('totalDuration', hours * 60 + minutes);
                            }}
                            className="bg-gray-700 border-gray-600 text-white"
                            placeholder="8"
                            min="0"
                          />
                          <p className="text-xs text-gray-400 mt-1">Hours</p>
                        </div>
                        <div>
                          <Input
                            type="number"
                            value={courseFeatures.totalDuration % 60}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value) || 0;
                              const hours = Math.floor(courseFeatures.totalDuration / 60);
                              handleFeatureChange('totalDuration', hours * 60 + minutes);
                            }}
                            className="bg-gray-700 border-gray-600 text-white"
                            placeholder="30"
                            min="0"
                            max="59"
                          />
                          <p className="text-xs text-gray-400 mt-1">Minutes</p>
                        </div>
                      </div>
                      <p className="text-xs text-gray-400 mt-1">Total: {Math.floor(courseFeatures.totalDuration / 60)}h {courseFeatures.totalDuration % 60}m ({courseFeatures.totalDuration} minutes)</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Course Features</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="certificate"
                          checked={courseFeatures.certificateEnabled}
                          onChange={(e) => handleFeatureChange('certificateEnabled', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="certificate" className="text-sm text-gray-300">Certificate of completion</label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="lifetime"
                          checked={courseFeatures.lifetimeAccess}
                          onChange={(e) => handleFeatureChange('lifetimeAccess', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="lifetime" className="text-sm text-gray-300">Lifetime access</label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="downloads"
                          checked={courseFeatures.downloadableResources}
                          onChange={(e) => handleFeatureChange('downloadableResources', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="downloads" className="text-sm text-gray-300">Downloadable resources</label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="mobile"
                          checked={courseFeatures.mobileAccess}
                          onChange={(e) => handleFeatureChange('mobileAccess', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="mobile" className="text-sm text-gray-300">Mobile access</label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="assignments"
                          checked={courseFeatures.assignments}
                          onChange={(e) => handleFeatureChange('assignments', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="assignments" className="text-sm text-gray-300">Assignments</label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="quizzes"
                          checked={courseFeatures.quizzes}
                          onChange={(e) => handleFeatureChange('quizzes', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="quizzes" className="text-sm text-gray-300">Quizzes</label>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Modules Tab */}
              <TabsContent value="modules" className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-white">Course Modules</h3>
                  <Button type="button" onClick={addModule} className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Module
                  </Button>
                </div>

                <div className="space-y-6">
                  {modules.map((module, moduleIndex) => (
                    <Card key={module.id} className="bg-gray-700 border-gray-600">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-center">
                          <h4 className="text-white font-medium">Module {moduleIndex + 1}</h4>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeModule(moduleIndex)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-2 text-gray-300">Module Title *</label>
                          <Input
                            value={module.title}
                            onChange={(e) => updateModule(moduleIndex, 'title', e.target.value)}
                            placeholder="e.g., Getting Started"
                            className="bg-gray-600 border-gray-500 text-white"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2 text-gray-300">Module Description</label>
                          <Textarea
                            value={module.description}
                            onChange={(e) => updateModule(moduleIndex, 'description', e.target.value)}
                            placeholder="Brief description of this module"
                            className="bg-gray-600 border-gray-500 text-white"
                            rows={2}
                          />
                        </div>

                        {/* Lessons */}
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <h5 className="text-white font-medium">Lessons</h5>
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => addLesson(moduleIndex)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              Add Lesson
                            </Button>
                          </div>

                          {module.lessons.map((lesson, lessonIndex) => (
                            <div key={lesson.id} className="bg-gray-600 p-4 rounded border border-gray-500">
                              <div className="flex justify-between items-center mb-3">
                                <span className="text-gray-300 text-sm">Lesson {lessonIndex + 1}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeLesson(moduleIndex, lessonIndex)}
                                  className="text-red-400 hover:text-red-300"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>

                              <div className="grid md:grid-cols-2 gap-3">
                                <div>
                                  <label className="block text-xs font-medium mb-1 text-gray-400">Lesson Title *</label>
                                  <Input
                                    value={lesson.title}
                                    onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'title', e.target.value)}
                                    placeholder="e.g., Introduction"
                                    className="bg-gray-500 border-gray-400 text-white text-sm"
                                  />
                                </div>

                                <div>
                                  <label className="block text-xs font-medium mb-1 text-gray-400">Duration (minutes)</label>
                                  <Input
                                    type="number"
                                    value={lesson.duration}
                                    onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'duration', parseInt(e.target.value) || 0)}
                                    className="bg-gray-500 border-gray-400 text-white text-sm"
                                  />
                                </div>
                              </div>

                              <div className="mt-3">
                                <label className="block text-xs font-medium mb-1 text-gray-400">Video URL</label>
                                <Input
                                  value={lesson.videoUrl}
                                  onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'videoUrl', e.target.value)}
                                  placeholder="https://..."
                                  className="bg-gray-500 border-gray-400 text-white text-sm"
                                />
                              </div>

                              <div className="mt-3 flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <input
                                    type="checkbox"
                                    id={`lesson-free-${moduleIndex}-${lessonIndex}`}
                                    checked={lesson.isFree || false}
                                    onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'isFree', e.target.checked)}
                                    className="rounded"
                                  />
                                  <label
                                    htmlFor={`lesson-free-${moduleIndex}-${lessonIndex}`}
                                    className="text-xs text-gray-400 flex items-center"
                                  >
                                    {lesson.isFree ? (
                                      <>
                                        <Unlock className="h-3 w-3 mr-1 text-green-400" />
                                        Free Preview
                                      </>
                                    ) : (
                                      <>
                                        <Lock className="h-3 w-3 mr-1 text-yellow-400" />
                                        Locked (Requires Purchase)
                                      </>
                                    )}
                                  </label>
                                </div>
                                {lesson.isFree && (
                                  <Badge variant="secondary" className="bg-green-600 text-green-100 text-xs">
                                    FREE
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* Learning Tab */}
              <TabsContent value="learning" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">What You'll Learn</h3>
                  <div className="space-y-3">
                    {learningOutcomes.map((outcome, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={outcome}
                          onChange={(e) => updateArrayItem(setLearningOutcomes, index, e.target.value)}
                          placeholder="e.g., FlutterFlow expert"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeArrayItem(setLearningOutcomes, index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => addArrayItem(setLearningOutcomes)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Learning Outcome
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Skills You'll Gain</h3>
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {formData.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-blue-600 text-blue-100">
                          {tag}
                          <button
                            type="button"
                            onClick={() => handleTagRemove(tag)}
                            className="ml-2 text-blue-200 hover:text-white"
                          >
                            ×
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="e.g., flutter, python, no-code"
                        className="bg-gray-700 border-gray-600 text-white"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleTagAdd(newTag);
                            setNewTag('');
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={() => {
                          handleTagAdd(newTag);
                          setNewTag('');
                        }}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Audience Tab */}
              <TabsContent value="audience" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Target Audience</h3>
                  <div className="space-y-3">
                    {targetAudience.map((audience, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={audience}
                          onChange={(e) => updateArrayItem(setTargetAudience, index, e.target.value)}
                          placeholder="e.g., Beginners interested in no-code development"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeArrayItem(setTargetAudience, index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => addArrayItem(setTargetAudience)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Target Audience
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Prerequisites</h3>
                  <div className="space-y-3">
                    {prerequisites.map((prerequisite, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={prerequisite}
                          onChange={(e) => updateArrayItem(setPrerequisites, index, e.target.value)}
                          placeholder="e.g., flutter, python"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeArrayItem(setPrerequisites, index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => addArrayItem(setPrerequisites)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Prerequisite
                    </Button>
                  </div>
                </div>
              </TabsContent>

              {/* Instructor Tab */}
              <TabsContent value="instructor" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">Instructor Name</label>
                      <Input
                        value={instructor.name}
                        onChange={(e) => setInstructor(prev => ({ ...prev, name: e.target.value }))}
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">Bio *</label>
                      <Textarea
                        value={instructor.bio}
                        onChange={(e) => setInstructor(prev => ({ ...prev, bio: e.target.value }))}
                        placeholder="Expert instructor with years of experience..."
                        className="bg-gray-700 border-gray-600 text-white"
                        rows={4}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">Experience</label>
                      <Input
                        value={instructor.experience}
                        onChange={(e) => setInstructor(prev => ({ ...prev, experience: e.target.value }))}
                        placeholder="e.g., 5+ years"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-white font-medium mb-3">Areas of Expertise</h4>
                      <div className="space-y-2">
                        {instructor.expertise.map((expertise, index) => (
                          <div key={index} className="flex gap-2">
                            <Input
                              value={expertise}
                              onChange={(e) => {
                                const newExpertise = [...instructor.expertise];
                                newExpertise[index] = e.target.value;
                                setInstructor(prev => ({ ...prev, expertise: newExpertise }));
                              }}
                              placeholder="e.g., FlutterFlow"
                              className="bg-gray-700 border-gray-600 text-white"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const newExpertise = instructor.expertise.filter((_, i) => i !== index);
                                setInstructor(prev => ({ ...prev, expertise: newExpertise }));
                              }}
                              className="text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setInstructor(prev => ({ ...prev, expertise: [...prev.expertise, ''] }))}
                          className="border-gray-600 text-gray-300 hover:bg-gray-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Expertise
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-white font-medium mb-3">Instructor Stats</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-400">Rating</label>
                          <Input
                            type="number"
                            step="0.1"
                            min="0"
                            max="5"
                            value={instructor.stats.rating}
                            onChange={(e) => setInstructor(prev => ({
                              ...prev,
                              stats: { ...prev.stats, rating: parseFloat(e.target.value) || 0 }
                            }))}
                            className="bg-gray-700 border-gray-600 text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-400">Reviews</label>
                          <Input
                            type="number"
                            value={instructor.stats.reviews}
                            onChange={(e) => setInstructor(prev => ({
                              ...prev,
                              stats: { ...prev.stats, reviews: parseInt(e.target.value) || 0 }
                            }))}
                            className="bg-gray-700 border-gray-600 text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-400">Students</label>
                          <Input
                            type="number"
                            value={instructor.stats.students}
                            onChange={(e) => setInstructor(prev => ({
                              ...prev,
                              stats: { ...prev.stats, students: parseInt(e.target.value) || 0 }
                            }))}
                            className="bg-gray-700 border-gray-600 text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-400">Courses</label>
                          <Input
                            type="number"
                            value={instructor.stats.courses}
                            onChange={(e) => setInstructor(prev => ({
                              ...prev,
                              stats: { ...prev.stats, courses: parseInt(e.target.value) || 0 }
                            }))}
                            className="bg-gray-700 border-gray-600 text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Publication Settings</h3>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.isPublished}
                        onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
                      />
                      <label className="text-gray-300">Publish course immediately</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.featured}
                        onCheckedChange={(checked) => handleInputChange('featured', checked)}
                      />
                      <label className="text-gray-300">Feature this course</label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Course Access</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-2 text-gray-300">Enrollment Limit</label>
                        <Input
                          type="number"
                          placeholder="Leave empty for unlimited"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2 text-gray-300">Course Language</label>
                        <Select defaultValue="en">
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600">
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="sw">Swahili</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Submit Button */}
              <div className="flex justify-end pt-6 border-t border-gray-700">
                <Button type="submit" disabled={saving || loading} className="bg-green-600 hover:bg-green-700">
                  {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                  {saving
                    ? (isEditing ? 'Updating Course...' : 'Creating Course...')
                    : (isEditing ? 'Update Course' : 'Create Course')
                  }
                </Button>
              </div>
            </Tabs>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
