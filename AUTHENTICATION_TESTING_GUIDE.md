# Authentication System Testing Guide

## 🚀 Quick Start Testing

### 1. Start the Development Server
```bash
npm run dev
```
The server should start on `http://localhost:5173`

### 2. Open Browser and Navigate
Open your browser and go to `http://localhost:5173`

## 🧪 Testing Checklist

### ✅ Initial Setup Verification

**Before Testing Authentication:**
- [ ] Development server is running without errors
- [ ] No console errors on page load
- [ ] AuthDebug panel appears in bottom-right corner (click to expand)
- [ ] Firebase configuration loads correctly (check AuthDebug panel)

### 🔐 Email/Password Authentication Testing

**Registration Test:**
1. [ ] Click "Sign Up" or registration button
2. [ ] Fill in registration form with valid data
3. [ ] Submit form
4. [ ] Check for success message or redirect
5. [ ] Verify email verification was sent
6. [ ] Check AuthDebug panel for user data

**Login Test:**
1. [ ] Click "Sign In" or login button
2. [ ] Enter registered email and password
3. [ ] Submit form
4. [ ] Check for successful login
5. [ ] Verify user profile loads in AuthDebug panel
6. [ ] Check if profile picture displays (if available)

### 🔍 Google Authentication Testing

**Google Sign-In Test:**
1. [ ] Click "Sign in with Google" button
2. [ ] **Expected Behavior:** Google popup should open
3. [ ] **If popup opens:** Select Google account and complete sign-in
4. [ ] **If popup is blocked:** Check browser popup settings
5. [ ] **If stuck on "Signing in":** Check console logs and AuthDebug panel
6. [ ] Verify successful login and profile creation
7. [ ] Check if Google profile picture displays

**Common Google Sign-In Issues:**

| Issue | Likely Cause | Solution |
|-------|--------------|----------|
| Popup blocked | Browser settings | Allow popups for localhost |
| "Unauthorized domain" | Firebase Console | Add localhost:5173 to authorized domains |
| Stuck on "Signing in" | Configuration issue | Check Firebase Console OAuth setup |
| "Internal error" | Firebase config | Verify API keys and project settings |

## 🐛 Debugging Tools

### AuthDebug Panel Features
- **Real-time auth state monitoring**
- **Error logging with timestamps**
- **User profile information display**
- **Firebase configuration verification**
- **Recent authentication events log**

### Browser Console Logs
Look for these log patterns:
- `🚀 Starting Google Sign-In process...`
- `📱 Opening Google Sign-In popup...`
- `✅ Google Sign-In popup completed successfully`
- `❌ Google sign-in error:` (indicates issues)

### Network Tab Inspection
Check for failed requests to:
- `accounts.google.com`
- `firebase.googleapis.com`
- `firestore.googleapis.com`

## 🔧 Firebase Console Verification

### Required Firebase Console Settings

**1. Authentication → Settings → Authorized Domains**
Add these domains:
- `localhost`
- `localhost:5173`
- `localhost:8080`
- Your production domain

**2. Authentication → Sign-in Method**
- [ ] Email/Password: Enabled
- [ ] Google: Enabled with proper OAuth client

**3. Google Cloud Console → OAuth Consent Screen**
- [ ] Application name set
- [ ] Support email configured
- [ ] Authorized domains added

**4. Google Cloud Console → Credentials → OAuth 2.0 Client IDs**
- [ ] JavaScript origins: `http://localhost:5173`
- [ ] Redirect URIs: `http://localhost:5173/__/auth/handler`

## 🚨 Troubleshooting Common Issues

### Google Sign-In Stuck on "Signing in"

**Immediate Checks:**
1. Open browser console - look for error messages
2. Check AuthDebug panel for error logs
3. Verify popup wasn't blocked
4. Try in incognito mode
5. Clear browser cache and cookies

**Configuration Checks:**
1. Verify Firebase project ID matches `.env` file
2. Check API key is correct and active
3. Confirm domain authorization in Firebase Console
4. Verify OAuth client configuration in Google Cloud Console

### Email/Password Issues

**Common Problems:**
- Weak password errors
- Email already in use
- Network connectivity issues
- Email verification not sent

### Profile Picture Not Displaying

**Troubleshooting:**
1. Check if `photoURL` exists in AuthDebug panel
2. Verify image URL is accessible
3. Check for CORS issues in browser console
4. Ensure UserMenu component is properly handling image errors

## 📋 Test Scenarios

### Scenario 1: New User Registration
1. Register with email/password
2. Verify email verification sent
3. Check profile creation in Firestore
4. Test login with new credentials

### Scenario 2: Google Sign-In (New User)
1. Sign in with Google account
2. Verify profile auto-creation
3. Check Google profile picture import
4. Verify user role assignment

### Scenario 3: Google Sign-In (Existing User)
1. Sign in with previously used Google account
2. Verify existing profile loads
3. Check profile picture updates

### Scenario 4: Mixed Authentication
1. Register with email/password
2. Later sign in with Google using same email
3. Verify account linking behavior

## 🎯 Success Criteria

**Authentication is working correctly when:**
- [ ] Both email/password and Google sign-in work without errors
- [ ] User profiles are created/updated properly in Firestore
- [ ] Profile pictures display correctly
- [ ] AuthDebug panel shows no errors
- [ ] Navigation updates to show authenticated state
- [ ] User can successfully log out

## 📞 Support Information

**If issues persist:**
1. Check `GOOGLE_AUTH_TROUBLESHOOTING.md` for detailed solutions
2. Review Firebase Console logs
3. Verify all environment variables are correct
4. Ensure internet connectivity is stable
5. Try different browsers or devices

**Environment Variables to Verify:**
```
VITE_FIREBASE_API_KEY=AIzaSyABB72plhO6QQEvbCGoiJnuowDbtjjJ2OM
VITE_FIREBASE_AUTH_DOMAIN=codefreelap.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=codefreelap
```

**Firebase Project:** `codefreelap`
**Development URL:** `http://localhost:5173`
**Production URL:** (To be configured)