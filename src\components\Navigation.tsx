import React, { useState } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { UserMenu } from '@/components/auth/UserMenu';
import { AdminOnly } from '@/components/auth/RoleBasedAccess';
import { Code, Menu, X, Home } from 'lucide-react';

interface NavItem {
  label: string;
  id?: string;
  action?: () => void;
}

export function Navigation() {
  const { currentUser, userProfile, loading, isAdmin } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Debug auth state (remove in production)
  React.useEffect(() => {
    console.log('Navigation - Auth State:', {
      currentUser: currentUser?.email || 'null',
      loading,
      userProfile: userProfile?.role || 'null'
    });
  }, [currentUser, loading, userProfile]);

  const scrollToSection = (sectionId: string) => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      navigate('/', { replace: true });
      // Wait for navigation to complete, then scroll
      setTimeout(() => {
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } else {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
    setIsMobileMenuOpen(false);
  };

  const handleHomeClick = () => {
    if (location.pathname === '/') {
      // If already on homepage, scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      // Navigate to homepage
      navigate('/');
    }
    setIsMobileMenuOpen(false);
  };

  // Show different navigation items based on current page
  const isDashboard = location.pathname === '/dashboard';

  const navItems: NavItem[] = isDashboard
    ? [
        { label: 'Home', action: handleHomeClick },
        { label: 'My Courses', id: 'courses' },
        { label: 'Live Sessions', id: 'sessions' },
        { label: 'Resources', id: 'resources' },
        ...(isAdmin ? [{ label: 'Admin Panel', action: () => navigate('/admin') }] : []),
      ]
    : [
        { label: 'Home', action: handleHomeClick },
        { label: 'All Courses', action: () => navigate('/courses') },
        { label: 'About', id: 'about-instructor' },
        { label: 'Testimonials', id: 'testimonials' },
        { label: 'FAQ', id: 'faq' },
        { label: 'Get in Touch', id: 'contact-section' },
      ];

  return (
    <>
      <nav className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-slate-700">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <button
              type="button"
              onClick={handleHomeClick}
              className="flex items-center space-x-2 hover:opacity-80 transition-all duration-300 btn-animated"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center animate-pulse-slow">
                <Code className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">
                FlutterFlow Academy
              </span>
            </button>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navItems.map((item, index) => (
                <button
                  key={item.id || index}
                  type="button"
                  onClick={() => item.action ? item.action() : scrollToSection(item.id!)}
                  className="text-slate-300 hover:text-white transition-all duration-300 flex items-center btn-animated"
                >
                  {item.label === 'Home' && <Home className="w-4 h-4 mr-1" />}
                  {item.label}
                </button>
              ))}
            </div>

            {/* Desktop Auth Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              {currentUser ? (
                <UserMenu />
              ) : (
                <>
                  <Button
                    asChild
                    className="btn-animated btn-ghost"
                  >
                    <Link to="/login">Sign In</Link>
                  </Button>
                  <Button
                    asChild
                    className="btn-animated btn-pink"
                  >
                    <Link to="/register">Get Started</Link>
                  </Button>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="btn-animated btn-ghost"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-slate-700 py-4 glass-effect">
              <div className="flex flex-col space-y-4">
                {navItems.map((item, index) => (
                  <button
                    key={item.id || index}
                    type="button"
                    onClick={() => item.action ? item.action() : scrollToSection(item.id!)}
                    className="text-left text-slate-300 hover:text-white transition-all duration-300 flex items-center btn-animated"
                  >
                    {item.label === 'Home' && <Home className="w-4 h-4 mr-2" />}
                    {item.label}
                  </button>
                ))}

                <div className="pt-4 border-t border-slate-700">
                  {currentUser ? (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        Welcome, {userProfile?.displayName || 'User'}
                      </span>
                      <UserMenu />
                    </div>
                  ) : (
                    <div className="flex flex-col space-y-2">
                      <Button
                        variant="ghost"
                        asChild
                        className="justify-start"
                      >
                        <Link to="/login">Sign In</Link>
                      </Button>
                      <Button
                        asChild
                        className="justify-start"
                      >
                        <Link to="/register">Get Started</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>


    </>
  );
}
