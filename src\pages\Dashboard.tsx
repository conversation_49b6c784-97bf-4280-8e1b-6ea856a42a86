import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCourse } from '@/contexts/CourseContext';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { courseService } from '@/services/courseService';
import { enrollmentService } from '@/services/enrollmentService';
import { debugManualEnrollment } from '@/utils/debugManualEnrollment';
import { Course } from '@/types/course';
import {
  BookOpen,
  Clock,
  Award,
  Users,
  PlayCircle,
  Download,
  Calendar,
  MessageCircle,
  ArrowLeft,
  CheckCircle,
  TrendingUp,
  Target
} from 'lucide-react';

export default function Dashboard() {
  const { currentUser, userProfile } = useAuth();
  const navigate = useNavigate();
  const [courses, setCourses] = useState<Course[]>([]);
  const [enrolledCourseIds, setEnrolledCourseIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // Load courses and enrollments from Firestore
  useEffect(() => {
    const loadData = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Load courses
        const coursesData = await courseService.getPublishedCourses();
        setCourses(coursesData);

        // Load user enrollments
        console.log('🔍 Dashboard: Loading enrollments for user:', currentUser.uid, 'email:', currentUser.email);
        const userEnrollments = await enrollmentService.getUserEnrollments(currentUser.uid);
        const enrolledIds = userEnrollments.map(enrollment => enrollment.courseId);
        setEnrolledCourseIds(enrolledIds);

        console.log('📚 Dashboard: Loaded', coursesData.length, 'courses and', enrolledIds.length, 'enrollments');
        console.log('📚 Dashboard: Enrolled course IDs:', enrolledIds);
        console.log('📚 Dashboard: User enrollments:', userEnrollments);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentUser]);

  // Debug function for manual enrollment testing
  const handleDebugEnrollment = async () => {
    if (!currentUser?.email) {
      console.log('❌ No user email found');
      return;
    }

    // Use the first available course for testing
    const testCourseId = courses.length > 0 ? courses[0].id : 'test-course-id';
    console.log('🔍 Testing with course ID:', testCourseId);
    await debugManualEnrollment(currentUser.email, testCourseId);
  };

  // Get user progress for enrolled courses
  const getUserProgress = (courseId: string) => {
    const progressKey = `progress_${currentUser?.uid}_${courseId}`;
    const stored = localStorage.getItem(progressKey);
    if (stored) {
      return JSON.parse(stored);
    }
    return null;
  };

  // Get enrolled courses with progress data
  const enrolledCourses = courses.filter(course =>
    enrolledCourseIds.includes(course.id)
  ).map(course => {
    // For now, use localStorage for progress data (this can be enhanced later to use Firestore)
    const progressKey = `progress_${currentUser?.uid}_${course.id}`;
    const progressData = localStorage.getItem(progressKey);
    let progress = null;

    if (progressData) {
      try {
        progress = JSON.parse(progressData);
      } catch (error) {
        console.error('Error parsing progress data:', error);
      }
    }

    const totalLessons = course.modules?.reduce((total, module) => total + (module.lessons?.length || 0), 0) || 0;
    const completedLessons = progress?.completedLessons?.length || 0;
    const completionPercentage = progress?.completionPercentage || 0;

    return {
      id: course.id,
      title: course.title,
      instructor: course.instructor,
      thumbnail: course.thumbnail,
      progress: completionPercentage,
      totalLessons,
      completedLessons,
      totalAssignments: course.modules.reduce((total, module) => total + module.assignments.length, 0),
      completedAssignments: progress?.completedAssignments?.length || 0,
      totalQuizzes: course.modules.reduce((total, module) => total + module.quizzes.length, 0),
      completedQuizzes: progress?.completedQuizzes?.length || 0,
      totalWatchTime: progress?.totalWatchTime || 0,
      lastAccessedAt: progress?.lastAccessedAt ? new Date(progress.lastAccessedAt) : null,
      certificateEarned: progress?.certificateEarned || false,
      enrolledAt: progress?.enrolledAt ? new Date(progress.enrolledAt) : new Date()
    };
  }).filter(course => {
    // Only show courses the user has actually started
    const progress = getUserProgress(course.id);
    return progress !== null;
  });

  const upcomingLiveClasses = [
    {
      id: 1,
      title: "Advanced Custom Widgets",
      date: "2025-07-12",
      time: "2:00 PM EAT",
      instructor: "Ahmed",
      spotsLeft: 8
    },
    {
      id: 2,
      title: "Revenue Cat Integration",
      date: "2025-07-19",
      time: "2:00 PM EAT",
      instructor: "Ahmed",
      spotsLeft: 12
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen section-dark">
      <Navigation />
      <div className="pt-16">
        <div className="container mx-auto px-4 py-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Welcome back, {currentUser?.displayName || 'Student'}!
                </h1>
                <p className="text-gray-600">
                  Continue your FlutterFlow journey and build amazing apps
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleDebugEnrollment}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  🔍 Debug Enrollment
                </Button>
                <Button
                  onClick={() => navigate('/')}
                  className="flex items-center btn-animated btn-blue"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="card-elevated card-bounce">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <BookOpen className="w-8 h-8 text-blue-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-200">Enrolled Courses</p>
                    <p className="text-2xl font-bold text-white">{enrolledCourses.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-elevated card-bounce animate-delay-100">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="w-8 h-8 text-green-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-200">Hours Learned</p>
                    <p className="text-2xl font-bold text-white">
                      {Math.floor(enrolledCourses.reduce((total, course) => total + course.totalWatchTime, 0) / 3600)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-elevated card-bounce animate-delay-200">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Award className="w-8 h-8 text-purple-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-200">Certificates</p>
                    <p className="text-2xl font-bold text-white">
                      {enrolledCourses.filter(course => course.certificateEarned).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-elevated card-bounce animate-delay-300">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <TrendingUp className="w-8 h-8 text-pink-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-200">Avg Progress</p>
                    <p className="text-2xl font-bold text-white">
                      {enrolledCourses.length > 0
                        ? Math.round(enrolledCourses.reduce((total, course) => total + course.progress, 0) / enrolledCourses.length)
                        : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* My Courses */}
            <div className="lg:col-span-2">
              <Card className="card-elevated">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
                    My Courses
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {enrolledCourses.length > 0 ? enrolledCourses.map((course) => (
                      <div key={course.id} className="border border-blue-dark-600 rounded-lg p-6 hover:shadow-md transition-shadow bg-blue-dark-800/50">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-semibold text-white text-lg">
                                {course.title}
                              </h3>
                              {course.certificateEarned && (
                                <Badge variant="success" className="bg-green-500 text-white">
                                  <Award className="w-3 h-3 mr-1" />
                                  Certified
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-blue-200 mb-3">
                              by {course.instructor}
                            </p>

                            {/* Progress Overview */}
                            <div className="grid grid-cols-3 gap-4 mb-4">
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <BookOpen className="w-4 h-4 text-blue-400 mr-1" />
                                  <span className="text-sm font-medium text-white">
                                    {course.completedLessons}/{course.totalLessons}
                                  </span>
                                </div>
                                <p className="text-xs text-blue-300">Lessons</p>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <CheckCircle className="w-4 h-4 text-green-400 mr-1" />
                                  <span className="text-sm font-medium text-white">
                                    {course.completedAssignments}/{course.totalAssignments}
                                  </span>
                                </div>
                                <p className="text-xs text-blue-300">Assignments</p>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <Target className="w-4 h-4 text-purple-400 mr-1" />
                                  <span className="text-sm font-medium text-white">
                                    {course.completedQuizzes}/{course.totalQuizzes}
                                  </span>
                                </div>
                                <p className="text-xs text-blue-300">Quizzes</p>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="mb-3">
                              <div className="flex justify-between text-sm mb-1">
                                <span className="text-white">Overall Progress</span>
                                <span className="font-medium text-blue-200">{Math.round(course.progress)}%</span>
                              </div>
                              <Progress value={course.progress} className="h-2" />
                            </div>

                            {/* Stats */}
                            <div className="flex items-center text-xs text-blue-300 space-x-4">
                              {course.totalWatchTime > 0 && (
                                <div className="flex items-center">
                                  <Clock className="w-3 h-3 mr-1" />
                                  <span>{Math.floor(course.totalWatchTime / 3600)}h {Math.floor((course.totalWatchTime % 3600) / 60)}m watched</span>
                                </div>
                              )}
                              {course.lastAccessedAt && (
                                <div className="flex items-center">
                                  <Calendar className="w-3 h-3 mr-1" />
                                  <span>Last accessed {course.lastAccessedAt.toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="flex items-center"
                            onClick={() => navigate(`/course/${course.id}/content`)}
                          >
                            <PlayCircle className="w-4 h-4 mr-1" />
                            Continue Learning
                          </Button>
                          <Button size="sm" variant="outline" className="flex items-center">
                            <Download className="w-4 h-4 mr-1" />
                            Resources
                          </Button>
                          {course.progress >= 100 && (
                            <Button size="sm" variant="outline" className="flex items-center">
                              <Award className="w-4 h-4 mr-1" />
                              Certificate
                            </Button>
                          )}
                        </div>
                      </div>
                    )) : (
                      <div className="text-center py-12">
                        <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Courses Yet</h3>
                        <p className="text-gray-600 mb-4">
                          Start your FlutterFlow journey by enrolling in a course.
                        </p>
                        <Button onClick={() => {
                          navigate('/');
                          // Scroll to course catalog after navigation
                          setTimeout(() => {
                            const catalogSection = document.getElementById('course-catalog');
                            catalogSection?.scrollIntoView({ behavior: 'smooth' });
                          }, 100);
                        }}>
                          Browse Courses
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Upcoming Live Classes */}
              <Card className="card-elevated">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <Calendar className="w-5 h-5 mr-2 text-pink-400" />
                    Upcoming Live Classes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingLiveClasses.map((session) => (
                      <div key={session.id} className="border border-blue-dark-600 rounded-lg p-3 bg-blue-dark-800/50">
                        <h4 className="font-medium text-white mb-1">
                          {session.title}
                        </h4>
                        <p className="text-sm text-blue-200 mb-2">
                          {session.date} at {session.time}
                        </p>
                        <p className="text-xs text-green-400 mb-3">
                          {session.spotsLeft} spots left
                        </p>
                        <Button size="sm" className="w-full btn-animated btn-pink">
                          Join Session
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="card-elevated">
                <CardHeader>
                  <CardTitle className="text-white">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Join Community
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="w-4 h-4 mr-2" />
                      Download Resources
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Award className="w-4 h-4 mr-2" />
                      View Certificates
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
