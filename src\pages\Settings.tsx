import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Settings as SettingsIcon, 
  Bell, 
  Shield, 
  Eye, 
  Moon, 
  Globe,
  Download,
  Trash2,
  LogOut,
  Save,
  AlertTriangle,
  Mail,
  Smartphone,
  Lock,
  Key
} from 'lucide-react';

export default function Settings() {
  const { currentUser, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    // General Settings
    language: 'en',
    timezone: 'Africa/Nairobi',
    theme: 'dark',
    
    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    courseUpdates: true,
    marketingEmails: false,
    weeklyDigest: true,
    
    // Privacy Settings
    profileVisibility: 'private',
    showProgress: false,
    allowMessages: true,
    
    // Security Settings
    twoFactorAuth: false,
    loginAlerts: true,
    sessionTimeout: '30'
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = () => {
    // TODO: Implement settings save logic
    console.log('Saving settings:', settings);
  };

  const handleDeleteAccount = () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      // TODO: Implement account deletion logic
      console.log('Deleting account...');
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <Navigation />
      
      <div className="container mx-auto px-4 py-24">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Account Settings</h1>
          <p className="text-xl text-gray-300">Manage your account preferences and security settings</p>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-800 border-gray-700 shadow-xl">
              <CardContent className="p-6">
                <nav className="space-y-2">
                  <Button
                    onClick={() => setActiveTab('general')}
                    className={`w-full justify-start ${activeTab === 'general' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-gray-700'}`}
                  >
                    <SettingsIcon className="w-4 h-4 mr-2" />
                    General
                  </Button>
                  <Button
                    onClick={() => setActiveTab('notifications')}
                    className={`w-full justify-start ${activeTab === 'notifications' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-gray-700'}`}
                  >
                    <Bell className="w-4 h-4 mr-2" />
                    Notifications
                  </Button>
                  <Button
                    onClick={() => setActiveTab('privacy')}
                    className={`w-full justify-start ${activeTab === 'privacy' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-gray-700'}`}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Privacy
                  </Button>
                  <Button
                    onClick={() => setActiveTab('security')}
                    className={`w-full justify-start ${activeTab === 'security' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-gray-700'}`}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    Security
                  </Button>
                  <Button
                    onClick={() => setActiveTab('account')}
                    className={`w-full justify-start ${activeTab === 'account' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-gray-700'}`}
                  >
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Account
                  </Button>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            {/* General Settings */}
            {activeTab === 'general' && (
              <Card className="bg-gray-800 border-gray-700 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <SettingsIcon className="w-6 h-6 mr-2 text-blue-400" />
                    General Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="language" className="text-gray-300">Language</Label>
                      <select
                        id="language"
                        value={settings.language}
                        onChange={(e) => handleSettingChange('language', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="en">English</option>
                        <option value="sw">Swahili</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                    
                    <div>
                      <Label htmlFor="timezone" className="text-gray-300">Timezone</Label>
                      <select
                        id="timezone"
                        value={settings.timezone}
                        onChange={(e) => handleSettingChange('timezone', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="Africa/Nairobi">Africa/Nairobi (EAT)</option>
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">America/New_York (EST)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-300">Theme Preference</Label>
                    <div className="flex items-center space-x-4 mt-2">
                      <Button
                        onClick={() => handleSettingChange('theme', 'dark')}
                        className={`${settings.theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
                      >
                        <Moon className="w-4 h-4 mr-2" />
                        Dark
                      </Button>
                      <Button
                        onClick={() => handleSettingChange('theme', 'light')}
                        className={`${settings.theme === 'light' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
                      >
                        <Globe className="w-4 h-4 mr-2" />
                        Light
                      </Button>
                    </div>
                  </div>

                  <Button onClick={handleSaveSettings} className="bg-green-600 hover:bg-green-700 text-white">
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Notification Settings */}
            {activeTab === 'notifications' && (
              <Card className="bg-gray-800 border-gray-700 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <Bell className="w-6 h-6 mr-2 text-blue-400" />
                    Notification Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-blue-400" />
                        <div>
                          <p className="text-white font-medium">Email Notifications</p>
                          <p className="text-gray-400 text-sm">Receive notifications via email</p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.emailNotifications}
                        onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Smartphone className="w-5 h-5 text-green-400" />
                        <div>
                          <p className="text-white font-medium">Push Notifications</p>
                          <p className="text-gray-400 text-sm">Receive push notifications on your device</p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.pushNotifications}
                        onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Bell className="w-5 h-5 text-purple-400" />
                        <div>
                          <p className="text-white font-medium">Course Updates</p>
                          <p className="text-gray-400 text-sm">Get notified about new course content</p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.courseUpdates}
                        onCheckedChange={(checked) => handleSettingChange('courseUpdates', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-yellow-400" />
                        <div>
                          <p className="text-white font-medium">Marketing Emails</p>
                          <p className="text-gray-400 text-sm">Receive promotional content and offers</p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.marketingEmails}
                        onCheckedChange={(checked) => handleSettingChange('marketingEmails', checked)}
                      />
                    </div>
                  </div>

                  <Button onClick={handleSaveSettings} className="bg-green-600 hover:bg-green-700 text-white">
                    <Save className="w-4 h-4 mr-2" />
                    Save Notification Settings
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Privacy Settings */}
            {activeTab === 'privacy' && (
              <Card className="bg-gray-800 border-gray-700 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <Eye className="w-6 h-6 mr-2 text-blue-400" />
                    Privacy Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label className="text-gray-300">Profile Visibility</Label>
                    <select
                      value={settings.profileVisibility}
                      onChange={(e) => handleSettingChange('profileVisibility', e.target.value)}
                      className="w-full mt-2 bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="public">Public - Anyone can see your profile</option>
                      <option value="students">Students Only - Only other students can see</option>
                      <option value="private">Private - Only you can see your profile</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div>
                      <p className="text-white font-medium">Show Learning Progress</p>
                      <p className="text-gray-400 text-sm">Allow others to see your course progress</p>
                    </div>
                    <Switch
                      checked={settings.showProgress}
                      onCheckedChange={(checked) => handleSettingChange('showProgress', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div>
                      <p className="text-white font-medium">Allow Messages</p>
                      <p className="text-gray-400 text-sm">Let other students send you messages</p>
                    </div>
                    <Switch
                      checked={settings.allowMessages}
                      onCheckedChange={(checked) => handleSettingChange('allowMessages', checked)}
                    />
                  </div>

                  <Button onClick={handleSaveSettings} className="bg-green-600 hover:bg-green-700 text-white">
                    <Save className="w-4 h-4 mr-2" />
                    Save Privacy Settings
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <Card className="bg-gray-800 border-gray-700 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <Shield className="w-6 h-6 mr-2 text-blue-400" />
                    Security Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Key className="w-5 h-5 text-green-400" />
                      <div>
                        <p className="text-white font-medium">Two-Factor Authentication</p>
                        <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.twoFactorAuth}
                      onCheckedChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Bell className="w-5 h-5 text-yellow-400" />
                      <div>
                        <p className="text-white font-medium">Login Alerts</p>
                        <p className="text-gray-400 text-sm">Get notified of new login attempts</p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.loginAlerts}
                      onCheckedChange={(checked) => handleSettingChange('loginAlerts', checked)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="sessionTimeout" className="text-gray-300">Session Timeout (minutes)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => handleSettingChange('sessionTimeout', e.target.value)}
                      className="mt-2 bg-gray-700 border-gray-600 text-white"
                    />
                  </div>

                  <div className="space-y-3">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                      <Lock className="w-4 h-4 mr-2" />
                      Change Password
                    </Button>
                    <Button onClick={handleSaveSettings} className="w-full bg-green-600 hover:bg-green-700 text-white">
                      <Save className="w-4 h-4 mr-2" />
                      Save Security Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Account Management */}
            {activeTab === 'account' && (
              <Card className="bg-gray-800 border-gray-700 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <AlertTriangle className="w-6 h-6 mr-2 text-red-400" />
                    Account Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-900/30 border border-blue-700 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-white mb-2">Export Your Data</h4>
                    <p className="text-gray-300 mb-4">Download a copy of all your account data and course progress.</p>
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Download className="w-4 h-4 mr-2" />
                      Export Data
                    </Button>
                  </div>

                  <div className="bg-yellow-900/30 border border-yellow-700 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-white mb-2">Sign Out</h4>
                    <p className="text-gray-300 mb-4">Sign out of your account on this device.</p>
                    <Button onClick={handleLogout} className="bg-yellow-600 hover:bg-yellow-700 text-white">
                      <LogOut className="w-4 h-4 mr-2" />
                      Sign Out
                    </Button>
                  </div>

                  <div className="bg-red-900/30 border border-red-700 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-white mb-2">Delete Account</h4>
                    <p className="text-gray-300 mb-4">
                      Permanently delete your account and all associated data. This action cannot be undone.
                    </p>
                    <Button onClick={handleDeleteAccount} className="bg-red-600 hover:bg-red-700 text-white">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Account
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
