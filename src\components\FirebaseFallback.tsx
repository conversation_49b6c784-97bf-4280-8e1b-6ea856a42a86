import React from 'react';
import { Al<PERSON><PERSON>riangle, Refresh<PERSON><PERSON>, Setting<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface FirebaseFallbackProps {
  error?: string;
  onRetry?: () => void;
}

export const FirebaseFallback: React.FC<FirebaseFallbackProps> = ({ 
  error, 
  onRetry 
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-blue-900 flex items-center justify-center p-4">
      <Card className="max-w-md w-full bg-white/10 backdrop-blur-lg border-white/20">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-yellow-400" />
          </div>
          <CardTitle className="text-white text-xl">
            Connection Issue
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-300">
            We're having trouble connecting to our services. This might be due to:
          </p>
          
          <ul className="text-left text-gray-300 space-y-2 text-sm">
            <li className="flex items-center space-x-2">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <span>Network connectivity issues</span>
            </li>
            <li className="flex items-center space-x-2">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <span>Firebase configuration problems</span>
            </li>
            <li className="flex items-center space-x-2">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <span>Temporary service outage</span>
            </li>
          </ul>

          {error && (
            <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-300 text-sm font-mono">{error}</p>
            </div>
          )}

          <div className="space-y-3 pt-4">
            <Button 
              onClick={onRetry || (() => window.location.reload())}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => window.location.href = '/page-test'}
              className="w-full border-white/20 text-white hover:bg-white/10"
            >
              <Settings className="w-4 h-4 mr-2" />
              Diagnostic Tools
            </Button>
          </div>

          <div className="pt-4 border-t border-white/10">
            <p className="text-xs text-gray-400">
              If the problem persists, please contact support or try again later.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FirebaseFallback;
