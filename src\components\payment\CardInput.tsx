import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CreditCard, Lock } from 'lucide-react';

interface CardInputProps {
  onCardChange: (cardData: CardData) => void;
  errors?: CardErrors;
}

export interface CardData {
  number: string;
  expiry: string;
  cvv: string;
  name: string;
  type: CardType;
  isValid: boolean;
}

export interface CardErrors {
  number?: string;
  expiry?: string;
  cvv?: string;
  name?: string;
}

export type CardType = 'visa' | 'mastercard' | 'verve' | 'amex' | 'unknown';

const CardInput: React.FC<CardInputProps> = ({ onCardChange, errors = {} }) => {
  const [cardData, setCardData] = useState<CardData>({
    number: '',
    expiry: '',
    cvv: '',
    name: '',
    type: 'unknown',
    isValid: false,
  });

  const [focused, setFocused] = useState<string | null>(null);
  const [showCvvHelp, setShowCvvHelp] = useState(false);

  // Card type detection
  const detectCardType = (number: string): CardType => {
    const cleanNumber = number.replace(/\s/g, '');
    
    if (cleanNumber.startsWith('4')) return 'visa';
    if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard';
    if (cleanNumber.startsWith('506')) return 'verve';
    if (cleanNumber.startsWith('34') || cleanNumber.startsWith('37')) return 'amex';
    
    return 'unknown';
  };

  // Format card number with spaces
  const formatCardNumber = (value: string): string => {
    const cleanValue = value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    const matches = cleanValue.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    return parts.length ? parts.join(' ') : cleanValue;
  };

  // Format expiry date
  const formatExpiry = (value: string): string => {
    const cleanValue = value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    if (cleanValue.length >= 2) {
      return cleanValue.substring(0, 2) + '/' + cleanValue.substring(2, 4);
    }
    return cleanValue;
  };

  // Validate card number using Luhn algorithm
  const validateCardNumber = (number: string): boolean => {
    const cleanNumber = number.replace(/\s/g, '');
    if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
    
    let sum = 0;
    let isEven = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber.charAt(i), 10);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  };

  // Validate expiry date
  const validateExpiry = (expiry: string): boolean => {
    if (expiry.length !== 5) return false;
    
    const [month, year] = expiry.split('/');
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt('20' + year, 10);
    
    if (monthNum < 1 || monthNum > 12) return false;
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (yearNum < currentYear) return false;
    if (yearNum === currentYear && monthNum < currentMonth) return false;
    
    return true;
  };

  // Validate CVV
  const validateCvv = (cvv: string, cardType: CardType): boolean => {
    if (cardType === 'amex') {
      return cvv.length === 4;
    }
    return cvv.length === 3;
  };

  // Update card data and validation
  useEffect(() => {
    const isNumberValid = validateCardNumber(cardData.number);
    const isExpiryValid = validateExpiry(cardData.expiry);
    const isCvvValid = validateCvv(cardData.cvv, cardData.type);
    const isNameValid = cardData.name.trim().length >= 2;
    
    const updatedCardData = {
      ...cardData,
      isValid: isNumberValid && isExpiryValid && isCvvValid && isNameValid,
    };
    
    onCardChange(updatedCardData);
  }, [cardData.number, cardData.expiry, cardData.cvv, cardData.name, cardData.type]);

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value);
    const type = detectCardType(formatted);
    
    setCardData(prev => ({
      ...prev,
      number: formatted,
      type,
    }));
  };

  const handleExpiryChange = (value: string) => {
    const formatted = formatExpiry(value);
    setCardData(prev => ({ ...prev, expiry: formatted }));
  };

  const handleCvvChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9]/gi, '');
    const maxLength = cardData.type === 'amex' ? 4 : 3;
    
    setCardData(prev => ({
      ...prev,
      cvv: cleanValue.substring(0, maxLength),
    }));
  };

  const handleNameChange = (value: string) => {
    setCardData(prev => ({ ...prev, name: value }));
  };

  // Get card logo
  const getCardLogo = (type: CardType) => {
    switch (type) {
      case 'visa':
        return (
          <div className="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">VISA</span>
          </div>
        );
      case 'mastercard':
        return (
          <div className="w-10 h-6 bg-red-500 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">MC</span>
          </div>
        );
      case 'verve':
        return (
          <div className="w-10 h-6 bg-green-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">VERVE</span>
          </div>
        );
      case 'amex':
        return (
          <div className="w-10 h-6 bg-blue-800 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">AMEX</span>
          </div>
        );
      default:
        return <CreditCard className="w-6 h-6 text-gray-400" />;
    }
  };

  return (
    <form autoComplete="on" className="space-y-4">
      {/* Card Number */}
      <div className="space-y-2">
        <Label htmlFor="card-number" className="text-sm font-medium text-gray-300">
          Card Number
        </Label>
        <div className="relative">
          <Input
            id="card-number"
            name="cardnumber"
            type="text"
            value={cardData.number}
            onChange={(e) => handleCardNumberChange(e.target.value)}
            onFocus={() => setFocused('number')}
            onBlur={() => setFocused(null)}
            placeholder="1234 5678 9012 3456"
            autoComplete="cc-number"
            autoCorrect="off"
            spellCheck="false"
            className={`payment-input pr-12 bg-gray-700 border-gray-600 text-white ${
              focused === 'number' ? 'ring-2 ring-blue-500' : ''
            } ${errors.number ? 'border-red-500' : ''}`}
            maxLength={19}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {getCardLogo(cardData.type)}
          </div>
        </div>
        {errors.number && (
          <p className="text-xs text-red-400">{errors.number}</p>
        )}
      </div>

      {/* Expiry and CVV */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="expiry" className="text-sm font-medium text-gray-300">
            Expiry Date
          </Label>
          <Input
            id="expiry"
            name="cc-exp"
            type="text"
            value={cardData.expiry}
            onChange={(e) => handleExpiryChange(e.target.value)}
            onFocus={() => setFocused('expiry')}
            onBlur={() => setFocused(null)}
            placeholder="MM/YY"
            autoComplete="cc-exp"
            autoCorrect="off"
            spellCheck="false"
            className={`payment-input bg-gray-700 border-gray-600 text-white ${
              focused === 'expiry' ? 'ring-2 ring-blue-500' : ''
            } ${errors.expiry ? 'border-red-500' : ''}`}
            maxLength={5}
          />
          {errors.expiry && (
            <p className="text-xs text-red-400">{errors.expiry}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="cvv" className="text-sm font-medium text-gray-300">
            CVV
          </Label>
          <div className="relative">
            <Input
              id="cvv"
              name="cc-csc"
              type="text"
              value={cardData.cvv}
              onChange={(e) => handleCvvChange(e.target.value)}
              onFocus={() => {
                setFocused('cvv');
                setShowCvvHelp(true);
              }}
              onBlur={() => {
                setFocused(null);
                setShowCvvHelp(false);
              }}
              placeholder={cardData.type === 'amex' ? '1234' : '123'}
              autoComplete="cc-csc"
              autoCorrect="off"
              spellCheck="false"
              className={`payment-input bg-gray-700 border-gray-600 text-white ${
                focused === 'cvv' ? 'ring-2 ring-blue-500' : ''
              } ${errors.cvv ? 'border-red-500' : ''}`}
              maxLength={cardData.type === 'amex' ? 4 : 3}
            />
            {showCvvHelp && (
              <div className="absolute top-full left-0 mt-1 p-2 bg-gray-800 text-white text-xs rounded shadow-lg z-10">
                {cardData.type === 'amex'
                  ? '4-digit code on front of card'
                  : '3-digit code on back of card'
                }
              </div>
            )}
          </div>
          {errors.cvv && (
            <p className="text-xs text-red-400">{errors.cvv}</p>
          )}
        </div>
      </div>

      {/* Cardholder Name */}
      <div className="space-y-2">
        <Label htmlFor="card-name" className="text-sm font-medium text-gray-300">
          Cardholder Name
        </Label>
        <Input
          id="card-name"
          name="cc-name"
          type="text"
          value={cardData.name}
          onChange={(e) => handleNameChange(e.target.value)}
          onFocus={() => setFocused('name')}
          onBlur={() => setFocused(null)}
          placeholder="Ahmed Takal"
          autoComplete="cc-name"
          autoCorrect="off"
          spellCheck="false"
          className={`payment-input bg-gray-700 border-gray-600 text-white ${
            focused === 'name' ? 'ring-2 ring-blue-500' : ''
          } ${errors.name ? 'border-red-500' : ''}`}
        />
        {errors.name && (
          <p className="text-xs text-red-400">{errors.name}</p>
        )}
      </div>

      {/* Security Notice */}
      <div className="flex items-center space-x-2 text-sm text-gray-400 pt-4">
        <Lock className="w-4 h-4 text-green-400" />
        <span>Your payment information is secure and encrypted with SSL</span>
      </div>
    </form>
  );
};

export default CardInput;
