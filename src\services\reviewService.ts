import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  updateDoc, 
  deleteDoc,
  serverTimestamp,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface Review {
  id?: string;
  courseId: string;
  userId: string;
  userName: string;
  userEmail: string;
  userPhotoURL?: string;
  rating: number;
  comment: string;
  helpful: number;
  notHelpful: number;
  helpfulUsers: string[];
  notHelpfulUsers: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isVerifiedPurchase: boolean;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

class ReviewService {
  private reviewsCollection = collection(db, 'reviews');

  // Submit a new review
  async submitReview(reviewData: Omit<Review, 'id' | 'createdAt' | 'updatedAt' | 'helpful' | 'notHelpful' | 'helpfulUsers' | 'notHelpfulUsers'>): Promise<string> {
    try {
      console.log('📝 Submitting review:', reviewData);

      // Check if user already reviewed this course
      const existingReview = await this.getUserReviewForCourse(reviewData.userId, reviewData.courseId);
      if (existingReview) {
        throw new Error('You have already reviewed this course. You can edit your existing review.');
      }

      const review: Omit<Review, 'id'> = {
        ...reviewData,
        helpful: 0,
        notHelpful: 0,
        helpfulUsers: [],
        notHelpfulUsers: [],
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };

      console.log('📝 Adding review to Firestore:', review);
      const docRef = await addDoc(this.reviewsCollection, review);
      console.log('✅ Review submitted successfully with ID:', docRef.id);

      // Verify the review was saved by reading it back
      try {
        const savedReview = await this.getCourseReviews(reviewData.courseId);
        console.log('✅ Verification: Found', savedReview.length, 'reviews after submission');
      } catch (verifyError) {
        console.warn('⚠️ Could not verify review was saved:', verifyError);
      }

      // Update course rating statistics
      await this.updateCourseRatingStats(reviewData.courseId);

      return docRef.id;
    } catch (error) {
      console.error('❌ Error submitting review:', error);
      throw error;
    }
  }

  // Get all reviews for a course
  async getCourseReviews(courseId: string): Promise<Review[]> {
    try {
      console.log('📖 Fetching reviews for course:', courseId);

      // First try with ordering, fallback to simple query if it fails
      let q;
      try {
        q = query(
          this.reviewsCollection,
          where('courseId', '==', courseId),
          orderBy('createdAt', 'desc')
        );
      } catch (indexError) {
        console.warn('⚠️ Firestore index not available, using simple query');
        q = query(
          this.reviewsCollection,
          where('courseId', '==', courseId)
        );
      }

      const querySnapshot = await getDocs(q);
      const reviews: Review[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        reviews.push({
          id: doc.id,
          ...data
        } as Review);
      });

      // Sort manually if we couldn't use orderBy
      reviews.sort((a, b) => {
        const aTime = a.createdAt?.toDate?.() || new Date(0);
        const bTime = b.createdAt?.toDate?.() || new Date(0);
        return bTime.getTime() - aTime.getTime();
      });

      console.log('✅ Fetched reviews:', reviews.length);
      return reviews;
    } catch (error) {
      console.error('❌ Error fetching course reviews:', error);
      // Return empty array instead of throwing to prevent UI from breaking
      return [];
    }
  }

  // Get real-time reviews for a course
  subscribeToReviews(courseId: string, callback: (reviews: Review[]) => void): () => void {
    console.log('🔄 Subscribing to reviews for course:', courseId);

    // Start with simple query to avoid index issues
    const q = query(
      this.reviewsCollection,
      where('courseId', '==', courseId)
    );

    const unsubscribe = onSnapshot(q,
      (querySnapshot) => {
        console.log('📊 Firestore snapshot received, docs:', querySnapshot.size);
        const reviews: Review[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          console.log('📄 Review document:', doc.id, data);
          reviews.push({
            id: doc.id,
            ...data
          } as Review);
        });

        // Sort manually by creation date (newest first)
        reviews.sort((a, b) => {
          const aTime = a.createdAt?.toDate?.() || new Date(0);
          const bTime = b.createdAt?.toDate?.() || new Date(0);
          return bTime.getTime() - aTime.getTime();
        });

        console.log('🔄 Real-time reviews update:', reviews.length, 'reviews processed');
        callback(reviews);
      },
      (error) => {
        console.error('❌ Error in reviews subscription:', error);
        // Fallback to manual fetch
        this.getCourseReviews(courseId).then(reviews => {
          console.log('🔄 Fallback: Manual fetch returned', reviews.length, 'reviews');
          callback(reviews);
        }).catch(fallbackError => {
          console.error('❌ Fallback fetch also failed:', fallbackError);
          callback([]);
        });
      }
    );

    return unsubscribe;
  }

  // Get user's review for a specific course
  async getUserReviewForCourse(userId: string, courseId: string): Promise<Review | null> {
    try {
      const q = query(
        this.reviewsCollection,
        where('userId', '==', userId),
        where('courseId', '==', courseId)
      );

      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as Review;
    } catch (error) {
      console.error('❌ Error fetching user review:', error);
      throw error;
    }
  }

  // Update review helpfulness
  async updateReviewHelpfulness(reviewId: string, userId: string, isHelpful: boolean): Promise<void> {
    try {
      const reviewRef = doc(this.reviewsCollection, reviewId);
      
      // Get current review data
      const reviews = await getDocs(query(this.reviewsCollection, where('__name__', '==', reviewId)));
      if (reviews.empty) return;
      
      const reviewData = reviews.docs[0].data() as Review;
      const helpfulUsers = reviewData.helpfulUsers || [];
      const notHelpfulUsers = reviewData.notHelpfulUsers || [];

      // Remove user from both arrays first
      const newHelpfulUsers = helpfulUsers.filter(id => id !== userId);
      const newNotHelpfulUsers = notHelpfulUsers.filter(id => id !== userId);

      // Add user to appropriate array
      if (isHelpful) {
        newHelpfulUsers.push(userId);
      } else {
        newNotHelpfulUsers.push(userId);
      }

      await updateDoc(reviewRef, {
        helpful: newHelpfulUsers.length,
        notHelpful: newNotHelpfulUsers.length,
        helpfulUsers: newHelpfulUsers,
        notHelpfulUsers: newNotHelpfulUsers,
        updatedAt: serverTimestamp()
      });

      console.log('✅ Review helpfulness updated');
    } catch (error) {
      console.error('❌ Error updating review helpfulness:', error);
      throw error;
    }
  }

  // Get review statistics for a course
  async getCourseReviewStats(courseId: string): Promise<ReviewStats> {
    try {
      const reviews = await this.getCourseReviews(courseId);
      
      const stats: ReviewStats = {
        totalReviews: reviews.length,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };

      if (reviews.length === 0) {
        return stats;
      }

      // Calculate average rating and distribution
      let totalRating = 0;
      reviews.forEach(review => {
        totalRating += review.rating;
        stats.ratingDistribution[review.rating as keyof typeof stats.ratingDistribution]++;
      });

      stats.averageRating = Number((totalRating / reviews.length).toFixed(1));

      return stats;
    } catch (error) {
      console.error('❌ Error calculating review stats:', error);
      throw error;
    }
  }

  // Update course rating statistics in courses collection
  private async updateCourseRatingStats(courseId: string): Promise<void> {
    try {
      const stats = await this.getCourseReviewStats(courseId);
      
      // Update the course document with new rating stats
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        rating: stats.averageRating,
        reviewCount: stats.totalReviews,
        ratingDistribution: stats.ratingDistribution,
        updatedAt: serverTimestamp()
      });

      console.log('✅ Course rating stats updated');
    } catch (error) {
      console.error('❌ Error updating course rating stats:', error);
      // Don't throw error as this is not critical
    }
  }

  // Delete a review (admin or user's own review)
  async deleteReview(reviewId: string, userId: string, isAdmin: boolean = false): Promise<void> {
    try {
      // If not admin, verify the review belongs to the user
      if (!isAdmin) {
        const reviews = await getDocs(query(this.reviewsCollection, where('__name__', '==', reviewId)));
        if (reviews.empty) {
          throw new Error('Review not found');
        }
        
        const reviewData = reviews.docs[0].data() as Review;
        if (reviewData.userId !== userId) {
          throw new Error('You can only delete your own reviews');
        }
      }

      await deleteDoc(doc(this.reviewsCollection, reviewId));
      console.log('✅ Review deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting review:', error);
      throw error;
    }
  }
}

export const reviewService = new ReviewService();
