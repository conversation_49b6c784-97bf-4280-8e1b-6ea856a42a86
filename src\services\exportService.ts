/**
 * Export Service for FreeCodeLap Admin Panel
 * Handles data export functionality for various admin operations
 */

import { enrollmentManagementService } from './enrollmentManagementService';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx';
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeFields?: string[];
  excludeFields?: string[];
}

class ExportService {
  /**
   * Export all enrollments data
   */
  async exportEnrollments(options: ExportOptions = { format: 'csv' }): Promise<void> {
    try {
      console.log('📊 Exporting enrollments data...');
      
      const enrollments = await enrollmentManagementService.getAllEnrollments(1000);
      
      // Filter by date range if specified
      let filteredEnrollments = enrollments;
      if (options.dateRange) {
        filteredEnrollments = enrollments.filter(enrollment => {
          const enrolledDate = enrollment.enrolledAt;
          return enrolledDate >= options.dateRange!.start && enrolledDate <= options.dateRange!.end;
        });
      }

      const exportData = filteredEnrollments.map(enrollment => ({
        'Enrollment ID': enrollment.id,
        'User Name': enrollment.userName,
        'User Email': enrollment.userEmail,
        'User Role': enrollment.userRole,
        'Course Title': enrollment.courseTitle,
        'Course Price': enrollment.coursePrice,
        'Enrolled Date': enrollment.enrolledAt.toLocaleDateString(),
        'Progress': `${enrollment.progress}%`,
        'Completed Lessons': enrollment.completedLessons.length,
        'Certificate Earned': enrollment.certificateEarned ? 'Yes' : 'No',
        'Enrollment Type': enrollment.paymentInfo ? 'Paid' : 'Manual',
        'Payment Amount': enrollment.paymentInfo?.amount || 'N/A',
        'Payment Method': enrollment.paymentInfo?.paymentMethod || 'N/A',
        'Last Accessed': enrollment.lastAccessedAt?.toLocaleDateString() || 'Never'
      }));

      await this.downloadData(exportData, 'enrollments', options.format);
      console.log('✅ Enrollments exported successfully');
    } catch (error) {
      console.error('❌ Error exporting enrollments:', error);
      throw error;
    }
  }

  /**
   * Export all users data
   */
  async exportUsers(options: ExportOptions = { format: 'csv' }): Promise<void> {
    try {
      console.log('👥 Exporting users data...');
      
      const usersRef = collection(db, 'users');
      const usersQuery = query(usersRef, orderBy('createdAt', 'desc'));
      const usersSnapshot = await getDocs(usersQuery);
      
      const exportData = usersSnapshot.docs.map(doc => {
        const userData = doc.data();
        return {
          'User ID': doc.id,
          'Display Name': userData.displayName || 'N/A',
          'Email': userData.email || 'N/A',
          'Role': userData.role || 'student',
          'Created Date': userData.createdAt?.toDate()?.toLocaleDateString() || 'N/A',
          'Last Login': userData.lastLoginAt?.toDate()?.toLocaleDateString() || 'N/A',
          'Email Verified': userData.emailVerified ? 'Yes' : 'No',
          'Profile Complete': userData.profileComplete ? 'Yes' : 'No',
          'Enrolled Courses': userData.enrolledCourses?.length || 0
        };
      });

      await this.downloadData(exportData, 'users', options.format);
      console.log('✅ Users exported successfully');
    } catch (error) {
      console.error('❌ Error exporting users:', error);
      throw error;
    }
  }

  /**
   * Export all courses data
   */
  async exportCourses(options: ExportOptions = { format: 'csv' }): Promise<void> {
    try {
      console.log('📚 Exporting courses data...');
      
      const coursesRef = collection(db, 'courses');
      const coursesQuery = query(coursesRef, orderBy('createdAt', 'desc'));
      const coursesSnapshot = await getDocs(coursesQuery);
      
      const exportData = coursesSnapshot.docs.map(doc => {
        const courseData = doc.data();
        return {
          'Course ID': doc.id,
          'Title': courseData.title || 'N/A',
          'Description': courseData.description || 'N/A',
          'Instructor': courseData.instructor || 'N/A',
          'Price': courseData.price || 0,
          'Original Price': courseData.originalPrice || 'N/A',
          'Currency': courseData.currency || 'KES',
          'Category': courseData.category || 'N/A',
          'Skill Level': courseData.skillLevel || 'N/A',
          'Duration': courseData.duration || 'N/A',
          'Published': courseData.published ? 'Yes' : 'No',
          'Featured': courseData.featured ? 'Yes' : 'No',
          'Created Date': courseData.createdAt?.toDate()?.toLocaleDateString() || 'N/A',
          'Updated Date': courseData.updatedAt?.toDate()?.toLocaleDateString() || 'N/A',
          'Enrollment Count': courseData.enrollmentCount || 0,
          'Rating': courseData.rating || 0,
          'Review Count': courseData.reviewCount || 0
        };
      });

      await this.downloadData(exportData, 'courses', options.format);
      console.log('✅ Courses exported successfully');
    } catch (error) {
      console.error('❌ Error exporting courses:', error);
      throw error;
    }
  }

  /**
   * Export all reviews data
   */
  async exportReviews(options: ExportOptions = { format: 'csv' }): Promise<void> {
    try {
      console.log('⭐ Exporting reviews data...');
      
      const reviewsRef = collection(db, 'reviews');
      const reviewsQuery = query(reviewsRef, orderBy('createdAt', 'desc'));
      const reviewsSnapshot = await getDocs(reviewsQuery);
      
      const exportData = reviewsSnapshot.docs.map(doc => {
        const reviewData = doc.data();
        return {
          'Review ID': doc.id,
          'Course ID': reviewData.courseId || 'N/A',
          'User ID': reviewData.userId || 'N/A',
          'User Name': reviewData.userName || 'N/A',
          'User Email': reviewData.userEmail || 'N/A',
          'Rating': reviewData.rating || 0,
          'Comment': reviewData.comment || 'N/A',
          'Created Date': reviewData.createdAt?.toDate()?.toLocaleDateString() || 'N/A',
          'Updated Date': reviewData.updatedAt?.toDate()?.toLocaleDateString() || 'N/A'
        };
      });

      await this.downloadData(exportData, 'reviews', options.format);
      console.log('✅ Reviews exported successfully');
    } catch (error) {
      console.error('❌ Error exporting reviews:', error);
      throw error;
    }
  }

  /**
   * Export complete platform data
   */
  async exportAllData(options: ExportOptions = { format: 'json' }): Promise<void> {
    try {
      console.log('🌐 Exporting all platform data...');
      
      const [enrollments, users, courses, reviews] = await Promise.all([
        enrollmentManagementService.getAllEnrollments(1000),
        this.getAllUsers(),
        this.getAllCourses(),
        this.getAllReviews()
      ]);

      const allData = {
        exportInfo: {
          exportedAt: new Date().toISOString(),
          exportedBy: 'Admin',
          platform: 'FreeCodeLap',
          version: '1.0.0'
        },
        statistics: {
          totalUsers: users.length,
          totalCourses: courses.length,
          totalEnrollments: enrollments.length,
          totalReviews: reviews.length
        },
        data: {
          users,
          courses,
          enrollments,
          reviews
        }
      };

      await this.downloadData(allData, 'complete-platform-data', options.format);
      console.log('✅ Complete platform data exported successfully');
    } catch (error) {
      console.error('❌ Error exporting all data:', error);
      throw error;
    }
  }

  /**
   * Download data in specified format
   */
  private async downloadData(data: any, filename: string, format: 'csv' | 'json' | 'xlsx'): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    const fullFilename = `${filename}-${timestamp}`;

    switch (format) {
      case 'csv':
        this.downloadCSV(data, fullFilename);
        break;
      case 'json':
        this.downloadJSON(data, fullFilename);
        break;
      case 'xlsx':
        // For now, fallback to CSV. XLSX would require additional library
        this.downloadCSV(data, fullFilename);
        break;
      default:
        throw new Error(`Unsupported format: ${format}`);
    }
  }

  /**
   * Download data as CSV
   */
  private downloadCSV(data: any[], filename: string): void {
    if (data.length === 0) {
      throw new Error('No data to export');
    }

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    this.triggerDownload(csvContent, `${filename}.csv`, 'text/csv');
  }

  /**
   * Download data as JSON
   */
  private downloadJSON(data: any, filename: string): void {
    const jsonContent = JSON.stringify(data, null, 2);
    this.triggerDownload(jsonContent, `${filename}.json`, 'application/json');
  }

  /**
   * Trigger file download
   */
  private triggerDownload(content: string, filename: string, mimeType: string): void {
    // Add BOM for CSV files to ensure proper encoding
    const finalContent = mimeType.includes('csv') ? '\uFEFF' + content : content;

    const blob = new Blob([finalContent], { type: mimeType + ';charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
    console.log(`✅ File downloaded: ${filename}`);
  }

  /**
   * Helper methods to get all data
   */
  private async getAllUsers(): Promise<any[]> {
    const usersRef = collection(db, 'users');
    const snapshot = await getDocs(usersRef);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  private async getAllCourses(): Promise<any[]> {
    const coursesRef = collection(db, 'courses');
    const snapshot = await getDocs(coursesRef);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  private async getAllReviews(): Promise<any[]> {
    const reviewsRef = collection(db, 'reviews');
    const snapshot = await getDocs(reviewsRef);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
}

export const exportService = new ExportService();
