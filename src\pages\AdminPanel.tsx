import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Navigation } from '@/components/Navigation';
import { ComprehensiveCourseForm } from '@/components/admin/ComprehensiveCourseForm';
import CourseManagement from '@/components/admin/CourseManagement';
import { UserManagement } from '@/components/admin/UserManagement';
import { NewEnrollmentManagement } from '@/components/admin/NewEnrollmentManagement';
import { AdminSettings } from '@/components/admin/AdminSettings';


import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// Removed Tabs import as we're using custom navigation
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';
import { Course } from '@/types/course';
import { migrateCourseIsFreeProperty } from '@/utils/courseMigration';
import { createTestCourse } from '@/utils/createTestCourse';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Star,
  Users,
  BookOpen,
  PlayCircle,
  Settings,
  BarChart3,
  DollarSign,
  TrendingUp,
  Calendar,
  Shield,
  Database,
  Search,
  Filter,
  Download,
  Upload,
  Mail,
  Bell,
  Globe,
  CreditCard,
  Activity,
  FileText,
  Zap,
  Home,
  PlusCircle,
  GraduationCap,
} from 'lucide-react';

export default function AdminPanel() {
  const { currentUser, isAdmin: checkIsAdmin } = useAuth();
  const navigate = useNavigate();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');

  // Categories for filtering
  const categories = [
    'No-Code Development',
    'AI Development',
    'Web Development',
    'Mobile Development',
    'Data Science',
    'Machine Learning',
    'Backend Development',
    'Frontend Development',
    'DevOps',
    'Cybersecurity',
    'UI/UX Design',
    'Digital Marketing',
    'Business',
    'Programming Fundamentals'
  ];

  // Get admin status from AuthContext
  const isAdmin = checkIsAdmin();

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    if (!isAdmin) {
      navigate('/dashboard');
      return;
    }

    // Load courses for admin
    loadCourses();

    // No cleanup needed for direct async calls
  }, [currentUser, isAdmin, navigate]);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const coursesData = await courseService.getAllCourses();
      setCourses(coursesData);
      console.log('📚 Admin: Loaded courses:', coursesData.length);
    } catch (error) {
      console.error('❌ Admin: Error loading courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleCourseStatus = async (courseId: string, currentStatus: boolean) => {
    try {
      console.log(`🔄 Toggling course ${courseId} from ${currentStatus} to ${!currentStatus}`);
      await courseService.updateCourse(courseId, { isPublished: !currentStatus });
      console.log('✅ Course status toggled successfully');
      // Reload courses to update UI
      loadCourses();
    } catch (error) {
      console.error('❌ Error toggling course status:', error);
    }
  };

  const deleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return;
    }

    try {
      console.log(`🗑️ Deleting course ${courseId}`);
      await courseService.deleteCourse(courseId);
      console.log('✅ Course deleted successfully');
      // Reload courses to update UI
      loadCourses();
    } catch (error) {
      console.error('❌ Error deleting course:', error);
    }
  };

  // Filter courses based on search and filters
  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'published' && course.isPublished) ||
                         (filterStatus === 'draft' && !course.isPublished);

    const matchesCategory = filterCategory === 'all' || course.category === filterCategory;

    return matchesSearch && matchesStatus && matchesCategory;
  });

  // Calculate stats
  const stats = {
    totalCourses: courses.length,
    publishedCourses: courses.filter(c => c.isPublished).length,
    draftCourses: courses.filter(c => !c.isPublished).length,
    totalStudents: courses.reduce((total, course) => total + (course.enrolledCount || 0), 0),
    totalRevenue: courses.reduce((total, course) => total + (course.price * (course.enrolledCount || 0)), 0),
    avgRating: courses.length > 0 ? (courses.reduce((total, course) => total + (course.rating || 0), 0) / courses.length) : 0,
    featuredCourses: courses.filter(c => c.featured).length
  };

  if (!currentUser || !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardContent className="p-6 text-center">
              <Shield className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
              <p className="text-gray-400 mb-6">You don't have permission to access the admin panel.</p>
              <Button onClick={() => navigate('/dashboard')} className="bg-blue-600 hover:bg-blue-700">
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading admin panel...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navigation />
      <div className="pt-16 flex">
        {/* Left Vertical Sidebar */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 min-h-screen">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-2">Admin Panel</h1>
            <p className="text-gray-400 text-sm">Manage your platform</p>
          </div>

          <div className="p-4">
            <div className="space-y-1">
              <button
                type="button"
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'overview'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Home className="h-5 w-5 mr-3" />
                Overview
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('courses')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'courses'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <BookOpen className="h-5 w-5 mr-3" />
                Courses
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('create')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'create'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <PlusCircle className="h-5 w-5 mr-3" />
                Create
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('users')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'users'
                    ? 'bg-purple-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Users className="h-5 w-5 mr-3" />
                Users
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('enrollments')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'enrollments'
                    ? 'bg-green-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <GraduationCap className="h-5 w-5 mr-3" />
                Enrollments
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('analytics')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'analytics'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <BarChart3 className="h-5 w-5 mr-3" />
                Analytics
              </button>

              <button
                type="button"
                onClick={() => setActiveTab('settings')}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'settings'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'text-gray-200 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Settings className="h-5 w-5 mr-3" />
                Settings
              </button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 p-8">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Dashboard Overview</h2>
                <p className="text-gray-400">Monitor your platform performance and key metrics</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <BookOpen className="h-8 w-8 text-blue-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Total Courses</p>
                        <p className="text-2xl font-bold text-white">{stats.totalCourses}</p>
                        <p className="text-xs text-gray-500">{stats.publishedCourses} published, {stats.draftCourses} drafts</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Users className="h-8 w-8 text-green-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Total Students</p>
                        <p className="text-2xl font-bold text-white">{stats.totalStudents.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">Across all courses</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <DollarSign className="h-8 w-8 text-yellow-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Total Revenue</p>
                        <p className="text-2xl font-bold text-white">KES {stats.totalRevenue.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">From course sales</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Star className="h-8 w-8 text-purple-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Average Rating</p>
                        <p className="text-2xl font-bold text-white">{stats.avgRating.toFixed(1)}</p>
                        <p className="text-xs text-gray-500">Course satisfaction</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions & Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Zap className="h-5 w-5 mr-2 text-yellow-400" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button
                      onClick={() => setActiveTab('create')}
                      className="w-full bg-green-600 hover:bg-green-700 justify-start"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Course
                    </Button>
                    <Button
                      onClick={() => setActiveTab('courses')}
                      variant="outline"
                      className="w-full justify-start border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      Manage Courses
                    </Button>
                    <Button
                      onClick={() => setActiveTab('analytics')}
                      variant="outline"
                      className="w-full justify-start border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      View Analytics
                    </Button>
                    <Button
                      onClick={() => setActiveTab('users')}
                      variant="outline"
                      className="w-full justify-start border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Manage Users
                    </Button>

                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-blue-400" />
                      Platform Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 text-gray-300">
                        <div className="h-2 w-2 bg-green-400 rounded-full"></div>
                        <span>Platform Online</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-300">
                        <Database className="h-4 w-4 text-green-400" />
                        <span>Firestore Connected</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-300">
                        <CreditCard className="h-4 w-4 text-blue-400" />
                        <span>Paystack Integration Active</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-300">
                        <Shield className="h-4 w-4 text-purple-400" />
                        <span>Firebase Auth Enabled</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-300">
                        <Globe className="h-4 w-4 text-yellow-400" />
                        <span>{stats.totalCourses} Courses Available</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-green-400" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-700">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                          <Plus className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-white text-sm">New course created</p>
                          <p className="text-gray-400 text-xs">2 hours ago</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-700">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-white text-sm">New student enrollment</p>
                          <p className="text-gray-400 text-xs">5 hours ago</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                          <DollarSign className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-white text-sm">Payment received</p>
                          <p className="text-gray-400 text-xs">1 day ago</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Courses Tab */}
          {activeTab === 'courses' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Course Management</h2>
                <p className="text-gray-400">Manage all courses, modules, and content</p>
              </div>

              {/* Course Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-white">{courses.length}</p>
                    <p className="text-sm text-gray-400">Total Courses</p>
                  </CardContent>
                </Card>
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-green-400">{courses.filter(c => c.isPublished).length}</p>
                    <p className="text-sm text-gray-400">Published</p>
                  </CardContent>
                </Card>
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-yellow-400">{courses.filter(c => !c.isPublished).length}</p>
                    <p className="text-sm text-gray-400">Drafts</p>
                  </CardContent>
                </Card>
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-blue-400">{courses.filter(c => c.featured).length}</p>
                    <p className="text-sm text-gray-400">Featured</p>
                  </CardContent>
                </Card>
              </div>

              {/* Search and Filters */}
              <Card className="bg-gray-800 border-gray-700">
                <CardContent className="p-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Search courses..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                        />
                      </div>
                    </div>
                    <Select value={filterStatus} onValueChange={setFilterStatus}>
                      <SelectTrigger className="w-full sm:w-40 bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-700 border-gray-600">
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={filterCategory} onValueChange={setFilterCategory}>
                      <SelectTrigger className="w-full sm:w-40 bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-700 border-gray-600">
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={() => setActiveTab('create')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      New Course
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Course List */}
              <CourseManagement
                courses={filteredCourses}
                onToggleStatus={toggleCourseStatus}
                onDeleteCourse={deleteCourse}
                onEditCourse={(courseId) => {
                  console.log('Edit course:', courseId);
                  navigate(`/admin/courses/edit/${courseId}`);
                }}
              />
            </div>
          )}

          {/* Create Tab */}
          {activeTab === 'create' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Create New Course</h2>
                <p className="text-gray-400">Add a new course to your platform</p>
              </div>
              <ComprehensiveCourseForm onCourseCreated={() => {
                console.log('Course created - reloading courses');
                loadCourses();
                setActiveTab('courses');
              }} />
            </div>
          )}







          {/* Users Tab */}
          {activeTab === 'users' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">User Management</h2>
                <p className="text-gray-400">Manage platform users, roles, and permissions</p>
              </div>
              <UserManagement />
            </div>
          )}

          {/* Enrollments Tab */}
          {activeTab === 'enrollments' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Enrollment Management</h2>
                <p className="text-gray-400">View and manage course enrollments</p>
              </div>
              <NewEnrollmentManagement />
            </div>
          )}
          {/* Analytics Tab */}
          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Analytics & Reports</h2>
                <p className="text-gray-400">Track performance and engagement metrics</p>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-green-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Growth Rate</p>
                        <p className="text-2xl font-bold text-white">+24%</p>
                        <p className="text-xs text-green-400">↗ vs last month</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Users className="h-8 w-8 text-blue-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Active Students</p>
                        <p className="text-2xl font-bold text-white">{stats.totalStudents}</p>
                        <p className="text-xs text-blue-400">↗ +12 this week</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <DollarSign className="h-8 w-8 text-yellow-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Monthly Revenue</p>
                        <p className="text-2xl font-bold text-white">KES {Math.floor(stats.totalRevenue * 0.3).toLocaleString()}</p>
                        <p className="text-xs text-yellow-400">↗ +18% vs last month</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Star className="h-8 w-8 text-purple-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-400">Avg Rating</p>
                        <p className="text-2xl font-bold text-white">{stats.avgRating.toFixed(1)}</p>
                        <p className="text-xs text-purple-400">↗ +0.2 vs last month</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
                      Course Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {courses.slice(0, 5).map((course, index) => (
                        <div key={course.id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-300 text-sm truncate">{course.title}</span>
                            <div className="flex items-center gap-2 text-sm">
                              <span className="text-green-400">{course.enrolledCount || 0}</span>
                              <span className="text-yellow-400">{course.rating || 0}★</span>
                            </div>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(((course.enrolledCount || 0) / 100) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <DollarSign className="h-5 w-5 mr-2 text-green-400" />
                      Revenue by Course
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {courses.slice(0, 5).map((course) => {
                        const revenue = course.price * (course.enrolledCount || 0);
                        const maxRevenue = Math.max(...courses.map(c => c.price * (c.enrolledCount || 0)));
                        return (
                          <div key={course.id} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-gray-300 text-sm truncate">{course.title}</span>
                              <span className="text-green-400 text-sm">
                                KES {revenue.toLocaleString()}
                              </span>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${maxRevenue > 0 ? (revenue / maxRevenue) * 100 : 0}%` }}
                              ></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Export Options */}
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Download className="h-5 w-5 mr-2 text-blue-400" />
                    Export Reports
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Button variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-600 hover:text-white font-medium">
                      <FileText className="h-4 w-4 mr-2" />
                      Course Report (PDF)
                    </Button>
                    <Button variant="outline" className="border-green-500 text-green-400 hover:bg-green-600 hover:text-white font-medium">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Analytics (CSV)
                    </Button>

                  </div>
                </CardContent>
              </Card>

              {/* Migration Tools */}
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    Database Migration Tools
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-white font-medium mb-2">Course Free Lessons Migration</h4>
                      <p className="text-gray-400 text-sm mb-3">
                        Run this migration to add the 'isFree' property to existing course lessons.
                        This ensures proper free/locked lesson functionality in course detail pages.
                      </p>
                      <Button
                        onClick={async () => {
                          const result = await migrateCourseIsFreeProperty();
                          if (result.success) {
                            alert(`Migration completed! Updated ${result.updatedCourses} courses and ${result.updatedLessons} lessons.`);
                          } else {
                            alert(`Migration failed: ${result.error}`);
                          }
                        }}
                        className="bg-orange-600 hover:bg-orange-700"
                      >
                        <Database className="h-4 w-4 mr-2" />
                        Run isFree Migration
                      </Button>
                    </div>

                    <div>
                      <h4 className="text-white font-medium mb-2">Video Player Test Course</h4>
                      <p className="text-gray-400 text-sm mb-3">
                        Create a test course with proper video URLs to debug the video player functionality.
                        This course will have both free and paid lessons with working video URLs.
                      </p>
                      <Button
                        onClick={async () => {
                          try {
                            const courseId = await createTestCourse();
                            alert(`Test course created successfully!\nCourse ID: ${courseId}\n\nYou can now:\n1. Preview free lessons at /course/${courseId}/preview\n2. Test full course at /course/${courseId}/content`);
                          } catch (error) {
                            alert(`Failed to create test course: ${error}`);
                          }
                        }}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        <PlayCircle className="h-4 w-4 mr-2" />
                        Create Test Course
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}









          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <AdminSettings />
          )}





        </div>
      </div>
    </div>
  );
}
