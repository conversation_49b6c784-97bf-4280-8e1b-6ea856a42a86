/**
 * Debug utility for enrollment access issues
 */

import { enrollmentService } from '@/services/enrollmentService';
import { enrollmentManagementService } from '@/services/enrollmentManagementService';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export const debugEnrollmentAccess = async (userId: string, courseId: string) => {
  console.log('🔧 DEBUG: Enrollment Access Issue');
  console.log('=====================================');
  console.log('User ID:', userId);
  console.log('Course ID:', courseId);
  console.log('');

  try {
    // Step 1: Check what the enrollment service returns
    console.log('🔍 Step 1: Testing enrollment service...');
    const isEnrolled = await enrollmentService.isUserEnrolled(userId, courseId);
    console.log('📊 Enrollment service result:', isEnrolled);
    console.log('');

    // Step 2: Check all enrollments for this course
    console.log('🔍 Step 2: Getting all enrollments for course...');
    const courseEnrollments = await enrollmentManagementService.getCourseEnrollments(courseId);
    console.log('📊 Total enrollments for course:', courseEnrollments.length);
    
    courseEnrollments.forEach((enrollment, index) => {
      console.log(`📋 Enrollment ${index + 1}:`, {
        id: enrollment.id,
        userId: enrollment.userId,
        userEmail: enrollment.userEmail,
        userName: enrollment.userName,
        enrolledAt: enrollment.enrolledAt.toLocaleDateString()
      });
    });
    console.log('');

    // Step 3: Check user profile
    console.log('🔍 Step 3: Checking user profile...');
    try {
      const userDoc = await import('firebase/firestore').then(({ doc, getDoc }) => 
        getDoc(doc(db, 'users', userId))
      );
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log('👤 User profile found:', {
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role
        });
      } else {
        console.log('❌ User profile not found');
      }
    } catch (error) {
      console.log('❌ Error getting user profile:', error);
    }
    console.log('');

    // Step 4: Direct enrollment queries
    console.log('🔍 Step 4: Direct enrollment queries...');
    const enrollmentsRef = collection(db, 'enrollments');
    
    // Query 1: By user ID
    const userIdQuery = query(
      enrollmentsRef,
      where('userId', '==', userId),
      where('courseId', '==', courseId)
    );
    const userIdResults = await getDocs(userIdQuery);
    console.log('📊 Query by user ID:', userIdResults.docs.length, 'results');
    
    // Query 2: All enrollments for course
    const courseQuery = query(
      enrollmentsRef,
      where('courseId', '==', courseId)
    );
    const courseResults = await getDocs(courseQuery);
    console.log('📊 All enrollments for course:', courseResults.docs.length, 'results');
    
    courseResults.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`📋 Raw enrollment ${index + 1}:`, {
        docId: doc.id,
        userId: data.userId,
        originalEmail: data.originalEmail,
        enrolledUserName: data.enrolledUserName,
        manualEnrollment: data.manualEnrollment
      });
    });

    console.log('');
    console.log('🎯 DIAGNOSIS:');
    
    if (isEnrolled) {
      console.log('✅ User should have access - enrollment found');
    } else {
      console.log('❌ User does not have access - no enrollment found');
      
      if (courseEnrollments.length > 0) {
        console.log('💡 POSSIBLE ISSUES:');
        console.log('1. User ID mismatch - check if enrollment uses email as userId');
        console.log('2. Email mismatch - check if user email matches enrollment email');
        console.log('3. Course ID mismatch - verify course ID is correct');
        
        const emailBasedEnrollments = courseEnrollments.filter(e => 
          e.userId.includes('@') || e.userEmail !== 'Unknown'
        );
        
        if (emailBasedEnrollments.length > 0) {
          console.log('📧 Found email-based enrollments - user might need to match email');
        }
      } else {
        console.log('💡 No enrollments found for this course - user needs to be enrolled first');
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
};

export const quickEnrollmentCheck = async (userEmail: string, courseId: string) => {
  console.log('⚡ Quick Enrollment Check');
  console.log('========================');
  console.log('Email:', userEmail);
  console.log('Course:', courseId);
  
  try {
    const enrollmentsRef = collection(db, 'enrollments');
    
    // Check by email as userId
    const emailQuery = query(
      enrollmentsRef,
      where('userId', '==', userEmail),
      where('courseId', '==', courseId)
    );
    
    const emailResults = await getDocs(emailQuery);
    console.log('📊 Email as userId results:', emailResults.docs.length);
    
    // Check by originalEmail
    const originalEmailQuery = query(
      enrollmentsRef,
      where('originalEmail', '==', userEmail),
      where('courseId', '==', courseId)
    );
    
    const originalEmailResults = await getDocs(originalEmailQuery);
    console.log('📊 Original email results:', originalEmailResults.docs.length);
    
    const hasAccess = emailResults.docs.length > 0 || originalEmailResults.docs.length > 0;
    console.log('🎯 Has access:', hasAccess);
    
    return hasAccess;
  } catch (error) {
    console.error('❌ Quick check failed:', error);
    return false;
  }
};

// Helper to run debug from browser console
if (typeof window !== 'undefined') {
  (window as any).debugEnrollmentAccess = debugEnrollmentAccess;
  (window as any).quickEnrollmentCheck = quickEnrollmentCheck;
  console.log('🔧 Debug functions available:');
  console.log('• debugEnrollmentAccess(userId, courseId)');
  console.log('• quickEnrollmentCheck(userEmail, courseId)');
}
