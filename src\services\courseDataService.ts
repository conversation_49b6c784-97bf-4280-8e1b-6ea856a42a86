import { doc, setDoc, getDoc, collection, getDocs, query, where, orderBy, deleteDoc, updateDoc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Course, CourseModule, Lesson, Enrollment, LessonProgress } from '@/types/course';

export class CourseDataService {
  // Create initial course data
  static async createInitialCourses() {
    const courses: Course[] = [
      {
        id: 'flutterflow-nocode',
        title: 'FlutterFlow No-Code Development',
        description: 'Master the art of building beautiful mobile and web applications without writing a single line of code. Learn FlutterFlow from basics to advanced features including custom functions, API integrations, and app deployment.',
        shortDescription: 'Build mobile apps without coding using FlutterFlow',
        thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Preview video
        instructor: '<PERSON>',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Senior Software Engineer with 10+ years of experience in mobile development and no-code solutions.',
        duration: '8 weeks',
        totalDuration: 2400, // 40 hours in minutes
        level: 'Beginner',
        category: 'No-Code Development',
        price: 4999,
        originalPrice: 6999,
        currency: 'KES',
        tags: ['FlutterFlow', 'No-Code', 'Mobile Apps', 'Web Apps', 'Firebase'],
        modules: [],
        requirements: ['Basic computer skills', 'Internet connection', 'Willingness to learn'],
        learningOutcomes: [
          'Build complete mobile apps without coding',
          'Master FlutterFlow interface and components',
          'Integrate Firebase backend services',
          'Deploy apps to App Store and Google Play',
          'Create custom functions and workflows',
          'Design responsive user interfaces'
        ],
        targetAudience: ['Entrepreneurs', 'Designers', 'Non-technical founders', 'Students'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 234,
        enrolledCount: 234,
        rating: 4.8,
        reviewCount: 89,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      },
      {
        id: 'vibe-coding-ai',
        title: 'Vibe Coding – AI Tools & Productivity with VS Code, Cursor, Agents',
        description: 'Revolutionize your coding workflow with AI-powered development tools. Master VS Code, Cursor, GitHub Copilot, and AI agents to boost your productivity and build applications faster than ever before.',
        shortDescription: 'Boost coding productivity with AI tools and automation',
        thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Preview video
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Senior Software Engineer with expertise in AI tools and developer productivity.',
        duration: '10 weeks',
        totalDuration: 3000, // 50 hours in minutes
        level: 'Intermediate',
        category: 'AI Development',
        price: 5999,
        originalPrice: 7999,
        currency: 'KES',
        tags: ['AI Tools', 'VS Code', 'Cursor', 'GitHub Copilot', 'Productivity', 'Automation'],
        modules: [],
        requirements: ['Basic programming knowledge', 'Familiarity with code editors', 'GitHub account'],
        learningOutcomes: [
          'Master AI-powered coding tools',
          'Boost productivity with VS Code extensions',
          'Use Cursor for AI-assisted development',
          'Implement GitHub Copilot effectively',
          'Create custom AI agents for coding',
          'Automate repetitive development tasks'
        ],
        targetAudience: ['Developers', 'Software Engineers', 'Tech Leads', 'Programming Students'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 189,
        enrolledCount: 189,
        rating: 4.9,
        reviewCount: 67,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      },
      {
        id: 'react-fullstack-web',
        title: 'React Full-Stack Web Development',
        description: 'Build modern web applications with React, Node.js, and MongoDB. Learn frontend and backend development, API integration, authentication, and deployment to create production-ready web applications.',
        shortDescription: 'Complete full-stack web development with React and Node.js',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Full-stack developer with expertise in React, Node.js, and modern web technologies.',
        duration: '12 weeks',
        totalDuration: 3600, // 60 hours in minutes
        level: 'Intermediate',
        category: 'Web Development',
        price: 6999,
        originalPrice: 8999,
        currency: 'KES',
        tags: ['React', 'Node.js', 'MongoDB', 'Full-Stack', 'JavaScript', 'API'],
        modules: [],
        requirements: ['Basic JavaScript knowledge', 'HTML/CSS fundamentals', 'Git basics'],
        learningOutcomes: [
          'Build complete React applications',
          'Create RESTful APIs with Node.js',
          'Implement user authentication',
          'Work with MongoDB databases',
          'Deploy applications to production',
          'Master modern development workflows'
        ],
        targetAudience: ['Web Developers', 'Frontend Developers', 'Programming Students', 'Career Changers'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 156,
        enrolledCount: 156,
        rating: 4.7,
        reviewCount: 43,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      },
      {
        id: 'python-data-science',
        title: 'Python for Data Science & Machine Learning',
        description: 'Master data science and machine learning with Python. Learn pandas, NumPy, matplotlib, scikit-learn, and TensorFlow to analyze data, build predictive models, and create intelligent applications.',
        shortDescription: 'Complete Python data science and machine learning course',
        thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Data scientist and machine learning engineer with 8+ years of experience in Python and AI.',
        duration: '14 weeks',
        totalDuration: 4200, // 70 hours in minutes
        level: 'Advanced',
        category: 'Data Science',
        price: 7999,
        originalPrice: 9999,
        currency: 'KES',
        tags: ['Python', 'Data Science', 'Machine Learning', 'AI', 'pandas', 'TensorFlow'],
        modules: [],
        requirements: ['Basic Python knowledge', 'Mathematics fundamentals', 'Statistics basics'],
        learningOutcomes: [
          'Analyze large datasets with pandas',
          'Create data visualizations',
          'Build machine learning models',
          'Implement deep learning with TensorFlow',
          'Deploy ML models to production',
          'Work with real-world data projects'
        ],
        targetAudience: ['Data Analysts', 'Software Engineers', 'Researchers', 'Business Analysts'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 98,
        enrolledCount: 98,
        rating: 4.9,
        reviewCount: 31,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      },
      {
        id: 'react-fullstack-web',
        title: 'React Full-Stack Web Development',
        description: 'Build modern web applications with React, Node.js, and MongoDB. Learn frontend and backend development, API integration, authentication, and deployment to create production-ready web applications.',
        shortDescription: 'Complete full-stack web development with React and Node.js',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Full-stack developer with expertise in React, Node.js, and modern web technologies.',
        duration: '12 weeks',
        totalDuration: 3600,
        level: 'Intermediate',
        category: 'Web Development',
        price: 6999,
        originalPrice: 8999,
        currency: 'KES',
        tags: ['React', 'Node.js', 'MongoDB', 'Full-Stack', 'JavaScript', 'API'],
        modules: [],
        requirements: ['Basic JavaScript knowledge', 'HTML/CSS fundamentals', 'Git basics'],
        learningOutcomes: [
          'Build complete React applications',
          'Create RESTful APIs with Node.js',
          'Implement user authentication',
          'Work with MongoDB databases',
          'Deploy applications to production',
          'Master modern development workflows'
        ],
        targetAudience: ['Web Developers', 'Frontend Developers', 'Programming Students', 'Career Changers'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 156,
        enrolledCount: 156,
        rating: 4.7,
        reviewCount: 43,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      },
      {
        id: 'python-data-science',
        title: 'Python for Data Science & Machine Learning',
        description: 'Master data science and machine learning with Python. Learn pandas, NumPy, matplotlib, scikit-learn, and TensorFlow to analyze data, build predictive models, and create intelligent applications.',
        shortDescription: 'Complete Python data science and machine learning course',
        thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        instructorBio: 'Data scientist and machine learning engineer with 8+ years of experience in Python and AI.',
        duration: '14 weeks',
        totalDuration: 4200,
        level: 'Advanced',
        category: 'Data Science',
        price: 7999,
        originalPrice: 9999,
        currency: 'KES',
        tags: ['Python', 'Data Science', 'Machine Learning', 'AI', 'pandas', 'TensorFlow'],
        modules: [],
        requirements: ['Basic Python knowledge', 'Mathematics fundamentals', 'Statistics basics'],
        learningOutcomes: [
          'Analyze large datasets with pandas',
          'Create data visualizations',
          'Build machine learning models',
          'Implement deep learning with TensorFlow',
          'Deploy ML models to production',
          'Work with real-world data projects'
        ],
        targetAudience: ['Data Analysts', 'Software Engineers', 'Researchers', 'Business Analysts'],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: true,
        featured: true,
        status: 'published',
        enrollmentCount: 98,
        enrolledCount: 98,
        rating: 4.9,
        reviewCount: 31,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime'
      }
    ];

    // Create modules for FlutterFlow course
    const flutterflowModules: CourseModule[] = [
      {
        id: 'ff-module-1',
        courseId: 'flutterflow-nocode',
        title: 'FlutterFlow Fundamentals',
        description: 'Get started with FlutterFlow basics and interface',
        order: 1,
        lessons: [],
        assignments: [],
        quizzes: [],
        isLocked: false
      },
      {
        id: 'ff-module-2',
        courseId: 'flutterflow-nocode',
        title: 'Building Your First App',
        description: 'Create a complete mobile application from scratch',
        order: 2,
        lessons: [],
        assignments: [],
        quizzes: [],
        isLocked: false
      },
      {
        id: 'ff-module-3',
        courseId: 'flutterflow-nocode',
        title: 'Advanced Features & Deployment',
        description: 'Master advanced FlutterFlow features and deploy your app',
        order: 3,
        lessons: [],
        assignments: [],
        quizzes: [],
        isLocked: false
      }
    ];

    // Create lessons for FlutterFlow Module 1
    const ffModule1Lessons: Lesson[] = [
      {
        id: 'ff-lesson-1-1',
        moduleId: 'ff-module-1',
        title: 'Introduction to FlutterFlow',
        description: 'Overview of FlutterFlow and no-code development',
        type: 'video',
        order: 1,
        duration: 15,
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        textContent: '',
        resources: [],
        isCompleted: false,
        isLocked: false,
        canPreview: true
      },
      {
        id: 'ff-lesson-1-2',
        moduleId: 'ff-module-1',
        title: 'Setting up your FlutterFlow Account',
        description: 'Create and configure your FlutterFlow workspace',
        type: 'video',
        order: 2,
        duration: 20,
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        textContent: '',
        resources: [],
        isCompleted: false,
        isLocked: false,
        canPreview: false
      },
      {
        id: 'ff-lesson-1-3',
        moduleId: 'ff-module-1',
        title: 'Understanding the FlutterFlow Interface',
        description: 'Navigate the FlutterFlow development environment',
        type: 'video',
        order: 3,
        duration: 25,
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        textContent: '',
        resources: [],
        isCompleted: false,
        isLocked: false,
        canPreview: false
      }
    ];

    // Create modules for AI Coding course
    const aiCodingModules: CourseModule[] = [
      {
        id: 'ai-module-1',
        courseId: 'vibe-coding-ai',
        title: 'AI Development Environment Setup',
        description: 'Configure your AI-powered development environment',
        order: 1,
        lessons: [],
        assignments: [],
        quizzes: [],
        isLocked: false
      },
      {
        id: 'ai-module-2',
        courseId: 'vibe-coding-ai',
        title: 'AI-Assisted Coding Techniques',
        description: 'Master AI tools for faster development',
        order: 2,
        lessons: [],
        assignments: [],
        quizzes: [],
        isLocked: false
      }
    ];

    // Create lessons for AI Coding Module 1
    const aiModule1Lessons: Lesson[] = [
      {
        id: 'ai-lesson-1-1',
        moduleId: 'ai-module-1',
        title: 'Setting up VS Code for AI Development',
        description: 'Configure VS Code with AI extensions and tools',
        type: 'video',
        order: 1,
        duration: 20,
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        textContent: '',
        resources: [],
        isCompleted: false,
        isLocked: false,
        canPreview: true
      },
      {
        id: 'ai-lesson-1-2',
        moduleId: 'ai-module-1',
        title: 'Installing and Configuring Cursor',
        description: 'Set up Cursor IDE for AI-assisted coding',
        type: 'video',
        order: 2,
        duration: 25,
        videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        textContent: '',
        resources: [],
        isCompleted: false,
        isLocked: false,
        canPreview: false
      }
    ];

    try {
      console.log('🔧 Creating initial course data...');

      // Create courses (only if they don't exist)
      for (const course of courses) {
        const courseRef = doc(db, 'courses', course.id);
        const courseSnap = await getDoc(courseRef);

        if (!courseSnap.exists()) {
          await setDoc(courseRef, course);
          console.log(`✅ Created course: ${course.title}`);
        } else {
          console.log(`ℹ️ Course already exists: ${course.title}`);
        }
      }

      // Create modules
      const allModules = [...flutterflowModules, ...aiCodingModules];
      for (const module of allModules) {
        const moduleRef = doc(db, 'modules', module.id);
        await setDoc(moduleRef, module);
        console.log(`✅ Created module: ${module.title}`);
      }

      // Create lessons
      const allLessons = [...ffModule1Lessons, ...aiModule1Lessons];
      for (const lesson of allLessons) {
        const lessonRef = doc(db, 'lessons', lesson.id);
        await setDoc(lessonRef, lesson);
        console.log(`✅ Created lesson: ${lesson.title}`);
      }

      console.log('🎉 Initial course data created successfully!');
      return true;
    } catch (error) {
      console.error('❌ Error creating course data:', error);
      return false;
    }
  }

  // Get all courses
  static async getAllCourses(): Promise<Course[]> {
    try {
      const coursesRef = collection(db, 'courses');
      // Get all courses first, then filter for published ones
      const snapshot = await getDocs(query(coursesRef, orderBy('createdAt', 'desc')));

      const allCourses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Course));

      // Filter for published courses - check both isPublished and published fields
      const publishedCourses = allCourses.filter(course =>
        course.isPublished === true || (course as any).published === true
      );

      // Return published courses, or all if none are marked as published
      return publishedCourses.length > 0 ? publishedCourses : allCourses;
    } catch (error) {
      console.error('Error fetching courses:', error);
      return [];
    }
  }

  // Get featured courses
  static async getFeaturedCourses(): Promise<Course[]> {
    try {
      const coursesRef = collection(db, 'courses');
      // Get all courses first, then filter
      const snapshot = await getDocs(query(coursesRef, orderBy('createdAt', 'desc')));

      const allCourses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Course));

      // Filter for published and featured courses
      const featuredCourses = allCourses.filter(course =>
        (course.isPublished === true || (course as any).published === true) &&
        course.featured === true
      );

      return featuredCourses;
    } catch (error) {
      console.error('Error fetching featured courses:', error);
      return [];
    }
  }

  // Get single course by ID
  static async getCourseById(courseId: string): Promise<Course | null> {
    try {
      const courseRef = doc(db, 'courses', courseId);
      const courseSnap = await getDoc(courseRef);

      if (courseSnap.exists()) {
        return {
          id: courseSnap.id,
          ...courseSnap.data()
        } as Course;
      }

      return null;
    } catch (error) {
      console.error('Error fetching course by ID:', error);
      return null;
    }
  }

  // Delete course by ID
  static async deleteCourse(courseId: string): Promise<boolean> {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await deleteDoc(courseRef);
      console.log(`🗑️ Course deleted: ${courseId}`);
      return true;
    } catch (error) {
      console.error('❌ Error deleting course:', error);
      return false;
    }
  }

  // Update course
  static async updateCourse(courseId: string, updates: Partial<Course>): Promise<boolean> {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        ...updates,
        updatedAt: new Date()
      });
      console.log(`✅ Course updated: ${courseId}`);
      return true;
    } catch (error) {
      console.error('❌ Error updating course:', error);
      return false;
    }
  }

  // Toggle course published status
  static async toggleCourseStatus(courseId: string, isPublished: boolean): Promise<boolean> {
    try {
      return await this.updateCourse(courseId, { isPublished });
    } catch (error) {
      console.error('❌ Error toggling course status:', error);
      return false;
    }
  }

  // Get all courses for admin (including unpublished)
  static async getAllCoursesForAdmin(): Promise<Course[]> {
    try {
      const coursesRef = collection(db, 'courses');
      const q = query(coursesRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Course));
    } catch (error) {
      console.error('Error fetching admin courses:', error);
      return [];
    }
  }

  // Real-time listener for courses
  static subscribeToCoursesForAdmin(callback: (courses: Course[]) => void): () => void {
    try {
      const coursesRef = collection(db, 'courses');
      const q = query(coursesRef, orderBy('createdAt', 'desc'));

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const courses = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Course));
        callback(courses);
      });

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up courses listener:', error);
      return () => {};
    }
  }

  // Real-time listener for published courses
  static subscribeToPublishedCourses(callback: (courses: Course[]) => void): () => void {
    try {
      const coursesRef = collection(db, 'courses');
      const q = query(coursesRef, orderBy('createdAt', 'desc'));

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const allCourses = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Course));

        // Filter for published courses - check both isPublished and published fields
        const publishedCourses = allCourses.filter(course =>
          course.isPublished === true || (course as any).published === true
        );

        // Return published courses, or all if none are marked as published
        const coursesToReturn = publishedCourses.length > 0 ? publishedCourses : allCourses;
        callback(coursesToReturn);
      });

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up published courses listener:', error);
      return () => {};
    }
  }
}

// Auto-initialization disabled to prevent recreating courses on every page load
// To initialize sample courses, call CourseDataService.createInitialCourses() manually
// if (typeof window !== 'undefined') {
//   CourseDataService.createInitialCourses().catch(console.error);
// }
