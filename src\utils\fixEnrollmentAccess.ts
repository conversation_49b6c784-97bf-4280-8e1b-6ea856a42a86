/**
 * Utility to fix enrollment access issues
 */

import { collection, query, where, getDocs, doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { enrollmentService } from '@/services/enrollmentService';

export interface EnrollmentIssue {
  type: 'user_id_mismatch' | 'email_mismatch' | 'missing_enrollment' | 'no_issue';
  description: string;
  suggestedFix?: string;
  enrollmentData?: any;
}

/**
 * Diagnose enrollment access issues
 */
export const diagnoseEnrollmentIssue = async (userId: string, courseId: string): Promise<EnrollmentIssue> => {
  try {
    console.log('🔍 Diagnosing enrollment issue for user:', userId, 'course:', courseId);

    // Step 1: Check if enrollment service finds the user
    const isEnrolled = await enrollmentService.isUserEnrolled(userId, courseId);
    
    if (isEnrolled) {
      return {
        type: 'no_issue',
        description: 'User is properly enrolled and should have access'
      };
    }

    // Step 2: Get user profile to find email
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      return {
        type: 'missing_enrollment',
        description: 'User profile not found',
        suggestedFix: 'User needs to complete registration'
      };
    }

    const userData = userDoc.data();
    const userEmail = userData.email;

    if (!userEmail) {
      return {
        type: 'missing_enrollment',
        description: 'User has no email in profile',
        suggestedFix: 'User needs to update their profile with email'
      };
    }

    // Step 3: Check all enrollments for this course
    const enrollmentsRef = collection(db, 'enrollments');
    const courseEnrollmentsQuery = query(
      enrollmentsRef,
      where('courseId', '==', courseId)
    );
    
    const courseEnrollments = await getDocs(courseEnrollmentsQuery);
    
    if (courseEnrollments.empty) {
      return {
        type: 'missing_enrollment',
        description: 'No enrollments found for this course',
        suggestedFix: 'User needs to be manually enrolled or purchase the course'
      };
    }

    // Step 4: Look for enrollments that might match this user
    let potentialMatch = null;
    
    courseEnrollments.docs.forEach(doc => {
      const data = doc.data();
      
      // Check if enrollment uses email as userId
      if (data.userId === userEmail) {
        potentialMatch = {
          type: 'user_id_mismatch',
          description: 'Found enrollment with email as userId, but user lookup failed',
          suggestedFix: 'Update enrollment userId to actual user ID',
          enrollmentData: { docId: doc.id, ...data }
        };
      }
      
      // Check if enrollment has originalEmail matching user
      if (data.originalEmail === userEmail) {
        potentialMatch = {
          type: 'user_id_mismatch',
          description: 'Found enrollment with matching originalEmail but different userId',
          suggestedFix: 'Update enrollment userId to actual user ID',
          enrollmentData: { docId: doc.id, ...data }
        };
      }
    });

    if (potentialMatch) {
      return potentialMatch;
    }

    return {
      type: 'missing_enrollment',
      description: 'No matching enrollment found for this user',
      suggestedFix: 'User needs to be manually enrolled or purchase the course'
    };

  } catch (error) {
    console.error('❌ Error diagnosing enrollment issue:', error);
    return {
      type: 'missing_enrollment',
      description: 'Error occurred during diagnosis',
      suggestedFix: 'Check console for detailed error information'
    };
  }
};

/**
 * Attempt to fix enrollment access issues
 */
export const fixEnrollmentAccess = async (userId: string, courseId: string): Promise<boolean> => {
  try {
    console.log('🔧 Attempting to fix enrollment access for user:', userId, 'course:', courseId);

    const issue = await diagnoseEnrollmentIssue(userId, courseId);
    
    if (issue.type === 'no_issue') {
      console.log('✅ No issue found - user should already have access');
      return true;
    }

    if (issue.type === 'user_id_mismatch' && issue.enrollmentData) {
      console.log('🔧 Fixing user ID mismatch...');
      
      // Update the enrollment document to use the correct userId
      const enrollmentRef = doc(db, 'enrollments', issue.enrollmentData.docId);
      await updateDoc(enrollmentRef, {
        userId: userId,
        fixedAt: new Date(),
        fixNote: 'Updated userId to match authenticated user'
      });
      
      console.log('✅ Fixed enrollment userId');
      return true;
    }

    console.log('❌ Cannot automatically fix issue:', issue.description);
    console.log('💡 Suggested fix:', issue.suggestedFix);
    return false;

  } catch (error) {
    console.error('❌ Error fixing enrollment access:', error);
    return false;
  }
};

/**
 * Quick test function for browser console
 */
export const testEnrollmentAccess = async (userId: string, courseId: string) => {
  console.log('🧪 Testing Enrollment Access');
  console.log('============================');
  
  const issue = await diagnoseEnrollmentIssue(userId, courseId);
  
  console.log('📊 Diagnosis:', issue.type);
  console.log('📝 Description:', issue.description);
  
  if (issue.suggestedFix) {
    console.log('💡 Suggested fix:', issue.suggestedFix);
  }
  
  if (issue.enrollmentData) {
    console.log('📋 Enrollment data:', issue.enrollmentData);
  }
  
  if (issue.type !== 'no_issue' && issue.type !== 'missing_enrollment') {
    console.log('');
    console.log('🔧 Attempting automatic fix...');
    const fixed = await fixEnrollmentAccess(userId, courseId);
    
    if (fixed) {
      console.log('✅ Issue fixed! User should now have access.');
      console.log('🔄 Refresh the page to see changes.');
    } else {
      console.log('❌ Could not automatically fix the issue.');
    }
  }
};

// Make functions available in browser console for debugging
if (typeof window !== 'undefined') {
  (window as any).testEnrollmentAccess = testEnrollmentAccess;
  (window as any).diagnoseEnrollmentIssue = diagnoseEnrollmentIssue;
  (window as any).fixEnrollmentAccess = fixEnrollmentAccess;
  
  console.log('🔧 Enrollment fix functions available:');
  console.log('• testEnrollmentAccess(userId, courseId)');
  console.log('• diagnoseEnrollmentIssue(userId, courseId)');
  console.log('• fixEnrollmentAccess(userId, courseId)');
}
