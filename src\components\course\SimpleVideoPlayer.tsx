import React, { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  SkipBack,
  SkipForward,
  RotateCcw,
  RotateCw,
  ExternalLink
} from 'lucide-react';

interface SimpleVideoPlayerProps {
  videoUrl: string;
  title?: string;
  onProgress?: (progress: number) => void;
  onComplete?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export function SimpleVideoPlayer({
  videoUrl,
  title,
  onProgress,
  onComplete,
  onNext,
  onPrevious
}: SimpleVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [skipIndicator, setSkipIndicator] = useState<{ show: boolean; direction: 'forward' | 'backward'; seconds: number }>({
    show: false,
    direction: 'forward',
    seconds: 10
  });

  // Helper functions for video URL detection
  const isYouTubeUrl = (url: string) => {
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  const isVimeoUrl = (url: string) => {
    return url.includes('vimeo.com');
  };

  const getYouTubeVideoId = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  const getVimeoVideoId = (url: string) => {
    const regExp = /vimeo\.com\/(?:.*#|.*\/videos\/)?([0-9]+)/;
    const match = url.match(regExp);
    return match ? match[1] : null;
  };

  const getEmbedUrl = (url: string) => {
    if (isYouTubeUrl(url)) {
      const videoId = getYouTubeVideoId(url);
      return videoId ? `https://www.youtube.com/embed/${videoId}?autoplay=0&controls=1&rel=0` : null;
    }

    if (isVimeoUrl(url)) {
      const videoId = getVimeoVideoId(url);
      return videoId ? `https://player.vimeo.com/video/${videoId}` : null;
    }

    return null;
  };

  const isEmbeddedVideo = isYouTubeUrl(videoUrl) || isVimeoUrl(videoUrl);
  const embedUrl = getEmbedUrl(videoUrl);

  // Reset state when video URL changes
  useEffect(() => {
    setIsLoading(true);
    setError(null);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
  }, [videoUrl]);

  // Video event handlers
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsLoading(false);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      setCurrentTime(current);
      
      if (onProgress && duration > 0) {
        onProgress((current / duration) * 100);
      }
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    if (onComplete) {
      onComplete();
    }
  };

  const handleError = () => {
    if (isYouTubeUrl(videoUrl)) {
      setError('YouTube URL detected but failed to load. Please check the video URL or try a direct MP4 link.');
    } else if (isVimeoUrl(videoUrl)) {
      setError('Vimeo URL detected but failed to load. Please check the video URL or try a direct MP4 link.');
    } else {
      setError('Failed to load video. Please check the video URL and ensure it\'s a valid MP4 file.');
    }
    setIsLoading(false);
  };

  const handleCanPlay = () => {
    setIsLoading(false);
    setError(null);
  };

  // Control functions
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (videoRef.current) {
      const seekTime = (parseFloat(e.target.value) / 100) * duration;
      videoRef.current.currentTime = seekTime;
      setCurrentTime(seekTime);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value) / 100;
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (videoRef.current) {
      if (isMuted) {
        videoRef.current.volume = volume;
        setIsMuted(false);
      } else {
        videoRef.current.volume = 0;
        setIsMuted(true);
      }
    }
  };

  const toggleFullscreen = () => {
    if (videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        videoRef.current.requestFullscreen();
      }
    }
  };

  const skip = (seconds: number) => {
    if (videoRef.current) {
      const newTime = videoRef.current.currentTime + seconds;
      videoRef.current.currentTime = Math.max(0, Math.min(newTime, duration));

      // Show skip indicator
      setSkipIndicator({
        show: true,
        direction: seconds > 0 ? 'forward' : 'backward',
        seconds: Math.abs(seconds)
      });

      // Hide indicator after 1 second
      setTimeout(() => {
        setSkipIndicator(prev => ({ ...prev, show: false }));
      }, 1000);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Only handle keyboard shortcuts when video player is focused or visible
      if (document.activeElement?.tagName === 'INPUT') return;

      switch (e.key) {
        case ' ':
        case 'k':
          e.preventDefault();
          togglePlay();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          skip(-10);
          break;
        case 'ArrowRight':
          e.preventDefault();
          skip(10);
          break;
        case 'j':
          e.preventDefault();
          skip(-10);
          break;
        case 'l':
          e.preventDefault();
          skip(10);
          break;
        case 'm':
          e.preventDefault();
          toggleMute();
          break;
        case 'f':
          e.preventDefault();
          toggleFullscreen();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isPlaying, duration]);

  // Double-click handlers for skip functionality
  const handleDoubleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const centerX = rect.width / 2;

    if (clickX < centerX) {
      // Double-click on left side - skip backward
      skip(-10);
    } else {
      // Double-click on right side - skip forward
      skip(10);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!videoUrl) {
    return (
      <div className="w-full h-full bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <Play className="w-8 h-8" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No Video Available</h3>
          <p className="text-gray-400">Please add a video URL to this course</p>
        </div>
      </div>
    );
  }

  // Handle embedded videos (YouTube, Vimeo)
  if (isEmbeddedVideo && embedUrl) {
    return (
      <div className="relative w-full h-full bg-black">
        <iframe
          src={embedUrl}
          className="w-full h-full"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          title={title || 'Video Player'}
        />

        {/* Navigation Controls for Embedded Videos */}
        <div className="absolute bottom-4 right-4 flex space-x-2">
          {onPrevious && (
            <Button
              onClick={onPrevious}
              variant="secondary"
              size="sm"
              className="bg-black bg-opacity-50 text-white hover:bg-opacity-70"
            >
              <SkipBack className="w-4 h-4" />
            </Button>
          )}
          {onNext && (
            <Button
              onClick={onNext}
              variant="secondary"
              size="sm"
              className="bg-black bg-opacity-50 text-white hover:bg-opacity-70"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Title Overlay for Embedded Videos */}
        {title && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black to-transparent p-4">
            <h3 className="text-white text-lg font-semibold">{title}</h3>
            <div className="flex items-center space-x-2 mt-1">
              <ExternalLink className="w-4 h-4 text-gray-300" />
              <span className="text-sm text-gray-300">
                {isYouTubeUrl(videoUrl) ? 'YouTube Video' : 'Vimeo Video'}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className="relative w-full h-full bg-black group"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
      onDoubleClick={handleDoubleClick}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleEnded}
        onError={handleError}
        onCanPlay={handleCanPlay}
        onLoadStart={() => setIsLoading(true)}
        preload="metadata"
      >
        <source src={videoUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading video...</p>
          </div>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white max-w-md">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <RotateCcw className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Video Error</h3>
            <p className="text-gray-300 mb-4">{error}</p>
            <p className="text-sm text-gray-400">Video URL: {videoUrl}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline" 
              className="mt-4"
            >
              Reload Page
            </Button>
          </div>
        </div>
      )}

      {/* Double-click zones for skip functionality */}
      <div className="absolute inset-0 flex pointer-events-none">
        {/* Left zone for backward skip */}
        <div className="w-1/2 h-full flex items-center justify-center opacity-0 group-hover:opacity-20 transition-opacity duration-200 bg-gradient-to-r from-black to-transparent">
          <div className="text-white text-center">
            <RotateCcw className="w-8 h-8 mx-auto mb-1" />
            <span className="text-sm">Double-click<br/>-10s</span>
          </div>
        </div>
        {/* Right zone for forward skip */}
        <div className="w-1/2 h-full flex items-center justify-center opacity-0 group-hover:opacity-20 transition-opacity duration-200 bg-gradient-to-l from-black to-transparent">
          <div className="text-white text-center">
            <RotateCw className="w-8 h-8 mx-auto mb-1" />
            <span className="text-sm">Double-click<br/>+10s</span>
          </div>
        </div>
      </div>

      {/* Skip Indicator Overlay */}
      {skipIndicator.show && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
          <div className="bg-black bg-opacity-75 rounded-lg px-4 py-2 flex items-center space-x-2 text-white">
            {skipIndicator.direction === 'backward' ? (
              <RotateCcw className="w-6 h-6" />
            ) : (
              <RotateCw className="w-6 h-6" />
            )}
            <span className="text-lg font-medium">
              {skipIndicator.direction === 'backward' ? '-' : '+'}{skipIndicator.seconds}s
            </span>
          </div>
        </div>
      )}

      {/* Play Button Overlay */}
      {!isPlaying && !isLoading && !error && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            onClick={togglePlay}
            size="lg"
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-white border-2 rounded-full w-20 h-20"
          >
            <Play className="w-8 h-8 ml-1" />
          </Button>
        </div>
      )}

      {/* Controls */}
      {showControls && !isLoading && !error && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          {/* Progress Bar */}
          <div className="mb-4">
            <input
              type="range"
              min="0"
              max="100"
              value={duration > 0 ? (currentTime / duration) * 100 : 0}
              onChange={handleSeek}
              className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
              aria-label="Video progress"
              title="Seek video position"
            />
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-4">
              {/* Previous */}
              {onPrevious && (
                <Button
                  onClick={onPrevious}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  <SkipBack className="w-4 h-4" />
                </Button>
              )}

              {/* Play/Pause */}
              <Button
                onClick={togglePlay}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white hover:bg-opacity-20"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>

              {/* Next */}
              {onNext && (
                <Button
                  onClick={onNext}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  <SkipForward className="w-4 h-4" />
                </Button>
              )}

              {/* Skip buttons */}
              <Button
                onClick={() => skip(-10)}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white hover:bg-opacity-20 relative group"
                title="Replay 10 seconds (J key or Left arrow)"
              >
                <RotateCcw className="w-4 h-4" />
                <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  -10s
                </span>
              </Button>
              <Button
                onClick={() => skip(10)}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white hover:bg-opacity-20 relative group"
                title="Forward 10 seconds (L key or Right arrow)"
              >
                <RotateCw className="w-4 h-4" />
                <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  +10s
                </span>
              </Button>

              {/* Time */}
              <span className="text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center space-x-4">
              {/* Volume */}
              <div className="flex items-center space-x-2">
                <Button
                  onClick={toggleMute}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={isMuted ? 0 : volume * 100}
                  onChange={handleVolumeChange}
                  className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  aria-label="Volume control"
                  title="Adjust volume"
                />
              </div>

              {/* Fullscreen */}
              <Button
                onClick={toggleFullscreen}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white hover:bg-opacity-20"
              >
                <Maximize className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Title Overlay */}
      {title && showControls && (
        <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black to-transparent p-4">
          <h3 className="text-white text-lg font-semibold">{title}</h3>
        </div>
      )}
    </div>
  );
}
