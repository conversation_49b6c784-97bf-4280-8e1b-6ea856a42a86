import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface SimpleReview {
  id?: string;
  courseId: string;
  userId: string;
  userName: string;
  userEmail: string;
  rating: number;
  comment: string;
  createdAt: Timestamp;
}

class SimpleReviewService {
  private reviewsCollection = collection(db, 'courseReviews');

  // Submit a new review
  async submitReview(reviewData: Omit<SimpleReview, 'id' | 'createdAt'>): Promise<string> {
    try {
      console.log('📝 Submitting simple review:', reviewData);

      const review: Omit<SimpleReview, 'id'> = {
        ...reviewData,
        createdAt: serverTimestamp() as Timestamp,
      };

      const docRef = await addDoc(this.reviewsCollection, review);
      console.log('✅ Simple review submitted successfully:', docRef.id);

      return docRef.id;
    } catch (error) {
      console.error('❌ Error submitting simple review:', error);
      throw error;
    }
  }

  // Get all reviews for a course
  async getCourseReviews(courseId: string): Promise<SimpleReview[]> {
    try {
      console.log('📖 Fetching simple reviews for course:', courseId);
      console.log('📖 Using collection:', this.reviewsCollection.path);

      // Try with orderBy first, fallback to simple query if it fails
      let querySnapshot;
      try {
        const q = query(
          this.reviewsCollection,
          where('courseId', '==', courseId),
          orderBy('createdAt', 'desc')
        );
        console.log('📖 Executing query with orderBy...');
        querySnapshot = await getDocs(q);
      } catch (orderError) {
        console.warn('⚠️ OrderBy query failed, trying simple query:', orderError);
        const simpleQ = query(
          this.reviewsCollection,
          where('courseId', '==', courseId)
        );
        console.log('📖 Executing simple query...');
        querySnapshot = await getDocs(simpleQ);
      }
      console.log('📖 Query completed. Documents found:', querySnapshot.size);

      const reviews: SimpleReview[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        console.log('📄 Review document:', doc.id, data);
        reviews.push({
          id: doc.id,
          ...data
        } as SimpleReview);
      });

      // Sort manually by creation date (newest first) if we used simple query
      reviews.sort((a, b) => {
        const aTime = a.createdAt?.toDate?.() || new Date(0);
        const bTime = b.createdAt?.toDate?.() || new Date(0);
        return bTime.getTime() - aTime.getTime();
      });

      console.log('✅ Fetched simple reviews:', reviews.length, reviews);
      return reviews;
    } catch (error) {
      console.error('❌ Error fetching simple reviews:', error);
      console.error('❌ Error details:', error);
      // Return empty array instead of throwing
      return [];
    }
  }

  // Get review statistics
  getReviewStats(reviews: SimpleReview[]) {
    if (reviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0
      };
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = Number((totalRating / reviews.length).toFixed(1));

    return {
      totalReviews: reviews.length,
      averageRating
    };
  }
}

export const simpleReviewService = new SimpleReviewService();
