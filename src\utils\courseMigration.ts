import { db } from '@/lib/firebase';
import { collection, getDocs, doc, updateDoc, getDoc } from 'firebase/firestore';

/**
 * Migration utility to add isFree property to existing course lessons
 * This should be run once to update existing courses in the database
 */
export const migrateCourseIsFreeProperty = async () => {
  try {
    console.log('🔄 Starting course migration for isFree property...');
    
    // Get all courses
    const coursesRef = collection(db, 'courses');
    const coursesSnapshot = await getDocs(coursesRef);
    
    let updatedCourses = 0;
    let updatedLessonsCount = 0;
    
    for (const courseDoc of coursesSnapshot.docs) {
      const courseData = courseDoc.data();
      const courseId = courseDoc.id;
      
      console.log(`📄 Processing course: ${courseData.title} (${courseId})`);
      
      if (courseData.modules && Array.isArray(courseData.modules)) {
        let courseNeedsUpdate = false;
        
        const updatedModules = courseData.modules.map((module: any) => {
          if (module.lessons && Array.isArray(module.lessons)) {
            const updatedLessons = module.lessons.map((lesson: any) => {
              // If lesson doesn't have isFree property, add it
              if (lesson.isFree === undefined) {
                console.log(`  ➕ Adding isFree property to lesson: ${lesson.title}`);
                courseNeedsUpdate = true;
                updatedLessonsCount++;
                return {
                  ...lesson,
                  isFree: false // Default to locked/paid
                };
              }
              return lesson;
            });
            
            return {
              ...module,
              lessons: updatedLessons
            };
          }
          return module;
        });
        
        // Update the course if any lessons were modified
        if (courseNeedsUpdate) {
          const courseRef = doc(db, 'courses', courseId);
          await updateDoc(courseRef, {
            modules: updatedModules,
            updatedAt: new Date()
          });
          
          updatedCourses++;
          console.log(`✅ Updated course: ${courseData.title}`);
        } else {
          console.log(`⏭️ Course already up to date: ${courseData.title}`);
        }
      }
    }
    
    console.log(`🎉 Migration completed!`);
    console.log(`📊 Updated ${updatedCourses} courses`);
    console.log(`📊 Updated ${updatedLessonsCount} lessons`);

    return {
      success: true,
      updatedCourses,
      updatedLessons: updatedLessonsCount
    };
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Utility to set specific lessons as free for a course
 * @param courseId - The course ID
 * @param freeLessonIds - Array of lesson IDs to mark as free
 */
export const setLessonsAsFree = async (courseId: string, freeLessonIds: string[]) => {
  try {
    console.log(`🔄 Setting lessons as free for course: ${courseId}`);
    
    const courseRef = doc(db, 'courses', courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    const courseData = courseDoc.data();
    
    if (courseData?.modules && Array.isArray(courseData.modules)) {
      const updatedModules = courseData.modules.map((module: any) => {
        if (module.lessons && Array.isArray(module.lessons)) {
          const updatedLessons = module.lessons.map((lesson: any) => {
            if (freeLessonIds.includes(lesson.id)) {
              console.log(`  🔓 Setting lesson as free: ${lesson.title}`);
              return {
                ...lesson,
                isFree: true
              };
            }
            return lesson;
          });
          
          return {
            ...module,
            lessons: updatedLessons
          };
        }
        return module;
      });
      
      await updateDoc(courseRef, {
        modules: updatedModules,
        updatedAt: new Date()
      });
      
      console.log(`✅ Updated free lessons for course: ${courseData.title}`);
      return { success: true };
    }
    
    throw new Error('Course has no modules');
    
  } catch (error) {
    console.error('❌ Failed to set lessons as free:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
