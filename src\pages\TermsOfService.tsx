import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileText, Users, CreditCard, AlertTriangle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const TermsOfService: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/')}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Terms of Service</h1>
              <p className="text-sm text-gray-600">Last updated: December 12, 2024</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="bg-green-50 border-b">
            <CardTitle className="flex items-center space-x-2 text-green-900">
              <FileText className="w-6 h-6" />
              <span>FreeCodeLap Terms of Service</span>
            </CardTitle>
            <p className="text-green-700 mt-2">
              Please read these terms carefully before using our online learning platform.
            </p>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* Acceptance */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-900 text-sm">
                  By accessing and using FreeCodeLap ("the Platform"), you accept and agree to be bound by these Terms of Service. 
                  If you do not agree to these terms, please do not use our services.
                </p>
                <div className="mt-3 text-blue-800 text-sm">
                  <strong>Effective Date:</strong> December 12, 2024<br />
                  <strong>Governing Law:</strong> Laws of Kenya<br />
                  <strong>Business Registration:</strong> FreeCodeLap, Kenya
                </div>
              </div>
            </section>

            {/* Service Description */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Service Description</h2>
              <div className="space-y-4">
                <p className="text-gray-700">
                  FreeCodeLap provides online courses and educational content focused on mobile app development, 
                  particularly FlutterFlow and related technologies.
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">What We Provide:</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Video-based learning courses</li>
                      <li>Interactive assignments and quizzes</li>
                      <li>Progress tracking and certificates</li>
                      <li>Community support and forums</li>
                      <li>Live classes and workshops</li>
                      <li>Course materials and resources</li>
                    </ul>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Service Availability:</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>24/7 access to course content</li>
                      <li>Mobile and desktop compatibility</li>
                      <li>Offline content download (where available)</li>
                      <li>Regular content updates</li>
                      <li>Technical support during business hours</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* User Accounts */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span>3. User Accounts and Responsibilities</span>
              </h2>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 mb-2">Account Requirements:</h3>
                  <ul className="list-disc list-inside text-yellow-800 text-sm space-y-1">
                    <li>You must be at least 16 years old to create an account</li>
                    <li>Provide accurate and complete registration information</li>
                    <li>Maintain the security of your account credentials</li>
                    <li>Use a valid email address for account verification</li>
                    <li>One account per person (no sharing accounts)</li>
                  </ul>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-medium text-red-900 mb-2">Prohibited Activities:</h3>
                  <ul className="list-disc list-inside text-red-800 text-sm space-y-1">
                    <li>Sharing account credentials with others</li>
                    <li>Downloading or redistributing course content</li>
                    <li>Using automated tools to access the platform</li>
                    <li>Attempting to hack or compromise platform security</li>
                    <li>Posting inappropriate or offensive content</li>
                    <li>Violating intellectual property rights</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Payment Terms */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <CreditCard className="w-5 h-5 text-green-600" />
                <span>4. Payment Terms and Pricing</span>
              </h2>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium text-green-900 mb-2">Payment Methods:</h3>
                    <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                      <li>Credit/Debit Cards (Visa, Mastercard, Verve)</li>
                      <li>M-Pesa Mobile Money</li>
                      <li>Bank Transfer</li>
                      <li>USSD Payments</li>
                    </ul>
                    <p className="text-green-800 text-xs mt-2">
                      All payments processed securely by Paystack
                    </p>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Pricing Policy:</h3>
                    <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                      <li>Prices displayed in Kenyan Shillings (KES)</li>
                      <li>One-time payment per course</li>
                      <li>No hidden fees or recurring charges</li>
                      <li>Prices subject to change with notice</li>
                    </ul>
                  </div>
                </div>
                
                <div className="border-l-4 border-orange-500 pl-4 bg-orange-50 p-4 rounded">
                  <h3 className="font-medium text-orange-900 mb-2">Payment Processing:</h3>
                  <p className="text-orange-800 text-sm">
                    Payments are processed immediately upon successful transaction. Course access is granted automatically 
                    after payment confirmation. Payment failures will be notified immediately with retry options.
                  </p>
                </div>
              </div>
            </section>

            {/* Intellectual Property */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">5. Intellectual Property Rights</h2>
              <div className="space-y-4">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h3 className="font-medium text-purple-900 mb-2">Our Content:</h3>
                  <p className="text-purple-800 text-sm mb-2">
                    All course content, including videos, text, images, code examples, and materials are owned by 
                    FreeCodeLap or our content partners and are protected by copyright laws.
                  </p>
                  <ul className="list-disc list-inside text-purple-800 text-sm space-y-1">
                    <li>Content is licensed for personal, non-commercial use only</li>
                    <li>No downloading, copying, or redistribution allowed</li>
                    <li>Screenshots and recordings are prohibited</li>
                    <li>Course certificates are for personal use only</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">Your Content:</h3>
                  <p className="text-gray-700 text-sm">
                    Content you submit (forum posts, assignments, feedback) remains yours, but you grant us 
                    a license to use it for platform improvement and educational purposes.
                  </p>
                </div>
              </div>
            </section>

            {/* Course Access */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">6. Course Access and Completion</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-900 mb-2">Access Rights:</h3>
                  <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                    <li>Lifetime access to purchased courses</li>
                    <li>Access to course updates and improvements</li>
                    <li>Mobile and desktop compatibility</li>
                    <li>Progress tracking and bookmarking</li>
                    <li>Certificate upon completion</li>
                  </ul>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-medium text-yellow-900 mb-2">Completion Requirements:</h3>
                  <ul className="list-disc list-inside text-yellow-800 text-sm space-y-1">
                    <li>Complete all required lessons</li>
                    <li>Pass quizzes with minimum 70% score</li>
                    <li>Submit required assignments</li>
                    <li>Participate in mandatory discussions</li>
                    <li>Final project submission (if applicable)</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Refund Policy */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">7. Refund and Cancellation Policy</h2>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">30-Day Money-Back Guarantee:</h3>
                  <p className="text-blue-800 text-sm mb-2">
                    We offer a full refund within 30 days of purchase if you're not satisfied with the course.
                  </p>
                  <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                    <li>Request refund within 30 days of purchase</li>
                    <li>Must have completed less than 30% of course content</li>
                    <li>Contact support with reason for refund</li>
                    <li>Refunds processed within 5-7 business days</li>
                    <li>Refund amount excludes payment processing fees</li>
                  </ul>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-medium text-red-900 mb-2">No Refund Conditions:</h3>
                  <ul className="list-disc list-inside text-red-800 text-sm space-y-1">
                    <li>After 30 days from purchase date</li>
                    <li>If more than 30% of content has been accessed</li>
                    <li>For completed courses with certificates issued</li>
                    <li>If account was terminated for policy violations</li>
                    <li>For promotional or discounted purchases (case by case)</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Disclaimers */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <span>8. Disclaimers and Limitations</span>
              </h2>
              <div className="space-y-4">
                <div className="border-l-4 border-orange-500 pl-4 bg-orange-50 p-4 rounded">
                  <h3 className="font-medium text-orange-900 mb-2">Service Availability:</h3>
                  <p className="text-orange-800 text-sm">
                    While we strive for 99.9% uptime, we cannot guarantee uninterrupted service. Maintenance, 
                    updates, or technical issues may temporarily affect platform availability.
                  </p>
                </div>
                
                <div className="border-l-4 border-gray-500 pl-4 bg-gray-50 p-4 rounded">
                  <h3 className="font-medium text-gray-900 mb-2">Educational Outcomes:</h3>
                  <p className="text-gray-700 text-sm">
                    Course completion does not guarantee employment, income, or specific career outcomes. 
                    Success depends on individual effort, market conditions, and application of learned skills.
                  </p>
                </div>
                
                <div className="border-l-4 border-red-500 pl-4 bg-red-50 p-4 rounded">
                  <h3 className="font-medium text-red-900 mb-2">Limitation of Liability:</h3>
                  <p className="text-red-800 text-sm">
                    FreeCodeLap's liability is limited to the amount paid for the course. We are not liable for 
                    indirect, incidental, or consequential damages arising from platform use.
                  </p>
                </div>
              </div>
            </section>

            {/* Termination */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">9. Account Termination</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">User-Initiated:</h3>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    <li>You may delete your account anytime</li>
                    <li>Contact support for account deletion</li>
                    <li>Course access ends upon deletion</li>
                    <li>Data retention per Privacy Policy</li>
                  </ul>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="font-medium text-red-900 mb-2">Platform-Initiated:</h3>
                  <ul className="list-disc list-inside text-red-800 text-sm space-y-1">
                    <li>Violation of terms of service</li>
                    <li>Fraudulent payment activity</li>
                    <li>Abuse of platform or other users</li>
                    <li>No refund for terminated accounts</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Contact */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">10. Contact Information</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <p className="text-blue-900 mb-4">
                  For questions about these Terms of Service, please contact us:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">Support Contact:</h3>
                    <p className="text-blue-800 text-sm">
                      Email: <EMAIL><br />
                      Phone: +************<br />
                      Response Time: Within 24 hours
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">Business Information:</h3>
                    <p className="text-blue-800 text-sm">
                      Business Name: FreeCodeLap<br />
                      Location: Kenya<br />
                      Registration: Educational Services
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Updates */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">11. Terms Updates</h2>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-4 rounded">
                <p className="text-blue-900 text-sm mb-2">
                  We may update these Terms of Service to reflect changes in our services or legal requirements.
                </p>
                <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                  <li>30 days notice for material changes</li>
                  <li>Email notification to all users</li>
                  <li>Continued use constitutes acceptance</li>
                  <li>Right to terminate if you disagree</li>
                </ul>
                <p className="text-blue-900 text-sm mt-2">
                  <strong>Last Updated:</strong> December 12, 2024
                </p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TermsOfService;
