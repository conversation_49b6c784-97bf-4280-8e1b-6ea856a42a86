import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  UserPlus, 
  CreditCard, 
  BookOpen, 
  ArrowRight,
  CheckCircle,
  Play,
  Award
} from 'lucide-react';

const HowItWorksSection = () => {
  const steps = [
    {
      number: "01",
      icon: <UserPlus className="h-8 w-8 text-blue-500" />,
      title: "Register",
      description: "Create your free account and explore our course catalog. Browse course details, watch preview videos, and choose your learning path.",
      features: [
        "Free account creation",
        "Course catalog access",
        "Preview videos",
        "Learning path guidance"
      ],
      color: "blue"
    },
    {
      number: "02", 
      icon: <CreditCard className="h-8 w-8 text-green-500" />,
      title: "Pay",
      description: "Choose your course and pay securely with Paystack. Multiple payment options including M-Pesa, cards, and bank transfers available.",
      features: [
        "Secure Paystack payment",
        "Multiple payment methods",
        "M-Pesa integration",
        "Instant course access"
      ],
      color: "green"
    },
    {
      number: "03",
      icon: <BookOpen className="h-8 w-8 text-purple-500" />,
      title: "Start Learning",
      description: "Access your course immediately and start building real projects. Learn at your own pace with lifetime access to all materials.",
      features: [
        "Immediate course access",
        "Hands-on projects",
        "Self-paced learning",
        "Lifetime access"
      ],
      color: "purple"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: {
        bg: "bg-blue-500",
        text: "text-blue-500",
        border: "border-blue-500",
        gradient: "from-blue-500 to-blue-600"
      },
      green: {
        bg: "bg-green-500",
        text: "text-green-500", 
        border: "border-green-500",
        gradient: "from-green-500 to-green-600"
      },
      purple: {
        bg: "bg-purple-500",
        text: "text-purple-500",
        border: "border-purple-500", 
        gradient: "from-purple-500 to-purple-600"
      }
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            How It Works
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get started with FreeCodeLap in three simple steps. 
            From registration to building your first app, we've made the process seamless.
          </p>
        </div>

        {/* Steps */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {steps.map((step, index) => {
            const colorClasses = getColorClasses(step.color);
            
            return (
              <div key={index} className="relative">
                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gray-600 z-0">
                    <ArrowRight className="absolute -top-2 -right-1 h-5 w-5 text-gray-600" />
                  </div>
                )}

                <Card className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gray-800 border-gray-700 relative z-10 h-full">
                  {/* Step Number */}
                  <div className={`absolute -top-4 -left-4 w-12 h-12 ${colorClasses.bg} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                    {step.number}
                  </div>

                  <CardHeader className="pt-8 pb-4">
                    <div className="flex items-center gap-4 mb-4">
                      <div className={`p-3 rounded-full bg-gradient-to-br ${colorClasses.gradient} bg-opacity-20`}>
                        {step.icon}
                      </div>
                      <CardTitle className={`text-2xl font-bold text-white group-hover:${colorClasses.text} transition-colors`}>
                        {step.title}
                      </CardTitle>
                    </div>
                    <p className="text-gray-300 leading-relaxed">
                      {step.description}
                    </p>
                  </CardHeader>

                  <CardContent>
                    <ul className="space-y-2">
                      {step.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2 text-gray-300">
                          <CheckCircle className={`h-4 w-4 ${colorClasses.text}`} />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-900 to-purple-900 rounded-lg p-8 border border-gray-700">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Start Your Learning Journey?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Join hundreds of students who have already transformed their careers with FreeCodeLap. 
              Start building real applications today!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Link to="/auth?mode=register">
                  <UserPlus className="mr-2 h-5 w-5" />
                  Create Free Account
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
                <Link to="/courses">
                  <Play className="mr-2 h-5 w-5" />
                  Browse Courses
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Success Metrics */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-3xl font-bold text-blue-400 mb-2">95%</div>
            <div className="text-gray-300">Course Completion Rate</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-3xl font-bold text-green-400 mb-2">24hrs</div>
            <div className="text-gray-300">Average Time to First App</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-3xl font-bold text-purple-400 mb-2">100%</div>
            <div className="text-gray-300">Money-Back Guarantee</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
