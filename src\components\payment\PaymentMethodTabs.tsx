import React, { useState } from 'react';
import { PaystackButton } from 'react-paystack';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Smartphone, Lock, CheckCircle } from 'lucide-react';
import { PaymentData, paymentService } from '@/services/paymentService';

interface PaymentMethodTabsProps {
  paymentData: PaymentData;
  onSuccess: (result: any) => void;
  onError: (error: string) => void;
}

const PaymentMethodTabs: React.FC<PaymentMethodTabsProps> = ({
  paymentData,
  onSuccess,
  onError,
}) => {
  const [activeTab, setActiveTab] = useState<'card' | 'mpesa'>('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Card form state
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: paymentData.name,
  });

  // M-Pesa form state
  const [mpesaData, setMpesaData] = useState({
    phone: paymentData.phone_number || '',
  });

  // M-Pesa STK status
  const [stkStatus, setStkStatus] = useState<'idle' | 'sent' | 'success' | 'failed'>('idle');

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const formatPhone = (value: string) => {
    let phone = value.replace(/\D/g, '');
    if (phone.startsWith('0')) {
      phone = '254' + phone.slice(1);
    } else if (!phone.startsWith('254')) {
      phone = '254' + phone;
    }
    return phone;
  };

  // Create Paystack configuration for card payments
  const getPaystackConfig = () => {
    const config = paymentService.createPaystackConfig({
      ...paymentData,
      payment_method: 'card',
    });

    return {
      ...config,
      channels: ['card'], // Only cards for this tab
    };
  };

  const handlePaystackSuccess = async (response: any) => {
    setIsProcessing(true);
    setError(null);

    try {
      // Verify the transaction with Paystack
      const verificationResult = await paymentService.verifyPaystackTransaction(response.reference);

      if (verificationResult.status === 'success') {
        onSuccess({
          transaction_id: verificationResult.transaction_id,
          reference: response.reference,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_method: 'card',
          paystack_response: response,
        });
      } else {
        setError(verificationResult.message);
        onError(verificationResult.message);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Payment verification failed. Please try again.';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaystackClose = () => {
    setError('Payment was cancelled');
    onError('Payment was cancelled');
  };

  // Create Paystack configuration for M-Pesa payments
  const getMpesaPaystackConfig = () => {
    const config = paymentService.createPaystackConfig({
      ...paymentData,
      phone_number: mpesaData.phone,
      payment_method: 'mpesa',
    });

    return {
      ...config,
      channels: ['mobile_money'], // Only mobile money for M-Pesa
    };
  };

  const handleMpesaPaystackSuccess = async (response: any) => {
    setStkStatus('success');
    setIsProcessing(true);

    try {
      // Verify the transaction with Paystack
      const verificationResult = await paymentService.verifyPaystackTransaction(response.reference);

      if (verificationResult.status === 'success') {
        onSuccess({
          transaction_id: verificationResult.transaction_id,
          reference: response.reference,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_method: 'mpesa',
          phone: mpesaData.phone,
          paystack_response: response,
        });
      } else {
        setStkStatus('failed');
        setError(verificationResult.message);
        onError(verificationResult.message);
      }
    } catch (err: any) {
      setStkStatus('failed');
      const errorMessage = err.message || 'M-Pesa payment verification failed.';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleMpesaPaystackClose = () => {
    setStkStatus('failed');
    setError('M-Pesa payment was cancelled');
    onError('M-Pesa payment was cancelled');
  };

  const getCardType = (number: string) => {
    const num = number.replace(/\s/g, '');
    if (num.startsWith('4')) return 'visa';
    if (num.startsWith('5')) return 'mastercard';
    if (num.startsWith('506')) return 'verve';
    return 'card';
  };

  if (stkStatus === 'sent') {
    return (
      <Card className="card-elevated">
        <CardContent className="p-8 text-center">
          <div className="bg-green-500/20 p-4 rounded-full w-16 h-16 mx-auto mb-4">
            <Smartphone className="w-8 h-8 text-green-400 mx-auto animate-pulse" />
          </div>
          <h3 className="text-xl font-bold text-white mb-2">Check Your Phone</h3>
          <p className="text-blue-200 mb-4">
            We've sent an M-Pesa STK push to <strong>{mpesaData.phone}</strong>
          </p>
          <p className="text-sm text-blue-300 mb-6">
            Enter your M-Pesa PIN on your phone to complete the payment
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-sm text-blue-300 mb-4">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
            <span>Waiting for payment confirmation...</span>
          </div>

          <Button
            onClick={() => {
              setStkStatus('idle');
              setIsProcessing(false);
            }}
            variant="ghost"
            className="btn-animated btn-ghost"
          >
            Cancel & Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (stkStatus === 'success') {
    return (
      <Card className="card-elevated">
        <CardContent className="p-8 text-center">
          <div className="bg-green-500/20 p-4 rounded-full w-16 h-16 mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-400 mx-auto" />
          </div>
          <h3 className="text-xl font-bold text-white mb-2">Payment Successful!</h3>
          <p className="text-blue-200">
            Your M-Pesa payment has been processed successfully.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="card-elevated">
      <CardContent className="p-6">
        {/* Payment Method Tabs */}
        <div className="flex space-x-1 mb-6 bg-blue-dark-800/50 p-1 rounded-lg">
          <button
            type="button"
            onClick={() => setActiveTab('card')}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all ${
              activeTab === 'card'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'text-blue-300 hover:text-white hover:bg-blue-dark-700'
            }`}
          >
            <CreditCard className="w-4 h-4" />
            <span className="font-medium">Card</span>
          </button>

          {paymentData.currency === 'KES' && (
            <button
              type="button"
              onClick={() => setActiveTab('mpesa')}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all ${
                activeTab === 'mpesa'
                  ? 'bg-green-600 text-white shadow-lg'
                  : 'text-blue-300 hover:text-white hover:bg-blue-dark-700'
              }`}
            >
              <Smartphone className="w-4 h-4" />
              <span className="font-medium">M-Pesa</span>
            </button>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <Alert className="border-red-500/50 bg-red-500/10 mb-6">
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Card Payment Form */}
        {activeTab === 'card' && (
          <div className="space-y-6">
            <div className="text-center">
              <div className="bg-blue-500/20 p-3 rounded-full w-12 h-12 mx-auto mb-3">
                <CreditCard className="w-6 h-6 text-blue-400 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-white">Card Payment</h3>
              <p className="text-blue-200 text-sm">Pay securely with your credit or debit card</p>
            </div>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CreditCard className="w-5 h-5 text-blue-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-blue-400 font-medium mb-1">Supported Cards:</p>
                  <ul className="text-blue-300 space-y-1">
                    <li>• Visa, Mastercard, Verve</li>
                    <li>• Secure 3D authentication</li>
                    <li>• Instant payment processing</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2 text-sm text-blue-300 py-2">
              <Lock className="w-4 h-4" />
              <span>Your payment information is secure and encrypted</span>
            </div>

            <PaystackButton
              {...getPaystackConfig()}
              onSuccess={handlePaystackSuccess}
              onClose={handlePaystackClose}
              className="w-full btn-animated btn-blue py-3 text-lg font-medium rounded-lg"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Verifying Payment...
                </>
              ) : (
                <>
                  <CreditCard className="w-5 h-5 mr-2" />
                  Pay {paymentData.currency} {paymentData.amount.toLocaleString()}
                </>
              )}
            </PaystackButton>
          </div>
        )}

        {/* M-Pesa Payment Form */}
        {activeTab === 'mpesa' && (
          <div className="space-y-6">
            <div className="text-center">
              <div className="bg-green-500/20 p-3 rounded-full w-12 h-12 mx-auto mb-3">
                <Smartphone className="w-6 h-6 text-green-400 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-white">M-Pesa Payment</h3>
              <p className="text-blue-200 text-sm">Pay securely with your M-Pesa account</p>
            </div>

            <div>
              <Label htmlFor="mpesa-phone" className="text-blue-200 text-sm font-medium">
                M-Pesa Phone Number
              </Label>
              <Input
                id="mpesa-phone"
                type="tel"
                value={mpesaData.phone}
                onChange={(e) => setMpesaData(prev => ({ ...prev, phone: formatPhone(e.target.value) }))}
                placeholder="************"
                className="bg-white border-gray-300 text-gray-900"
                required
              />
              <p className="text-xs text-blue-300 mt-1">
                Enter your M-Pesa registered phone number
              </p>
            </div>

            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Smartphone className="w-5 h-5 text-green-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-green-400 font-medium mb-1">How it works:</p>
                  <ul className="text-green-300 space-y-1">
                    <li>• You'll receive an STK push notification</li>
                    <li>• Enter your M-Pesa PIN to confirm</li>
                    <li>• Payment will be processed instantly</li>
                  </ul>
                </div>
              </div>
            </div>

            <PaystackButton
              {...getMpesaPaystackConfig()}
              onSuccess={handleMpesaPaystackSuccess}
              onClose={handleMpesaPaystackClose}
              className="w-full btn-animated btn-green py-3 text-lg font-medium rounded-lg"
              disabled={isProcessing || !mpesaData.phone}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Processing M-Pesa...
                </>
              ) : (
                <>
                  <Smartphone className="w-5 h-5 mr-2" />
                  Pay KES {paymentData.amount.toLocaleString()} via M-Pesa
                </>
              )}
            </PaystackButton>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PaymentMethodTabs;
