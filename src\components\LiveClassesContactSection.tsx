import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MessageCircle, 
  Users, 
  Calendar, 
  Clock,
  Video,
  CheckCircle,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';

const LiveClassesContactSection = () => {
  // Replace with actual WhatsApp number
  const whatsappNumber = "************"; // Example Kenyan number
  const whatsappMessage = "Hi! I'm interested in joining live classes at FreeCodeLap. Can you provide more information?";
  const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(whatsappMessage)}`;

  const liveClassFeatures = [
    {
      icon: <Video className="h-6 w-6 text-blue-500" />,
      title: "Interactive Live Sessions",
      description: "Real-time coding sessions with instructor guidance"
    },
    {
      icon: <Users className="h-6 w-6 text-green-500" />,
      title: "Small Class Sizes",
      description: "Maximum 15 students per class for personalized attention"
    },
    {
      icon: <Calendar className="h-6 w-6 text-purple-500" />,
      title: "Flexible Scheduling",
      description: "Weekend and evening classes available"
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-orange-500" />,
      title: "Project-Based Learning",
      description: "Build real applications during live sessions"
    }
  ];

  const scheduleOptions = [
    {
      day: "Saturdays",
      time: "9:00 AM - 12:00 PM",
      course: "FlutterFlow Basics",
      level: "Beginner",
      color: "blue"
    },
    {
      day: "Saturdays", 
      time: "2:00 PM - 5:00 PM",
      course: "Advanced FlutterFlow",
      level: "Intermediate",
      color: "green"
    },
    {
      day: "Sundays",
      time: "10:00 AM - 1:00 PM", 
      course: "Vibe Coding with AI",
      level: "All Levels",
      color: "purple"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      green: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      purple: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-green-500 text-white">
            <MessageCircle className="mr-2 h-4 w-4" />
            Live Classes Available
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Join Our Live Classes
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get personalized guidance and learn alongside other students in our interactive live sessions. 
            Perfect for those who prefer real-time learning and immediate feedback.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Features */}
          <div>
            <h3 className="text-2xl font-bold text-white mb-6">
              Why Choose Live Classes?
            </h3>
            
            <div className="space-y-4 mb-8">
              {liveClassFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-4 p-4 bg-gray-900 rounded-lg border border-gray-700">
                  <div className="flex-shrink-0">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">{feature.title}</h4>
                    <p className="text-gray-300 text-sm">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Schedule */}
            <h4 className="text-xl font-bold text-white mb-4">Class Schedule</h4>
            <div className="space-y-3 mb-8">
              {scheduleOptions.map((schedule, index) => (
                <div key={index} className="bg-gray-900 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="font-semibold text-white">{schedule.day}</span>
                    </div>
                    <Badge className={getColorClasses(schedule.color)}>
                      {schedule.level}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300 text-sm mb-1">
                    <Clock className="h-4 w-4" />
                    <span>{schedule.time}</span>
                  </div>
                  <div className="text-white font-medium">{schedule.course}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side - Contact Card */}
          <div>
            <Card className="bg-gradient-to-br from-green-900 to-green-800 border-green-700 text-white">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="bg-green-500 rounded-full p-4">
                    <MessageCircle className="h-8 w-8 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold">
                  Ready to Join Live Classes?
                </CardTitle>
                <p className="text-green-100">
                  Chat with us on WhatsApp to get started and reserve your spot in our next live session.
                </p>
              </CardHeader>

              <CardContent className="text-center">
                <Button 
                  size="lg" 
                  className="w-full bg-green-500 hover:bg-green-600 text-white mb-6"
                  onClick={() => window.open(whatsappUrl, '_blank')}
                >
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Chat for Live Classes
                </Button>

                <div className="space-y-3 text-sm text-green-100">
                  <div className="flex items-center justify-center gap-2">
                    <Phone className="h-4 w-4" />
                    <span>+254 700 000 000</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Mail className="h-4 w-4" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>Nairobi, Kenya</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-green-800 rounded-lg">
                  <h5 className="font-semibold mb-2">What to Expect:</h5>
                  <ul className="text-sm text-green-100 space-y-1">
                    <li>• Immediate response during business hours</li>
                    <li>• Class schedule and pricing information</li>
                    <li>• Help choosing the right course level</li>
                    <li>• Payment options and enrollment process</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Additional Contact Options */}
            <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Card className="bg-gray-900 border-gray-700">
                <CardContent className="p-4 text-center">
                  <Video className="h-6 w-6 text-blue-500 mx-auto mb-2" />
                  <h5 className="font-semibold text-white mb-1">Free Demo</h5>
                  <p className="text-gray-300 text-xs">Join a free demo session</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900 border-gray-700">
                <CardContent className="p-4 text-center">
                  <Calendar className="h-6 w-6 text-purple-500 mx-auto mb-2" />
                  <h5 className="font-semibold text-white mb-1">Schedule Call</h5>
                  <p className="text-gray-300 text-xs">Book a consultation call</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-700">
            <h3 className="text-2xl font-bold text-white mb-4">
              Can't Join Live Classes?
            </h3>
            <p className="text-gray-300 mb-6">
              No problem! Our self-paced recorded courses offer the same comprehensive content 
              with lifetime access and the flexibility to learn on your schedule.
            </p>
            <Button asChild size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
              <a href="#courses-section">
                Explore Recorded Courses
              </a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LiveClassesContactSection;
