/**
 * FreeCodeLap Platform - Email Notification Service
 * 
 * Service for sending no-reply emails to users with templates and filtering
 */

import { UserProfile, UserRole } from '@/types/user';
import { userManagementService } from './userManagementService';
import { realEmailService } from './realEmailService';
import { simpleEmailService } from './simpleEmailService';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[]; // Available template variables like {{name}}, {{email}}
}

export interface EmailRecipientFilter {
  status?: 'active' | 'inactive' | 'suspended' | 'all';
  role?: UserRole | 'all';
  enrollmentStatus?: 'enrolled' | 'not_enrolled' | 'all';
  customUserIds?: string[];
}

export interface EmailNotification {
  id: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  recipients: EmailRecipientFilter;
  scheduledAt?: Date;
  sentAt?: Date;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed';
  sentCount: number;
  failedCount: number;
  createdBy: string;
  createdAt: Date;
}

export interface EmailSendResult {
  success: boolean;
  sentCount: number;
  failedCount: number;
  errors: string[];
}

export class EmailNotificationService {
  private static instance: EmailNotificationService;

  public static getInstance(): EmailNotificationService {
    if (!EmailNotificationService.instance) {
      EmailNotificationService.instance = new EmailNotificationService();
    }
    return EmailNotificationService.instance;
  }

  /**
   * Get predefined email templates
   */
  getEmailTemplates(): EmailTemplate[] {
    return [
      {
        id: 'welcome',
        name: 'Welcome Email',
        subject: 'Welcome to FreeCodeLap - Start Your Learning Journey!',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
            <div style="background-color: #1e293b; padding: 30px; border-radius: 10px; text-align: center;">
              <h1 style="color: #3b82f6; margin: 0 0 20px 0;">Welcome to FreeCodeLap!</h1>
              <p style="color: #e2e8f0; font-size: 18px; margin: 0;">Hi {{name}},</p>
            </div>
            
            <div style="background-color: white; padding: 30px; border-radius: 10px; margin-top: 20px;">
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Welcome to FreeCodeLap! We're excited to have you join our community of learners.
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Your account has been successfully created with the email: <strong>{{email}}</strong>
              </p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{platformUrl}}" style="background-color: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                  Start Learning Now
                </a>
              </div>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                If you have any questions, feel free to reach out to our support team.
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Happy learning!<br>
                The FreeCodeLap Team
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px;">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        `,
        textContent: `
Welcome to FreeCodeLap!

Hi {{name}},

Welcome to FreeCodeLap! We're excited to have you join our community of learners.

Your account has been successfully created with the email: {{email}}

Visit our platform to start learning: {{platformUrl}}

If you have any questions, feel free to reach out to our support team.

Happy learning!
The FreeCodeLap Team

This is an automated message. Please do not reply to this email.
        `,
        variables: ['name', 'email', 'platformUrl']
      },
      {
        id: 'account_suspended',
        name: 'Account Suspended',
        subject: 'Important: Your FreeCodeLap Account Has Been Suspended',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
            <div style="background-color: #dc2626; padding: 30px; border-radius: 10px; text-align: center;">
              <h1 style="color: white; margin: 0 0 20px 0;">Account Suspended</h1>
              <p style="color: #fecaca; font-size: 18px; margin: 0;">Hi {{name}},</p>
            </div>
            
            <div style="background-color: white; padding: 30px; border-radius: 10px; margin-top: 20px;">
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                We're writing to inform you that your FreeCodeLap account ({{email}}) has been temporarily suspended.
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                <strong>Reason:</strong> {{reason}}
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                During this suspension period, you will not be able to access your courses or account features.
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                If you believe this suspension was made in error or would like to appeal this decision, please contact our support team immediately.
              </p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="mailto:<EMAIL>" style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                  Contact Support
                </a>
              </div>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Best regards,<br>
                The FreeCodeLap Team
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px;">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        `,
        textContent: `
Account Suspended

Hi {{name}},

We're writing to inform you that your FreeCodeLap account ({{email}}) has been temporarily suspended.

Reason: {{reason}}

During this suspension period, you will not be able to access your courses or account features.

If you believe this suspension was made in error or would like to appeal this decision, please contact our support <NAME_EMAIL>.

Best regards,
The FreeCodeLap Team

This is an automated message. Please do not reply to this email.
        `,
        variables: ['name', 'email', 'reason']
      },
      {
        id: 'account_activated',
        name: 'Account Activated',
        subject: 'Great News! Your FreeCodeLap Account Has Been Activated',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
            <div style="background-color: #059669; padding: 30px; border-radius: 10px; text-align: center;">
              <h1 style="color: white; margin: 0 0 20px 0;">Account Activated!</h1>
              <p style="color: #a7f3d0; font-size: 18px; margin: 0;">Hi {{name}},</p>
            </div>
            
            <div style="background-color: white; padding: 30px; border-radius: 10px; margin-top: 20px;">
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Great news! Your FreeCodeLap account ({{email}}) has been successfully activated.
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                You now have full access to all your courses and platform features. You can continue your learning journey where you left off.
              </p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{platformUrl}}" style="background-color: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                  Access Your Courses
                </a>
              </div>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Thank you for your patience, and welcome back to FreeCodeLap!
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Happy learning!<br>
                The FreeCodeLap Team
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px;">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        `,
        textContent: `
Account Activated!

Hi {{name}},

Great news! Your FreeCodeLap account ({{email}}) has been successfully activated.

You now have full access to all your courses and platform features. You can continue your learning journey where you left off.

Visit our platform: {{platformUrl}}

Thank you for your patience, and welcome back to FreeCodeLap!

Happy learning!
The FreeCodeLap Team

This is an automated message. Please do not reply to this email.
        `,
        variables: ['name', 'email', 'platformUrl']
      },
      {
        id: 'course_announcement',
        name: 'Course Announcement',
        subject: 'New Course Available: {{courseTitle}}',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
            <div style="background-color: #7c3aed; padding: 30px; border-radius: 10px; text-align: center;">
              <h1 style="color: white; margin: 0 0 20px 0;">New Course Available!</h1>
              <p style="color: #c4b5fd; font-size: 18px; margin: 0;">Hi {{name}},</p>
            </div>
            
            <div style="background-color: white; padding: 30px; border-radius: 10px; margin-top: 20px;">
              <h2 style="color: #7c3aed; margin: 0 0 20px 0;">{{courseTitle}}</h2>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                We're excited to announce a new course that's now available on FreeCodeLap!
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                {{courseDescription}}
              </p>
              
              <div style="background-color: #f3f4f6; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <p style="color: #374151; margin: 0; font-weight: bold;">Course Details:</p>
                <p style="color: #374151; margin: 5px 0;">Duration: {{courseDuration}}</p>
                <p style="color: #374151; margin: 5px 0;">Level: {{courseLevel}}</p>
                <p style="color: #374151; margin: 5px 0;">Price: {{coursePrice}}</p>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{courseUrl}}" style="background-color: #7c3aed; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                  Enroll Now
                </a>
              </div>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Don't miss out on this opportunity to expand your skills!
              </p>
              
              <p style="color: #374151; font-size: 16px; line-height: 1.6;">
                Best regards,<br>
                The FreeCodeLap Team
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px;">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        `,
        textContent: `
New Course Available!

Hi {{name}},

We're excited to announce a new course that's now available on FreeCodeLap!

{{courseTitle}}

{{courseDescription}}

Course Details:
- Duration: {{courseDuration}}
- Level: {{courseLevel}}
- Price: {{coursePrice}}

Enroll now: {{courseUrl}}

Don't miss out on this opportunity to expand your skills!

Best regards,
The FreeCodeLap Team

This is an automated message. Please do not reply to this email.
        `,
        variables: ['name', 'courseTitle', 'courseDescription', 'courseDuration', 'courseLevel', 'coursePrice', 'courseUrl']
      }
    ];
  }

  /**
   * Get users based on filter criteria
   */
  async getFilteredUsers(filter: EmailRecipientFilter): Promise<UserProfile[]> {
    try {
      console.log('🔍 Filtering users for email:', filter);

      // If custom user IDs are provided, get those specific users
      if (filter.customUserIds && filter.customUserIds.length > 0) {
        console.log('📧 Using custom user IDs:', filter.customUserIds);
        const users: UserProfile[] = [];
        for (const userId of filter.customUserIds) {
          const user = await userManagementService.getUserById(userId);
          if (user) {
            console.log('✅ Found user:', user.email);
            users.push(user);
          }
        }
        console.log(`📧 Custom users found: ${users.length}`);
        return users;
      }

      // Get all users first
      console.log('📧 Getting all users from database...');
      const { users: allUsers } = await userManagementService.getUsers({}, 1000); // Get up to 1000 users
      console.log(`📧 Total users in database: ${allUsers.length}`);

      let filteredUsers = allUsers;

      // Apply role filter
      if (filter.role && filter.role !== 'all') {
        console.log(`📧 Filtering by role: ${filter.role}`);
        filteredUsers = filteredUsers.filter(user => user.role === filter.role);
        console.log(`📧 Users after role filter: ${filteredUsers.length}`);
      }

      // Apply status filter
      if (filter.status && filter.status !== 'all') {
        console.log(`📧 Filtering by status: ${filter.status}`);
        filteredUsers = filteredUsers.filter(user => {
          const userStatus = (user as any).status || 'active';
          return userStatus === filter.status;
        });
        console.log(`📧 Users after status filter: ${filteredUsers.length}`);
      }

      // Apply enrollment filter
      if (filter.enrollmentStatus && filter.enrollmentStatus !== 'all') {
        console.log(`📧 Filtering by enrollment: ${filter.enrollmentStatus}`);
        filteredUsers = filteredUsers.filter(user => {
          const hasEnrollments = user.enrolledCourses && user.enrolledCourses.length > 0;
          return filter.enrollmentStatus === 'enrolled' ? hasEnrollments : !hasEnrollments;
        });
        console.log(`📧 Users after enrollment filter: ${filteredUsers.length}`);
      }

      console.log(`📧 Final filtered users: ${filteredUsers.length}`);
      return filteredUsers;
    } catch (error) {
      console.error('❌ Error filtering users:', error);
      throw error;
    }
  }

  /**
   * Replace template variables in content
   */
  private replaceTemplateVariables(content: string, user: UserProfile, customVariables: Record<string, string> = {}): string {
    let processedContent = content;

    // Default user variables
    const variables = {
      name: user.displayName || user.firstName || 'User',
      email: user.email,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      role: user.role,
      platformUrl: window.location.origin,
      ...customVariables
    };

    // Replace all variables
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedContent = processedContent.replace(regex, String(value));
    });

    return processedContent;
  }

  /**
   * Send email to a single user (simulated - in production would use actual email service)
   */
  private async sendEmailToUser(
    user: UserProfile,
    subject: string,
    htmlContent: string,
    textContent: string,
    customVariables: Record<string, string> = {}
  ): Promise<boolean> {
    try {
      // Replace template variables
      const processedSubject = this.replaceTemplateVariables(subject, user, customVariables);
      const processedHtmlContent = this.replaceTemplateVariables(htmlContent, user, customVariables);
      const processedTextContent = this.replaceTemplateVariables(textContent, user, customVariables);

      // In production, this would integrate with an actual email service like:
      // - SendGrid
      // - AWS SES
      // - Mailgun
      // - Nodemailer with SMTP

      console.log(`📧 Sending email to: ${user.email}`);
      console.log(`📧 Subject: ${processedSubject}`);
      console.log(`📧 Content preview: ${processedTextContent.substring(0, 100)}...`);

      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 100));

      // Simulate 95% success rate
      const success = Math.random() > 0.05;

      if (success) {
        console.log(`✅ Email sent successfully to: ${user.email}`);
      } else {
        console.log(`❌ Failed to send email to: ${user.email}`);
      }

      return success;
    } catch (error) {
      console.error(`❌ Error sending email to ${user.email}:`, error);
      return false;
    }
  }

  /**
   * Send bulk emails to filtered users using real email service
   */
  async sendBulkEmail(
    subject: string,
    htmlContent: string,
    textContent: string,
    recipients: EmailRecipientFilter,
    customVariables: Record<string, string> = {},
    adminId: string
  ): Promise<EmailSendResult> {
    try {
      console.log('📧 Starting bulk email send...');

      // Get filtered users
      const users = await this.getFilteredUsers(recipients);
      console.log(`📧 Found ${users.length} users matching criteria`);

      if (users.length === 0) {
        return {
          success: true,
          sentCount: 0,
          failedCount: 0,
          errors: ['No users found matching the specified criteria']
        };
      }

      // Get admin info for tracking
      const adminUser = await userManagementService.getUserById(adminId);
      const adminName = adminUser?.displayName || 'Admin';

      // Process template variables in content
      const processedSubject = this.replaceTemplateVariables(subject, users[0] || {} as UserProfile, customVariables);
      const processedContent = this.replaceTemplateVariables(textContent, users[0] || {} as UserProfile, customVariables);

      console.log('📧 Processed subject:', processedSubject);
      console.log('📧 Processed content preview:', processedContent.substring(0, 100));

      // Use simple email service that opens email client
      const result = await simpleEmailService.sendEmailsViaClient(
        users,
        processedSubject,
        processedContent,
        adminId,
        adminName
      );

      console.log(`📧 Bulk email completed: ${result.sentCount} sent, ${result.failedCount} failed`);

      return {
        success: result.success,
        sentCount: result.sentCount,
        failedCount: result.failedCount,
        errors: result.errors
      };
    } catch (error) {
      console.error('❌ Error in bulk email send:', error);
      return {
        success: false,
        sentCount: 0,
        failedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Send email using a predefined template
   */
  async sendTemplateEmail(
    templateId: string,
    recipients: EmailRecipientFilter,
    customVariables: Record<string, string> = {},
    adminId: string
  ): Promise<EmailSendResult> {
    try {
      const templates = this.getEmailTemplates();
      const template = templates.find(t => t.id === templateId);

      if (!template) {
        throw new Error(`Template with ID '${templateId}' not found`);
      }

      return await this.sendBulkEmail(
        template.subject,
        template.htmlContent,
        template.textContent,
        recipients,
        customVariables,
        adminId
      );
    } catch (error) {
      console.error('❌ Error sending template email:', error);
      throw error;
    }
  }

  /**
   * Get email statistics
   */
  async getEmailStatistics(): Promise<{
    totalSent: number;
    totalFailed: number;
    recentEmails: Array<{
      subject: string;
      sentAt: Date;
      sentCount: number;
      failedCount: number;
    }>;
  }> {
    // In production, this would query the email logs from database
    return {
      totalSent: 1250,
      totalFailed: 45,
      recentEmails: [
        {
          subject: 'Welcome to FreeCodeLap',
          sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          sentCount: 15,
          failedCount: 1
        },
        {
          subject: 'New Course Available: Advanced React',
          sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          sentCount: 234,
          failedCount: 8
        }
      ]
    };
  }

  /**
   * Log email activity for audit purposes
   */
  private async logEmailActivity(activity: {
    subject: string;
    recipients: EmailRecipientFilter;
    sentCount: number;
    failedCount: number;
    adminId: string;
  }): Promise<void> {
    try {
      // In production, this would save to Firestore or another database
      console.log('📝 Logging email activity:', {
        ...activity,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Error logging email activity:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Validate email content
   */
  validateEmailContent(subject: string, content: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!subject || subject.trim().length === 0) {
      errors.push('Subject is required');
    }

    if (subject && subject.length > 200) {
      errors.push('Subject must be less than 200 characters');
    }

    if (!content || content.trim().length === 0) {
      errors.push('Email content is required');
    }

    if (content && content.length > 50000) {
      errors.push('Email content is too long (max 50,000 characters)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Preview email with sample user data
   */
  previewEmail(
    subject: string,
    htmlContent: string,
    textContent: string,
    customVariables: Record<string, string> = {}
  ): { subject: string; htmlContent: string; textContent: string } {
    // Sample user for preview
    const sampleUser: UserProfile = {
      uid: 'sample-user-id',
      email: '<EMAIL>',
      displayName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      role: 'student',
      createdAt: new Date(),
      updatedAt: new Date(),
      enrolledCourses: ['course-1', 'course-2'],
      completedCourses: ['course-1'],
      certificates: [],
      paymentHistory: [],
      preferences: {
        emailNotifications: true,
        courseUpdates: true,
        marketingEmails: false,
        language: 'en',
        timezone: 'Africa/Nairobi'
      }
    };

    return {
      subject: this.replaceTemplateVariables(subject, sampleUser, customVariables),
      htmlContent: this.replaceTemplateVariables(htmlContent, sampleUser, customVariables),
      textContent: this.replaceTemplateVariables(textContent, sampleUser, customVariables)
    };
  }
}

export const emailNotificationService = EmailNotificationService.getInstance();
