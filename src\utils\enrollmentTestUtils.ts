/**
 * Utility functions for testing enrollment management features
 * These functions help verify the enrollment system is working correctly
 */

import { enrollmentManagementService } from '@/services/enrollmentManagementService';

export const testEnrollmentFeatures = async () => {
  console.log('🧪 Testing Enrollment Management Features...');
  
  try {
    // Test 1: Get all enrollments
    console.log('📚 Test 1: Getting all enrollments...');
    const allEnrollments = await enrollmentManagementService.getAllEnrollments(10);
    console.log(`✅ Found ${allEnrollments.length} enrollments`);
    
    if (allEnrollments.length > 0) {
      const firstEnrollment = allEnrollments[0];
      console.log('📋 Sample enrollment:', {
        user: firstEnrollment.userName,
        email: firstEnrollment.userEmail,
        course: firstEnrollment.courseTitle,
        enrolledAt: firstEnrollment.enrolledAt,
        isPaid: !!firstEnrollment.paymentInfo
      });
    }

    // Test 2: Get available courses
    console.log('📚 Test 2: Getting available courses...');
    const courses = await enrollmentManagementService.getAvailableCourses();
    console.log(`✅ Found ${courses.length} available courses`);
    
    if (courses.length > 0) {
      console.log('📋 Sample courses:', courses.slice(0, 3).map(c => ({
        title: c.title,
        price: c.price,
        currency: c.currency
      })));
    }

    // Test 3: Search users (if any exist)
    console.log('👥 Test 3: Testing user search...');
    const searchResults = await enrollmentManagementService.searchUsers('test');
    console.log(`✅ User search returned ${searchResults.length} results`);

    console.log('🎉 All enrollment management tests completed successfully!');
    
    return {
      enrollmentsCount: allEnrollments.length,
      coursesCount: courses.length,
      searchResultsCount: searchResults.length,
      success: true
    };
    
  } catch (error) {
    console.error('❌ Enrollment management test failed:', error);
    return {
      error: error.message,
      success: false
    };
  }
};

export const logEnrollmentSummary = async () => {
  try {
    const enrollments = await enrollmentManagementService.getAllEnrollments(100);
    
    const summary = {
      total: enrollments.length,
      paid: enrollments.filter(e => e.paymentInfo).length,
      manual: enrollments.filter(e => !e.paymentInfo).length,
      withCertificates: enrollments.filter(e => e.certificateEarned).length,
      uniqueUsers: new Set(enrollments.map(e => e.userId)).size,
      uniqueCourses: new Set(enrollments.map(e => e.courseId)).size
    };
    
    console.log('📊 Enrollment Summary:', summary);
    return summary;
  } catch (error) {
    console.error('❌ Failed to generate enrollment summary:', error);
    return null;
  }
};

// Helper function to format enrollment data for display
export const formatEnrollmentForDisplay = (enrollment: any) => {
  return {
    id: enrollment.id,
    user: `${enrollment.userName} (${enrollment.userEmail})`,
    course: enrollment.courseTitle,
    enrolled: new Date(enrollment.enrolledAt).toLocaleDateString(),
    progress: `${enrollment.progress}%`,
    type: enrollment.paymentInfo ? 'Paid' : 'Manual',
    amount: enrollment.paymentInfo ? 
      `${enrollment.paymentInfo.currency} ${enrollment.paymentInfo.amount}` : 
      'Free/Manual'
  };
};

// Helper function to validate enrollment data
export const validateEnrollmentData = (enrollment: any) => {
  const errors = [];
  
  if (!enrollment.userId) errors.push('Missing userId');
  if (!enrollment.courseId) errors.push('Missing courseId');
  if (!enrollment.userEmail) errors.push('Missing userEmail');
  if (!enrollment.userName) errors.push('Missing userName');
  if (!enrollment.courseTitle) errors.push('Missing courseTitle');
  if (!enrollment.enrolledAt) errors.push('Missing enrolledAt');
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
