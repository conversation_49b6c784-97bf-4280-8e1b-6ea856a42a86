/**
 * Enrollment Management Service for FreeCodeLap Admin Panel
 * Provides comprehensive enrollment management functionality
 */

import { db } from '@/lib/firebase';
import {
  collection,
  query,
  getDocs,
  doc,
  getDoc,
  setDoc,
  deleteDoc,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp
} from 'firebase/firestore';
import { enrollmentService } from './enrollmentService';
import { courseService } from './courseService';
import { emailService } from './emailService';
import { Enrollment, Course } from '@/types/schema';

export interface EnrollmentWithUserDetails {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  courseId: string;
  courseTitle: string;
  enrolledAt: Timestamp;
  status: string;
  progress: number;
  certificateEarned: boolean;
  paymentInfo?: {
    amount: number;
    currency: string;
    paymentMethod: string;
    transactionId: string;
  };
}

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  role: string;
  createdAt: Timestamp;
}

class EnrollmentManagementService {
  /**
   * Get all enrollments with user and course details
   */
  async getAllEnrollments(limit: number = 100): Promise<EnrollmentWithUserDetails[]> {
    try {
      console.log('🔍 Fetching all enrollments...');
      
      const enrollmentsRef = collection(db, 'enrollments');
      const q = query(
        enrollmentsRef,
        orderBy('enrolledAt', 'desc'),
        firestoreLimit(limit)
      );
      
      const snapshot = await getDocs(q);
      const enrollments: EnrollmentWithUserDetails[] = [];
      
      for (const enrollmentDoc of snapshot.docs) {
        const enrollmentData = enrollmentDoc.data() as Enrollment;
        
        try {
          // Get user details
          const userDoc = await getDoc(doc(db, 'users', enrollmentData.userId));
          const userData = userDoc.exists() ? userDoc.data() : null;
          
          // Get course details
          const courseDoc = await getDoc(doc(db, 'courses', enrollmentData.courseId));
          const courseData = courseDoc.exists() ? courseDoc.data() : null;
          
          // Calculate progress
          const progress = enrollmentData.progress?.progressPercentage || 0;
          
          // Check for payment info
          let paymentInfo = undefined;
          if (enrollmentData.paymentId) {
            try {
              const paymentDoc = await getDoc(doc(db, 'payments', enrollmentData.paymentId));
              if (paymentDoc.exists()) {
                const payment = paymentDoc.data();
                paymentInfo = {
                  amount: payment.amount || 0,
                  currency: payment.currency || 'USD',
                  paymentMethod: payment.paymentMethod || 'Unknown',
                  transactionId: payment.transactionId || payment.reference || 'N/A'
                };
              }
            } catch (paymentError) {
              console.warn('Could not fetch payment info:', paymentError);
            }
          }
          
          const enrichedEnrollment: EnrollmentWithUserDetails = {
            id: enrollmentDoc.id,
            userId: enrollmentData.userId,
            userName: userData?.displayName || userData?.email?.split('@')[0] || 'Unknown User',
            userEmail: userData?.email || 'No email',
            courseId: enrollmentData.courseId,
            courseTitle: courseData?.title || 'Unknown Course',
            enrolledAt: enrollmentData.enrolledAt,
            status: enrollmentData.status || 'active',
            progress: progress,
            certificateEarned: enrollmentData.certificateIssued || false,
            paymentInfo
          };
          
          enrollments.push(enrichedEnrollment);
        } catch (detailError) {
          console.warn('Error fetching details for enrollment:', enrollmentDoc.id, detailError);
          
          // Add enrollment with minimal data if details fetch fails
          const basicEnrollment: EnrollmentWithUserDetails = {
            id: enrollmentDoc.id,
            userId: enrollmentData.userId,
            userName: 'Unknown User',
            userEmail: 'Unknown Email',
            courseId: enrollmentData.courseId,
            courseTitle: 'Unknown Course',
            enrolledAt: enrollmentData.enrolledAt,
            status: enrollmentData.status || 'active',
            progress: 0,
            certificateEarned: false
          };
          
          enrollments.push(basicEnrollment);
        }
      }
      
      console.log(`✅ Successfully fetched ${enrollments.length} enrollments`);
      console.log('📋 Enrollment details:', enrollments.map(e => ({
        id: e.id,
        userEmail: e.userEmail,
        courseTitle: e.courseTitle,
        status: e.status,
        enrolledAt: e.enrolledAt
      })));
      return enrollments;
    } catch (error) {
      console.error('❌ Error fetching enrollments:', error);
      throw new Error('Failed to fetch enrollments');
    }
  }

  /**
   * Get available courses for enrollment
   */
  async getAvailableCourses(): Promise<Course[]> {
    try {
      return await courseService.getAllCourses();
    } catch (error) {
      console.error('❌ Error fetching available courses:', error);
      throw new Error('Failed to fetch available courses');
    }
  }

  /**
   * Manually enroll a user with details
   */
  async manuallyEnrollUserWithDetails(
    userEmail: string,
    userName: string,
    courseId: string,
    adminNote: string
  ): Promise<string> {
    try {
      console.log('🎯 Manually enrolling user:', { userEmail, userName, courseId });
      
      // Check if user exists, if not create a basic user record
      let userId = await this.findOrCreateUser(userEmail, userName);
      console.log('👤 User ID resolved:', userId);

      // Create the enrollment
      const enrollmentId = await enrollmentService.createManualEnrollment({
        userId,
        courseId,
        enrollmentType: 'manual',
        adminNote,
        enrolledBy: 'admin' // You might want to pass the actual admin ID
      });

      console.log('✅ Manual enrollment created successfully:', enrollmentId);

      // Verify the enrollment was created
      const verification = await enrollmentService.isUserEnrolled(userId, courseId);
      console.log('🔍 Enrollment verification:', verification);
      return enrollmentId;
    } catch (error) {
      console.error('❌ Error creating manual enrollment:', error);
      throw error;
    }
  }

  /**
   * Send enrollment notification email
   */
  async sendEnrollmentNotification(
    userEmail: string,
    userName: string,
    courseTitle: string,
    adminNote: string
  ): Promise<void> {
    try {
      await emailService.sendEnrollmentNotification(
        userEmail,
        userName,
        courseTitle,
        adminNote
      );
      console.log('✅ Enrollment notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending enrollment notification:', error);
      // Don't throw here - enrollment should succeed even if email fails
    }
  }

  /**
   * Remove an enrollment
   */
  async removeEnrollment(enrollmentId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'enrollments', enrollmentId));
      console.log('✅ Enrollment removed successfully:', enrollmentId);
    } catch (error) {
      console.error('❌ Error removing enrollment:', error);
      throw new Error('Failed to remove enrollment');
    }
  }

  /**
   * Find or create a user by email
   */
  private async findOrCreateUser(email: string, displayName: string): Promise<string> {
    try {
      // First, try to find existing user by email
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', email));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        return snapshot.docs[0].id;
      }
      
      // If user doesn't exist, create a basic user record
      // Note: This creates a Firestore user record, not a Firebase Auth user
      const userDoc = doc(collection(db, 'users'));
      await setDoc(userDoc, {
        email,
        displayName: displayName || email.split('@')[0],
        role: 'student',
        createdAt: Timestamp.now(),
        createdBy: 'admin_enrollment'
      });

      return userDoc.id;
    } catch (error) {
      console.error('❌ Error finding/creating user:', error);
      throw new Error('Failed to find or create user');
    }
  }

  /**
   * Get all users (for admin purposes)
   */
  async getAllUsers(): Promise<UserProfile[]> {
    try {
      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);
      
      return snapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      } as UserProfile));
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  /**
   * Search users by email or name
   */
  async searchUsers(searchTerm: string): Promise<UserProfile[]> {
    try {
      const users = await this.getAllUsers();
      const term = searchTerm.toLowerCase();
      
      return users.filter(user => 
        user.email.toLowerCase().includes(term) ||
        user.displayName.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('❌ Error searching users:', error);
      throw new Error('Failed to search users');
    }
  }
}

// Export singleton instance
export const enrollmentManagementService = new EnrollmentManagementService();
