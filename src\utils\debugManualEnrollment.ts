import { collection, query, where, getDocs, getDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export const debugManualEnrollment = async (userEmail: string, courseId: string) => {
  console.log('🔍 DEBUG: Manual Enrollment Check');
  console.log('==================================');
  console.log('User Email:', userEmail);
  console.log('Course ID:', courseId);
  console.log('');

  try {
    const enrollmentsRef = collection(db, 'enrollments');

    // 1. Check by email as userId (how manual enrollment stores it)
    console.log('📋 Step 1: Checking by email as userId...');
    const emailAsUserIdQuery = query(
      enrollmentsRef,
      where('userId', '==', userEmail),
      where('courseId', '==', courseId)
    );
    const emailAsUserIdSnapshot = await getDocs(emailAsUserIdQuery);
    console.log('   Results:', emailAsUserIdSnapshot.docs.length, 'documents found');
    
    if (!emailAsUserIdSnapshot.empty) {
      const doc = emailAsUserIdSnapshot.docs[0];
      const data = doc.data();
      console.log('   ✅ FOUND! Document data:', {
        id: doc.id,
        userId: data.userId,
        courseId: data.courseId,
        manualEnrollment: data.manualEnrollment,
        originalEmail: data.originalEmail,
        enrollmentMethod: data.enrollmentMethod,
        status: data.status
      });
    } else {
      console.log('   ❌ No enrollment found by email as userId');
    }

    // 2. Check by originalEmail field
    console.log('');
    console.log('📋 Step 2: Checking by originalEmail field...');
    const originalEmailQuery = query(
      enrollmentsRef,
      where('originalEmail', '==', userEmail),
      where('courseId', '==', courseId)
    );
    const originalEmailSnapshot = await getDocs(originalEmailQuery);
    console.log('   Results:', originalEmailSnapshot.docs.length, 'documents found');
    
    if (!originalEmailSnapshot.empty) {
      const doc = originalEmailSnapshot.docs[0];
      const data = doc.data();
      console.log('   ✅ FOUND! Document data:', {
        id: doc.id,
        userId: data.userId,
        courseId: data.courseId,
        manualEnrollment: data.manualEnrollment,
        originalEmail: data.originalEmail,
        enrollmentMethod: data.enrollmentMethod,
        status: data.status
      });
    } else {
      console.log('   ❌ No enrollment found by originalEmail');
    }

    // 3. Find user document to get actual userId
    console.log('');
    console.log('📋 Step 3: Finding user document...');
    const usersRef = collection(db, 'users');
    const userQuery = query(usersRef, where('email', '==', userEmail));
    const userSnapshot = await getDocs(userQuery);
    
    if (!userSnapshot.empty) {
      const userDoc = userSnapshot.docs[0];
      const userData = userDoc.data();
      const actualUserId = userDoc.id;
      
      console.log('   ✅ User found:', {
        id: actualUserId,
        email: userData.email,
        displayName: userData.displayName
      });

      // 4. Check by actual userId
      console.log('');
      console.log('📋 Step 4: Checking by actual userId...');
      const actualUserIdQuery = query(
        enrollmentsRef,
        where('userId', '==', actualUserId),
        where('courseId', '==', courseId)
      );
      const actualUserIdSnapshot = await getDocs(actualUserIdQuery);
      console.log('   Results:', actualUserIdSnapshot.docs.length, 'documents found');
      
      if (!actualUserIdSnapshot.empty) {
        const doc = actualUserIdSnapshot.docs[0];
        const data = doc.data();
        console.log('   ✅ FOUND! Document data:', {
          id: doc.id,
          userId: data.userId,
          courseId: data.courseId,
          manualEnrollment: data.manualEnrollment,
          originalEmail: data.originalEmail,
          enrollmentMethod: data.enrollmentMethod,
          status: data.status
        });
      } else {
        console.log('   ❌ No enrollment found by actual userId');
      }
    } else {
      console.log('   ❌ User document not found');
    }

    // 5. List ALL enrollments for this course to see what's there
    console.log('');
    console.log('📋 Step 5: All enrollments for this course...');
    const allEnrollmentsQuery = query(
      enrollmentsRef,
      where('courseId', '==', courseId)
    );
    const allEnrollmentsSnapshot = await getDocs(allEnrollmentsQuery);
    console.log('   Total enrollments for course:', allEnrollmentsSnapshot.docs.length);
    
    allEnrollmentsSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`   Enrollment ${index + 1}:`, {
        id: doc.id,
        userId: data.userId,
        originalEmail: data.originalEmail,
        manualEnrollment: data.manualEnrollment,
        enrollmentMethod: data.enrollmentMethod,
        status: data.status
      });
    });

  } catch (error) {
    console.error('❌ Debug error:', error);
  }

  console.log('');
  console.log('==================================');
};

// Helper function to test enrollment from browser console
(window as any).debugManualEnrollment = debugManualEnrollment;

// Quick test function
export const testEnrollmentAccess = async () => {
  console.log('🧪 Testing Enrollment Access');
  console.log('============================');

  // Test with common scenarios
  const testCases = [
    { email: '<EMAIL>', courseId: 'test-course-1' },
    { email: '<EMAIL>', courseId: 'test-course-2' }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.email} -> ${testCase.courseId}`);
    await debugManualEnrollment(testCase.email, testCase.courseId);
  }
};

(window as any).testEnrollmentAccess = testEnrollmentAccess;
