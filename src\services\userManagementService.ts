/**
 * FreeCodeLap Platform - User Management Service
 * 
 * Production-ready service for comprehensive user management operations
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  writeBatch,
  Timestamp,
  DocumentSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword, 
  updateProfile, 
  sendEmailVerification,
  deleteUser as deleteAuthUser,
  User as FirebaseUser
} from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { UserProfile, UserRole } from '@/types/user';
import { RoleService } from './roleService';

export interface UserSearchFilters {
  role?: UserRole;
  status?: 'active' | 'inactive' | 'suspended';
  searchTerm?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  enrollmentStatus?: 'enrolled' | 'not_enrolled';
  lastLoginRange?: {
    start: Date;
    end: Date;
  };
}

export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  suspendedUsers: number;
  adminUsers: number;
  studentUsers: number;
  newUsersThisMonth: number;
  newUsersThisWeek: number;
  usersWithEnrollments: number;
  usersWithoutEnrollments: number;
  averageCoursesPerUser: number;
  topCountries: Array<{ country: string; count: number }>;
  userGrowthData: Array<{ date: string; count: number }>;
}

export interface BulkUserOperation {
  userIds: string[];
  operation: 'delete' | 'suspend' | 'activate' | 'changeRole' | 'sendNotification';
  data?: any;
}

export interface UserActivityLog {
  userId: string;
  action: string;
  timestamp: Timestamp;
  details?: any;
  adminId: string;
}

export class UserManagementService {
  private static instance: UserManagementService;

  public static getInstance(): UserManagementService {
    if (!UserManagementService.instance) {
      UserManagementService.instance = new UserManagementService();
    }
    return UserManagementService.instance;
  }

  /**
   * Get all users with optional filtering and pagination
   */
  async getUsers(
    filters?: UserSearchFilters,
    pageSize: number = 50,
    lastDoc?: DocumentSnapshot
  ): Promise<{ users: UserProfile[]; lastDoc?: DocumentSnapshot; hasMore: boolean }> {
    try {
      console.log('🔍 Fetching users with filters:', filters);

      const constraints: QueryConstraint[] = [];

      // Add order by first (required for pagination)
      constraints.push(orderBy('createdAt', 'desc'));

      // Add filters
      if (filters?.role) {
        constraints.push(where('role', '==', filters.role));
      }

      if (filters?.status) {
        constraints.push(where('status', '==', filters.status));
      }

      if (filters?.dateRange) {
        constraints.push(
          where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)),
          where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end))
        );
      }

      // Add pagination
      constraints.push(limit(pageSize + 1)); // +1 to check if there are more

      if (lastDoc) {
        constraints.push(startAfter(lastDoc));
      }

      const usersRef = collection(db, 'users');
      const q = query(usersRef, ...constraints);
      const snapshot = await getDocs(q);

      const users: UserProfile[] = [];
      const docs = snapshot.docs;
      const hasMore = docs.length > pageSize;

      // Process documents (excluding the extra one if hasMore)
      const docsToProcess = hasMore ? docs.slice(0, -1) : docs;
      
      for (const docSnap of docsToProcess) {
        const data = docSnap.data();
        const user = this.mapFirestoreToUserProfile(docSnap.id, data);
        
        // Apply client-side filters for complex searches
        if (this.matchesSearchFilters(user, filters)) {
          users.push(user);
        }
      }

      const newLastDoc = hasMore ? docs[docs.length - 2] : undefined;

      console.log(`✅ Fetched ${users.length} users`);
      return { users, lastDoc: newLastDoc, hasMore };
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Search users by email or display name
   */
  async searchUsers(searchTerm: string, limit: number = 20): Promise<UserProfile[]> {
    try {
      console.log('🔍 Searching users for term:', searchTerm);

      const usersRef = collection(db, 'users');
      
      // Search by email
      const emailQuery = query(
        usersRef,
        where('email', '>=', searchTerm.toLowerCase()),
        where('email', '<=', searchTerm.toLowerCase() + '\uf8ff'),
        orderBy('email'),
        limit(limit)
      );

      // Search by display name
      const nameQuery = query(
        usersRef,
        where('displayName', '>=', searchTerm),
        where('displayName', '<=', searchTerm + '\uf8ff'),
        orderBy('displayName'),
        limit(limit)
      );

      const [emailSnapshot, nameSnapshot] = await Promise.all([
        getDocs(emailQuery),
        getDocs(nameQuery)
      ]);

      const users = new Map<string, UserProfile>();

      // Process email results
      emailSnapshot.docs.forEach(doc => {
        const user = this.mapFirestoreToUserProfile(doc.id, doc.data());
        users.set(user.uid, user);
      });

      // Process name results
      nameSnapshot.docs.forEach(doc => {
        const user = this.mapFirestoreToUserProfile(doc.id, doc.data());
        users.set(user.uid, user);
      });

      const result = Array.from(users.values());
      console.log(`✅ Found ${result.length} users matching search term`);
      return result;
    } catch (error) {
      console.error('❌ Error searching users:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<UserProfile | null> {
    try {
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);
      
      if (!userSnap.exists()) {
        return null;
      }

      return this.mapFirestoreToUserProfile(userSnap.id, userSnap.data());
    } catch (error) {
      console.error('❌ Error fetching user by ID:', error);
      throw error;
    }
  }

  /**
   * Create new user (admin only)
   */
  async createUser(userData: {
    email: string;
    password: string;
    displayName: string;
    role: UserRole;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
  }): Promise<UserProfile> {
    try {
      console.log('👤 Creating new user:', userData.email);

      // Create Firebase Auth user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      // Update Firebase Auth profile
      await updateProfile(userCredential.user, {
        displayName: userData.displayName
      });

      // Send email verification
      await sendEmailVerification(userCredential.user);

      // Create Firestore profile
      const profileData: Partial<UserProfile> = {
        uid: userCredential.user.uid,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phoneNumber: userData.phoneNumber,
        enrolledCourses: [],
        completedCourses: [],
        certificates: [],
        paymentHistory: [],
        preferences: {
          emailNotifications: true,
          courseUpdates: true,
          marketingEmails: false,
          language: 'en',
          timezone: 'Africa/Nairobi'
        }
      };

      // Save to Firestore
      const userRef = doc(db, 'users', userCredential.user.uid);
      await updateDoc(userRef, {
        ...profileData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active'
      });

      // Log activity
      await this.logUserActivity(userCredential.user.uid, 'user_created', {
        createdBy: 'admin',
        role: userData.role
      });

      console.log('✅ User created successfully');
      return this.mapFirestoreToUserProfile(userCredential.user.uid, {
        ...profileData,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active'
      });
    } catch (error) {
      console.error('❌ Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      console.log('📝 Updating user:', userId);

      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });

      // Log activity
      await this.logUserActivity(userId, 'user_updated', { updates });

      console.log('✅ User updated successfully');
    } catch (error) {
      console.error('❌ Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete user (soft delete by default)
   */
  async deleteUser(userId: string, hardDelete: boolean = false): Promise<void> {
    try {
      console.log(`🗑️ ${hardDelete ? 'Hard' : 'Soft'} deleting user:`, userId);

      if (hardDelete) {
        // Hard delete: remove from Firestore and Firebase Auth
        const userRef = doc(db, 'users', userId);
        await deleteDoc(userRef);

        // Note: Deleting from Firebase Auth requires admin SDK in production
        // This would typically be done via a Cloud Function
        console.log('⚠️ Hard delete completed for Firestore. Auth deletion requires admin SDK.');
      } else {
        // Soft delete: mark as deleted
        const userRef = doc(db, 'users', userId);
        await updateDoc(userRef, {
          status: 'deleted',
          deletedAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      // Log activity
      await this.logUserActivity(userId, hardDelete ? 'user_hard_deleted' : 'user_soft_deleted');

      console.log('✅ User deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Helper method to map Firestore data to UserProfile
   */
  private mapFirestoreToUserProfile(id: string, data: any): UserProfile {
    return {
      uid: id,
      email: data.email || '',
      displayName: data.displayName || '',
      role: data.role || 'student',
      createdAt: data.createdAt?.toDate?.() || new Date(),
      updatedAt: data.updatedAt?.toDate?.() || new Date(),
      lastLoginAt: data.lastLoginAt?.toDate?.(),
      firstName: data.firstName,
      lastName: data.lastName,
      phoneNumber: data.phoneNumber,
      profilePictureUrl: data.profilePictureUrl,
      bio: data.bio,
      enrolledCourses: data.enrolledCourses || [],
      completedCourses: data.completedCourses || [],
      certificates: data.certificates || [],
      paymentHistory: data.paymentHistory || [],
      subscriptionStatus: data.subscriptionStatus,
      preferences: data.preferences || {
        emailNotifications: true,
        courseUpdates: true,
        marketingEmails: false,
        language: 'en',
        timezone: 'Africa/Nairobi'
      }
    };
  }

  /**
   * Helper method to check if user matches search filters
   */
  private matchesSearchFilters(user: UserProfile, filters?: UserSearchFilters): boolean {
    if (!filters) return true;

    // Search term filter
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const matchesEmail = user.email.toLowerCase().includes(searchTerm);
      const matchesName = user.displayName.toLowerCase().includes(searchTerm);
      const matchesFirstName = user.firstName?.toLowerCase().includes(searchTerm);
      const matchesLastName = user.lastName?.toLowerCase().includes(searchTerm);
      
      if (!matchesEmail && !matchesName && !matchesFirstName && !matchesLastName) {
        return false;
      }
    }

    return true;
  }

  /**
   * Log user activity
   */
  private async logUserActivity(
    userId: string, 
    action: string, 
    details?: any, 
    adminId?: string
  ): Promise<void> {
    try {
      const activityRef = collection(db, 'userActivityLogs');
      await addDoc(activityRef, {
        userId,
        action,
        details,
        adminId: adminId || 'system',
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ Error logging user activity:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Suspend/Unsuspend user
   */
  async toggleUserSuspension(userId: string, suspend: boolean): Promise<void> {
    try {
      console.log(`${suspend ? '🚫' : '✅'} ${suspend ? 'Suspending' : 'Unsuspending'} user:`, userId);

      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        status: suspend ? 'suspended' : 'active',
        suspendedAt: suspend ? serverTimestamp() : null,
        updatedAt: serverTimestamp()
      });

      // Log activity
      await this.logUserActivity(userId, suspend ? 'user_suspended' : 'user_unsuspended');

      console.log(`✅ User ${suspend ? 'suspended' : 'unsuspended'} successfully`);
    } catch (error) {
      console.error(`❌ Error ${suspend ? 'suspending' : 'unsuspending'} user:`, error);
      throw error;
    }
  }

  /**
   * Perform bulk operations on users
   */
  async performBulkOperation(operation: BulkUserOperation): Promise<void> {
    try {
      console.log('🔄 Performing bulk operation:', operation.operation, 'on', operation.userIds.length, 'users');

      const batch = writeBatch(db);

      for (const userId of operation.userIds) {
        const userRef = doc(db, 'users', userId);

        switch (operation.operation) {
          case 'delete':
            batch.update(userRef, {
              status: 'deleted',
              deletedAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            });
            break;

          case 'suspend':
            batch.update(userRef, {
              status: 'suspended',
              suspendedAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            });
            break;

          case 'activate':
            batch.update(userRef, {
              status: 'active',
              suspendedAt: null,
              updatedAt: serverTimestamp()
            });
            break;

          case 'changeRole':
            if (operation.data?.role) {
              batch.update(userRef, {
                role: operation.data.role,
                updatedAt: serverTimestamp()
              });
            }
            break;
        }
      }

      await batch.commit();

      // Log bulk activity
      for (const userId of operation.userIds) {
        await this.logUserActivity(userId, `bulk_${operation.operation}`, operation.data);
      }

      console.log('✅ Bulk operation completed successfully');
    } catch (error) {
      console.error('❌ Error performing bulk operation:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<UserStatistics> {
    try {
      console.log('📊 Calculating user statistics...');

      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);

      const users = snapshot.docs.map(doc => this.mapFirestoreToUserProfile(doc.id, doc.data()));

      const now = new Date();
      const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const stats: UserStatistics = {
        totalUsers: users.length,
        activeUsers: users.filter(u => !u.lastLoginAt || u.lastLoginAt > oneWeekAgo).length,
        inactiveUsers: users.filter(u => u.lastLoginAt && u.lastLoginAt <= oneWeekAgo).length,
        suspendedUsers: users.filter(u => (u as any).status === 'suspended').length,
        adminUsers: users.filter(u => u.role === 'admin').length,
        studentUsers: users.filter(u => u.role === 'student').length,
        newUsersThisMonth: users.filter(u => u.createdAt > oneMonthAgo).length,
        newUsersThisWeek: users.filter(u => u.createdAt > oneWeekAgo).length,
        usersWithEnrollments: users.filter(u => u.enrolledCourses.length > 0).length,
        usersWithoutEnrollments: users.filter(u => u.enrolledCourses.length === 0).length,
        averageCoursesPerUser: users.reduce((sum, u) => sum + u.enrolledCourses.length, 0) / users.length || 0,
        topCountries: this.calculateTopCountries(users),
        userGrowthData: this.calculateUserGrowthData(users)
      };

      console.log('✅ User statistics calculated');
      return stats;
    } catch (error) {
      console.error('❌ Error calculating user statistics:', error);
      throw error;
    }
  }

  /**
   * Export users to CSV
   */
  async exportUsersToCSV(filters?: UserSearchFilters): Promise<string> {
    try {
      console.log('📤 Exporting users to CSV...');

      const { users } = await this.getUsers(filters, 1000); // Get up to 1000 users

      const headers = [
        'UID',
        'Email',
        'Display Name',
        'First Name',
        'Last Name',
        'Role',
        'Status',
        'Phone Number',
        'Created At',
        'Last Login',
        'Enrolled Courses',
        'Completed Courses',
        'Subscription Status'
      ];

      const csvRows = [
        headers.join(','),
        ...users.map(user => [
          user.uid,
          user.email,
          user.displayName,
          user.firstName || '',
          user.lastName || '',
          user.role,
          (user as any).status || 'active',
          user.phoneNumber || '',
          user.createdAt.toISOString(),
          user.lastLoginAt?.toISOString() || '',
          user.enrolledCourses.length,
          user.completedCourses.length,
          user.subscriptionStatus || 'inactive'
        ].map(field => `"${field}"`).join(','))
      ];

      const csvContent = csvRows.join('\n');
      console.log('✅ CSV export completed');
      return csvContent;
    } catch (error) {
      console.error('❌ Error exporting users to CSV:', error);
      throw error;
    }
  }

  /**
   * Calculate top countries from user data
   */
  private calculateTopCountries(users: UserProfile[]): Array<{ country: string; count: number }> {
    const countryCount = new Map<string, number>();

    users.forEach(user => {
      const country = (user as any).country || 'Unknown';
      countryCount.set(country, (countryCount.get(country) || 0) + 1);
    });

    return Array.from(countryCount.entries())
      .map(([country, count]) => ({ country, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Calculate user growth data for charts
   */
  private calculateUserGrowthData(users: UserProfile[]): Array<{ date: string; count: number }> {
    const growthData = new Map<string, number>();

    users.forEach(user => {
      const date = user.createdAt.toISOString().split('T')[0];
      growthData.set(date, (growthData.get(date) || 0) + 1);
    });

    return Array.from(growthData.entries())
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30); // Last 30 days
  }

  /**
   * Change user role
   */
  async changeUserRole(userId: string, newRole: UserRole): Promise<void> {
    try {
      console.log(`🔄 Changing user role: ${userId} -> ${newRole}`);

      await RoleService.updateUserRole(userId, newRole);

      // Log activity
      await this.logUserActivity(userId, 'role_changed', { newRole });

      console.log('✅ User role changed successfully');
    } catch (error) {
      console.error('❌ Error changing user role:', error);
      throw error;
    }
  }
}

export const userManagementService = UserManagementService.getInstance();
