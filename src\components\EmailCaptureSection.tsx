
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Mail, Gift, BookOpen, Bell } from 'lucide-react';

const EmailCaptureSection = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically integrate with an email service like Mailchimp, ConvertKit, etc.
    console.log('Email submitted:', email);
    setIsSubmitted(true);
    setEmail('');
  };

  const benefits = [
    {
      icon: <Gift className="w-6 h-6 text-green-600" />,
      title: "Free FlutterFlow Templates",
      description: "Get 5 premium app templates worth $200"
    },
    {
      icon: <BookOpen className="w-6 h-6 text-blue-600" />,
      title: "Weekly Tips & Tutorials",
      description: "Learn new FlutterFlow tricks every week"
    },
    {
      icon: <Bell className="w-6 h-6 text-purple-600" />,
      title: "Exclusive Course Discounts",
      description: "Get early access to new courses at 50% off"
    }
  ];

  return (
    <section className="py-20 section-dark">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
              <Mail className="w-5 h-5 text-white" />
              <span className="text-white font-semibold">Join Our Newsletter</span>
            </div>
            <h2 className="text-4xl font-bold text-white mb-4">
              Get Free FlutterFlow Resources & Updates
            </h2>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Join 1000+ developers getting free tips, course discounts, and exclusive FlutterFlow resources delivered to your inbox
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">What You'll Get:</h3>
              <div className="space-y-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                      {benefit.icon}
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-1">{benefit.title}</h4>
                      <p className="text-blue-100">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 shadow-2xl">
              <CardContent className="p-8">
                {!isSubmitted ? (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="email" className="block text-white font-semibold mb-2">
                        Email Address
                      </label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="bg-white/20 border-white/30 text-white placeholder-white/70 focus:border-white/50"
                      />
                    </div>
                    
                    <Button
                      type="submit"
                      size="lg"
                      className="w-full btn-animated btn-pink py-4 rounded-lg transform hover:scale-105"
                    >
                      <Gift className="w-5 h-5 mr-2" />
                      Get Free Resources
                    </Button>
                    
                    <p className="text-sm text-blue-100 text-center">
                      No spam, unsubscribe anytime. Your email is safe with us.
                    </p>
                  </form>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Mail className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">Thank You!</h3>
                    <p className="text-blue-100 mb-4">
                      Check your email for your free FlutterFlow templates and resources.
                    </p>
                    <Button
                      onClick={() => setIsSubmitted(false)}
                      className="btn-animated btn-ghost"
                    >
                      Subscribe Another Email
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 inline-block">
              <div className="flex items-center justify-center space-x-8">
                <div>
                  <div className="text-2xl font-bold text-white">1000+</div>
                  <div className="text-sm text-blue-100">Subscribers</div>
                </div>
                <div className="w-px h-8 bg-white/20"></div>
                <div>
                  <div className="text-2xl font-bold text-white">4.8/5</div>
                  <div className="text-sm text-blue-100">Rating</div>
                </div>
                <div className="w-px h-8 bg-white/20"></div>
                <div>
                  <div className="text-2xl font-bold text-white">Weekly</div>
                  <div className="text-sm text-blue-100">Updates</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EmailCaptureSection;
