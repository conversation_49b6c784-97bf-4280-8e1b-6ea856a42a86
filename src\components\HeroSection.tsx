
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Code, Smartphone, Users, Brain, Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const HeroSection = () => {
  return (
    <section className="relative overflow-hidden section-dark text-white min-h-screen flex items-center">
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-600/30 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-600/25 rounded-full blur-3xl animate-float animate-delay-300"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-500/20 rounded-full blur-2xl animate-pulse-slow"></div>
        <div className="absolute top-20 right-20 w-32 h-32 bg-purple-500/30 rounded-full blur-xl animate-float animate-delay-500"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-blue-500/25 rounded-full blur-xl animate-float animate-delay-200"></div>
        <div className="absolute top-40 left-1/3 w-24 h-24 bg-blue-400/20 rounded-full blur-lg animate-heartbeat"></div>
        <div className="absolute bottom-40 right-1/3 w-36 h-36 bg-purple-500/20 rounded-full blur-2xl animate-rotate-in"></div>
      </div>

      <div className="relative container mx-auto px-4 py-20 lg:py-32 z-10">
        <div className="max-w-4xl mx-auto text-center">
          <Badge className="mb-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 animate-bounce-in">
            🚀 Learn No-Code & AI Coding Skills
          </Badge>

          <div className="mb-6 flex justify-center space-x-4 animate-bounce-in">
            <div className="flex items-center space-x-2 glass-effect rounded-full px-4 py-2 animate-bounce-in animate-delay-100">
              <Code className="w-5 h-5 text-blue-300" />
              <span className="text-sm font-medium text-blue-100">FlutterFlow No-Code</span>
            </div>
            <div className="flex items-center space-x-2 glass-effect rounded-full px-4 py-2 animate-bounce-in animate-delay-200">
              <Brain className="w-5 h-5 text-purple-300" />
              <span className="text-sm font-medium text-purple-100">Vibe Coding with AI</span>
            </div>
          </div>

          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fade-in-up animate-delay-200 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
            Learn No-Code & AI Coding Skills
          </h1>

          <p className="text-xl md:text-2xl mb-8 text-gray-300 leading-relaxed max-w-3xl mx-auto animate-fade-in-up animate-delay-300">
            Master FlutterFlow No-Code Development and Vibe Coding with AI-powered tools like VS Code, Cursor, and Agents.
            Build real applications without traditional coding barriers.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8 animate-slide-in-bottom animate-delay-400">
            <Button
              size="lg"
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 transform hover:scale-105"
              asChild
            >
              <Link to="/live-classes">
                <Users className="w-5 h-5 mr-2" />
                <span>Join Live Classes</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>

            <Button
              size="lg"
              className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 transform hover:scale-105"
              asChild
            >
              <Link to="/courses">
                <Play className="w-5 h-5 mr-2" />
                <span>Access Recorded Courses</span>
              </Link>
            </Button>
          </div>

          <div className="mb-12 flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="sm"
              variant="outline"
              className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20"
              asChild
            >
              <Link to="/courses">
                Browse All Courses
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="card-elevated bg-blue-900/40 backdrop-blur-sm text-white rounded-lg p-4 shadow-glow-blue">
              <div className="text-3xl font-bold text-blue-400">2</div>
              <div className="text-white/90">Main Courses</div>
            </div>
            <div className="card-elevated bg-purple-900/40 backdrop-blur-sm text-white rounded-lg p-4 shadow-glow-purple">
              <div className="text-3xl font-bold text-purple-400">24/7</div>
              <div className="text-white/90">Access Anytime</div>
            </div>
            <div className="card-elevated bg-blue-900/40 backdrop-blur-sm text-white rounded-lg p-4 shadow-glow-blue">
              <div className="text-3xl font-bold text-blue-400">Lifetime</div>
              <div className="text-white/90">Course Access</div>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-pulse delay-500"></div>
    </section>
  );
};

export default HeroSection;
