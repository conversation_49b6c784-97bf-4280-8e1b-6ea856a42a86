/**
 * FreeCodeLap Platform - User Analytics Component
 * 
 * Advanced analytics and reporting for user management
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Activity, 
  Calendar,
  Download,
  Filter,
  Globe,
  Clock,
  UserCheck,
  UserX,
  Shield,
  GraduationCap
} from 'lucide-react';
import { userManagementService, UserStatistics } from '@/services/userManagementService';

interface UserAnalyticsProps {
  className?: string;
}

export const UserAnalytics: React.FC<UserAnalyticsProps> = ({ className }) => {
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<string>('30d');

  useEffect(() => {
    loadStatistics();
  }, [timeRange]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const stats = await userManagementService.getUserStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading user statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportAnalytics = async () => {
    try {
      // This would generate a comprehensive analytics report
      const csvContent = await userManagementService.exportUsersToCSV();
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user_analytics_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting analytics:', error);
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-400">Loading analytics...</div>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-400">No analytics data available</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white">User Analytics</h3>
          <p className="text-gray-400">Detailed insights into user behavior and engagement</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button
            onClick={exportAnalytics}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Users</p>
                <p className="text-2xl font-bold text-white">{statistics.totalUsers}</p>
                <p className="text-xs text-green-400">↗ +{statistics.newUsersThisMonth} this month</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Users</p>
                <p className="text-2xl font-bold text-white">{statistics.activeUsers}</p>
                <p className="text-xs text-blue-400">
                  {((statistics.activeUsers / statistics.totalUsers) * 100).toFixed(1)}% of total
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Enrolled Users</p>
                <p className="text-2xl font-bold text-white">{statistics.usersWithEnrollments}</p>
                <p className="text-xs text-purple-400">
                  {((statistics.usersWithEnrollments / statistics.totalUsers) * 100).toFixed(1)}% enrolled
                </p>
              </div>
              <GraduationCap className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Courses/User</p>
                <p className="text-2xl font-bold text-white">{statistics.averageCoursesPerUser.toFixed(1)}</p>
                <p className="text-xs text-yellow-400">Per enrolled user</p>
              </div>
              <BarChart3 className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Shield className="h-5 w-5 mr-2 text-gray-400" />
              User Roles Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-300">Students</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.studentUsers}</span>
                  <Badge className="bg-blue-600 text-white">
                    {((statistics.studentUsers / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-300">Admins</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.adminUsers}</span>
                  <Badge className="bg-purple-600 text-white">
                    {((statistics.adminUsers / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-gray-300">Suspended</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.suspendedUsers}</span>
                  <Badge className="bg-red-600 text-white">
                    {((statistics.suspendedUsers / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-gray-400" />
              Growth Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">New Users This Week</span>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.newUsersThisWeek}</span>
                  <Badge className="bg-green-600 text-white">
                    +{((statistics.newUsersThisWeek / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-300">New Users This Month</span>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.newUsersThisMonth}</span>
                  <Badge className="bg-blue-600 text-white">
                    +{((statistics.newUsersThisMonth / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300">Users Without Enrollments</span>
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold">{statistics.usersWithoutEnrollments}</span>
                  <Badge className="bg-yellow-600 text-white">
                    {((statistics.usersWithoutEnrollments / statistics.totalUsers) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Countries */}
      {statistics.topCountries.length > 0 && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Globe className="h-5 w-5 mr-2 text-gray-400" />
              Top Countries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {statistics.topCountries.slice(0, 6).map((country, index) => (
                <div key={country.country} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">#{index + 1}</span>
                    <span className="text-white">{country.country}</span>
                  </div>
                  <Badge className="bg-blue-600 text-white">
                    {country.count} users
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* User Growth Chart Placeholder */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-gray-400" />
            User Growth Over Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-600" />
              <p>Chart visualization would be implemented here</p>
              <p className="text-sm">Showing growth data for the last {statistics.userGrowthData.length} days</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
