import React, { useState } from 'react';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AuthModal } from '@/components/auth/AuthModal';
import { useAuth } from '@/contexts/AuthContext';
import {
  Calendar,
  Clock,
  Users,
  Video,
  MessageCircle,
  Mail,
  Phone,
  CheckCircle,
  Star,
  ArrowRight,
  Zap,
  BookOpen,
  Award,
  Globe,
  Headphones,
  Monitor
} from 'lucide-react';

export default function LiveClasses() {
  const { currentUser } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'register'>('register');

  const handleJoinClass = () => {
    if (!currentUser) {
      setAuthModalMode('register');
      setIsAuthModalOpen(true);
      return;
    }
    // <PERSON><PERSON> joining live class for authenticated users
    alert('Redirecting to live class...');
  };

  const liveClassFeatures = [
    {
      icon: Video,
      title: 'Interactive Live Sessions',
      description: 'Real-time learning with expert instructors'
    },
    {
      icon: MessageCircle,
      title: 'Q&A Support',
      description: 'Ask questions and get instant answers'
    },
    {
      icon: Users,
      title: 'Small Class Sizes',
      description: 'Maximum 20 students per session'
    },
    {
      icon: Award,
      title: 'Completion Certificates',
      description: 'Get certified upon course completion'
    },
    {
      icon: Monitor,
      title: 'Screen Sharing',
      description: 'Follow along with live coding sessions'
    },
    {
      icon: Headphones,
      title: '24/7 Support',
      description: 'Technical support whenever you need it'
    }
  ];

  const upcomingClasses = [
    {
      id: 1,
      title: 'FlutterFlow Basics for Beginners',
      instructor: 'Sarah Johnson',
      date: '2024-01-15',
      time: '10:00 AM - 12:00 PM',
      timezone: 'EST',
      spots: 8,
      maxSpots: 20,
      level: 'Beginner',
      price: 49
    },
    {
      id: 2,
      title: 'Advanced App Development',
      instructor: 'Mike Chen',
      date: '2024-01-17',
      time: '2:00 PM - 4:00 PM',
      timezone: 'EST',
      spots: 12,
      maxSpots: 15,
      level: 'Advanced',
      price: 79
    },
    {
      id: 3,
      title: 'Database Integration Workshop',
      instructor: 'Emily Davis',
      date: '2024-01-20',
      time: '11:00 AM - 1:00 PM',
      timezone: 'EST',
      spots: 5,
      maxSpots: 12,
      level: 'Intermediate',
      price: 59
    }
  ];

  const tools = [
    { name: 'Zoom', description: 'Primary video conferencing platform' },
    { name: 'FlutterFlow', description: 'No-code app development platform' },
    { name: 'Slack', description: 'Class communication and resources' },
    { name: 'GitHub', description: 'Code sharing and collaboration' },
    { name: 'Figma', description: 'Design and prototyping tool' }
  ];

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen section-dark">
      <Navigation />
      
      <div className="pt-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-5xl font-bold mb-6">
              Join Live FlutterFlow Classes
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Learn FlutterFlow with expert instructors in real-time. Get personalized guidance, 
              ask questions instantly, and build apps alongside fellow developers.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                size="lg"
                onClick={handleJoinClass}
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
              >
                <Users className="w-5 h-5 mr-2" />
                {currentUser ? 'Browse Live Classes' : 'Sign Up for Live Classes'}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4"
                onClick={() => window.location.href = '#contact'}
              >
                <MessageCircle className="w-5 h-5 mr-2" />
                Contact Us
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">500+</div>
                <div className="text-blue-100">Students Taught</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">50+</div>
                <div className="text-blue-100">Live Classes Monthly</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">4.9/5</div>
                <div className="text-blue-100">Average Rating</div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Why Choose Live Classes?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the benefits of real-time learning with expert guidance
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {liveClassFeatures.map((feature, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Upcoming Classes */}
        <div className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Upcoming Live Classes
              </h2>
              <p className="text-xl text-gray-600">
                Join our next live sessions and start building amazing apps
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {upcomingClasses.map((classItem) => (
                <Card key={classItem.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-2">
                      <Badge className={getLevelColor(classItem.level)}>
                        {classItem.level}
                      </Badge>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-600">${classItem.price}</div>
                      </div>
                    </div>
                    <CardTitle className="text-xl">{classItem.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-gray-600">
                        <Users className="w-4 h-4 mr-2" />
                        Instructor: {classItem.instructor}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Calendar className="w-4 h-4 mr-2" />
                        {classItem.date}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="w-4 h-4 mr-2" />
                        {classItem.time} ({classItem.timezone})
                      </div>
                      <div className="flex items-center text-gray-600">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        {classItem.spots} spots available (of {classItem.maxSpots})
                      </div>
                    </div>

                    <Button 
                      onClick={handleJoinClass}
                      className="w-full"
                      disabled={classItem.spots === 0}
                    >
                      {classItem.spots === 0 ? 'Class Full' : 'Reserve Spot'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Tools & Requirements */}
        <div className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Tools */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  Tools We Use
                </h2>
                <div className="space-y-4">
                  {tools.map((tool, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <Globe className="w-6 h-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{tool.name}</h3>
                            <p className="text-gray-600 text-sm">{tool.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Requirements */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  What You Need
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-1" />
                    <div>
                      <h3 className="font-semibold">Stable Internet Connection</h3>
                      <p className="text-gray-600">For smooth video streaming and interaction</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-1" />
                    <div>
                      <h3 className="font-semibold">Computer or Laptop</h3>
                      <p className="text-gray-600">Windows, Mac, or Linux with modern browser</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-1" />
                    <div>
                      <h3 className="font-semibold">Webcam & Microphone</h3>
                      <p className="text-gray-600">For interactive participation (optional)</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-1" />
                    <div>
                      <h3 className="font-semibold">FlutterFlow Account</h3>
                      <p className="text-gray-600">Free account to follow along with exercises</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div id="contact" className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Get in Touch
              </h2>
              <p className="text-xl text-gray-600">
                Have questions? We're here to help you get started
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">WhatsApp Support</h3>
                  <p className="text-gray-600 mb-6">
                    Get instant answers to your questions via WhatsApp
                  </p>
                  <Button 
                    onClick={() => window.open('https://wa.me/1234567890', '_blank')}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Chat on WhatsApp
                  </Button>
                </CardContent>
              </Card>

              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">Email Support</h3>
                  <p className="text-gray-600 mb-6">
                    Send us detailed questions via email
                  </p>
                  <Button 
                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                    variant="outline"
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Send Email
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultMode={authModalMode}
      />
    </div>
  );
}
