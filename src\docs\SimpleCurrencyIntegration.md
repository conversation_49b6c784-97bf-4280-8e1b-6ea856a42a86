# Simple Currency System Integration Guide

## Overview
This guide shows how to integrate the simplified USD-based currency system into your existing FreeCodeLap components.

## 🚀 Quick Start

### 1. Test the System
Visit `/currency-test` in your browser to see the complete system in action.

### 2. Basic Price Display
Replace existing price displays with the new components:

```tsx
// OLD: Static price display
<span className="text-lg font-bold">${course.price}</span>

// NEW: USD with local equivalent
import { SimpleUSDPrice } from '@/components/ui/ExchangeRateDisplay';
<SimpleUSDPrice usdAmount={course.price} />
```

### 3. Course Cards Integration
```tsx
// In your course card components
import { SimpleUSDPrice } from '@/components/ui/ExchangeRateDisplay';

const CourseCard = ({ course }) => (
  <div className="course-card">
    <img src={course.thumbnail} alt={course.title} />
    <h3>{course.title}</h3>
    
    {/* Replace price display */}
    <SimpleUSDPrice usdAmount={course.price} />
    
    <button>Enroll Now</button>
  </div>
);
```

### 4. Course Detail Page
```tsx
// In CourseDetail.tsx
import { ExchangeRateDisplay } from '@/components/ui/ExchangeRateDisplay';

const CourseDetail = ({ course }) => (
  <div className="course-detail">
    <h1>{course.title}</h1>
    
    {/* Detailed price display */}
    <ExchangeRateDisplay 
      usdAmount={course.price} 
      variant="detailed" 
    />
    
    <button>Enroll Now</button>
  </div>
);
```

### 5. Checkout Page Integration
```tsx
// In Checkout.tsx or payment pages
import { CheckoutSummary } from '@/components/checkout/CheckoutSummary';

const CheckoutPage = ({ course }) => (
  <div className="checkout-page">
    <CheckoutSummary course={course} />
    
    {/* Payment form */}
    <PaymentForm />
  </div>
);
```

## 🎨 Component Variants

### ExchangeRateDisplay Variants
```tsx
// Compact - for course cards
<ExchangeRateDisplay usdAmount={49.99} variant="compact" />

// Default - for general use
<ExchangeRateDisplay usdAmount={49.99} />

// Detailed - for course detail pages
<ExchangeRateDisplay usdAmount={49.99} variant="detailed" />

// Checkout - for payment pages
<ExchangeRateDisplay usdAmount={49.99} variant="checkout" />
```

### Currency Selector
```tsx
// Add to navigation or user settings
import { CurrencySelector } from '@/components/ui/ExchangeRateDisplay';

<CurrencySelector />
```

## 💳 Payment Integration

### Available Payment Methods
```tsx
import { simplePaymentService } from '@/services/simplePaymentService';

// Get available methods for user
const paymentMethods = simplePaymentService.getAvailablePaymentMethods(
  userCountry // 'KE' for Kenya, 'US' for USA, etc.
);

// Returns:
// [
//   { method: 'card', name: 'Credit/Debit Card', gateway: 'stripe', available: true },
//   { method: 'mpesa', name: 'M-Pesa', gateway: 'paystack', available: true } // Only for Kenya
// ]
```

### Payment Processing
```tsx
import { simplePaymentService } from '@/services/simplePaymentService';

const handlePayment = async (course, paymentMethod) => {
  const paymentData = {
    courseId: course.id,
    courseTitle: course.title,
    usdAmount: course.price,
    userEmail: user.email,
    userName: user.displayName,
    userCountry: user.country, // 'KE', 'US', etc.
    paymentMethod: paymentMethod, // 'card' or 'mpesa'
  };

  const result = await simplePaymentService.initializePayment(paymentData);
  
  if (result.success) {
    // Redirect to payment gateway or show payment form
    if (result.gateway === 'stripe') {
      // Handle Stripe payment
    } else if (result.gateway === 'paystack') {
      // Handle Paystack payment
    }
  }
};
```

## 🔧 Existing Component Updates

### Homepage Course Section
```tsx
// In your homepage course section
{courses.map(course => (
  <div key={course.id} className="course-card">
    <h3>{course.title}</h3>
    <SimpleUSDPrice usdAmount={course.price} />
    <button onClick={() => navigate(`/course/${course.id}`)}>
      View Course
    </button>
  </div>
))}
```

### All Courses Page
```tsx
// In AllCourses.tsx
import { SimpleUSDPrice } from '@/components/ui/ExchangeRateDisplay';

{courses.map(course => (
  <div className="course-grid-item">
    <img src={course.thumbnail} alt={course.title} />
    <h3>{course.title}</h3>
    <p>{course.description}</p>
    <SimpleUSDPrice usdAmount={course.price} />
    <button>Enroll Now</button>
  </div>
))}
```

### Navigation Currency Selector
```tsx
// In your main navigation/header
import { CurrencySelector } from '@/components/ui/ExchangeRateDisplay';

const Navigation = () => (
  <nav className="navigation">
    <div className="nav-items">
      {/* Other nav items */}
      <CurrencySelector />
    </div>
  </nav>
);
```

## 🎯 Key Benefits

### For Users
- **Clear Pricing**: See USD price + local equivalent
- **No Surprises**: Know exactly what they'll pay
- **Local Context**: Understand value in their currency
- **Simple Checkout**: Single payment flow

### For Development
- **Simple Integration**: Just replace price components
- **No Complex Logic**: No multi-gateway selection
- **Easy Maintenance**: Single payment flow
- **Better Performance**: No complex conversions

### For Business
- **Consistent Revenue**: All payments in USD
- **Global Reach**: Works in any country
- **Lower Complexity**: Easier to manage
- **Better Analytics**: Single currency reporting

## 📊 Data Structure

### Course Data
```typescript
// Ensure your courses have USD prices
interface Course {
  id: string;
  title: string;
  price: number; // USD price (e.g., 49.99)
  currency: 'USD'; // Always USD
  // ... other fields
}
```

### User Data
```typescript
// Optional: Store user's country for payment methods
interface User {
  // ... existing fields
  country?: string; // 'KE', 'US', 'NG', etc.
  preferredCurrency?: string; // For display only
}
```

## 🧪 Testing

### Manual Testing Steps
1. Visit `/currency-test` to see all components
2. Change currency selector and watch prices update
3. Test different currencies: KES, NGN, EUR, GBP
4. Verify M-Pesa appears only for Kenyan users
5. Check that all payments remain in USD

### Browser Console Testing
```javascript
// Test currency detection
localStorage.setItem('userDisplayCurrency', 'KES');
window.location.reload();

// Test different currencies
['USD', 'KES', 'NGN', 'EUR', 'GBP'].forEach(currency => {
  localStorage.setItem('userDisplayCurrency', currency);
  console.log(`Testing ${currency}...`);
  // Refresh to see changes
});
```

## 🚀 Migration Steps

### Step 1: Update Course Data
Ensure all courses have USD prices:
```typescript
const courses = [
  { id: '1', title: 'React Course', price: 49.99, currency: 'USD' },
  { id: '2', title: 'Node Course', price: 79.99, currency: 'USD' }
];
```

### Step 2: Replace Price Components
Find and replace existing price displays:
```tsx
// Find: <span>${course.price}</span>
// Replace: <SimpleUSDPrice usdAmount={course.price} />
```

### Step 3: Update Checkout Pages
Replace checkout components:
```tsx
// Replace existing checkout with:
<CheckoutSummary course={selectedCourse} />
```

### Step 4: Add Currency Selector
Add to navigation or user settings:
```tsx
<CurrencySelector />
```

### Step 5: Test Everything
1. Test price displays across all pages
2. Test currency changes
3. Test checkout flow
4. Test payment methods

## 📞 Support

### Common Issues
- **Prices not updating**: Check if SimpleCurrencyProvider is wrapped correctly
- **Currency not persisting**: Check localStorage and user profile updates
- **M-Pesa not showing**: Ensure user currency is set to KES
- **Exchange rates failing**: Check API connectivity and fallback rates

### Debug Tools
```javascript
// Check current currency
console.log(localStorage.getItem('userDisplayCurrency'));

// Check exchange rates
// (Available in React DevTools under SimpleCurrencyContext)
```

---

**The simplified currency system is now ready for production use!** 🌍💰
