import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2,
  CreditCard,
  Shield,
  Globe,
  Zap
} from 'lucide-react';

declare global {
  interface Window {
    PaystackPop: any;
  }
}

export const PaystackTest: React.FC = () => {
  const [checks, setChecks] = useState({
    csp: false,
    script: false,
    ssl: false,
    keys: false,
    popup: false
  });
  const [loading, setLoading] = useState(true);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    setLoading(true);
    setError(null);

    // Check SSL
    const isSSL = window.location.protocol === 'https:' || 
                  window.location.hostname === 'localhost';
    
    // Check environment variables
    const hasKeys = !!PAYSTACK_PUBLIC_KEY;

    // Check CSP by trying to load Paystack script
    const scriptLoaded = await checkPaystackScript();

    setChecks({
      csp: scriptLoaded,
      script: scriptLoaded,
      ssl: isSSL,
      keys: hasKeys,
      popup: scriptLoaded && window.PaystackPop
    });

    setLoading(false);
  };

  const checkPaystackScript = (): Promise<boolean> => {
    return new Promise((resolve) => {
      if (window.PaystackPop) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      
      const timeout = setTimeout(() => {
        resolve(false);
      }, 10000);

      script.onload = () => {
        clearTimeout(timeout);
        resolve(true);
      };

      script.onerror = () => {
        clearTimeout(timeout);
        resolve(false);
      };

      document.head.appendChild(script);
    });
  };

  const testPayment = () => {
    if (!window.PaystackPop) {
      setError('Paystack not loaded');
      return;
    }

    if (!PAYSTACK_PUBLIC_KEY) {
      setError('Paystack public key not found');
      return;
    }

    try {
      const config = {
        key: PAYSTACK_PUBLIC_KEY,
        email: '<EMAIL>',
        amount: 100 * 100, // 100 KES in kobo
        currency: 'KES',
        channels: ['card'],
        callback: (response: any) => {
          setTestResult(`Test successful! Reference: ${response.reference}`);
        },
        onClose: () => {
          setTestResult('Test cancelled by user');
        }
      };

      const handler = window.PaystackPop.setup(config);
      handler.openIframe();
    } catch (error) {
      setError(`Test failed: ${error}`);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  const getStatusBadge = (status: boolean) => {
    return (
      <Badge variant={status ? "default" : "destructive"}>
        {status ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  const allChecksPassed = Object.values(checks).every(Boolean);

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Paystack Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Running diagnostics...</span>
            </div>
          ) : (
            <>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <span>SSL Certificate</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(checks.ssl)}
                    {getStatusBadge(checks.ssl)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    <span>Environment Variables</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(checks.keys)}
                    {getStatusBadge(checks.keys)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    <span>CSP Configuration</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(checks.csp)}
                    {getStatusBadge(checks.csp)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Paystack Script</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(checks.script)}
                    {getStatusBadge(checks.script)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <span>Payment Popup</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(checks.popup)}
                    {getStatusBadge(checks.popup)}
                  </div>
                </div>
              </div>

              {error && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-800">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {testResult && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="text-green-800">
                    {testResult}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex gap-2">
                <Button onClick={runDiagnostics} variant="outline">
                  Re-run Diagnostics
                </Button>
                <Button 
                  onClick={testPayment} 
                  disabled={!allChecksPassed}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Test Payment (KES 100)
                </Button>
              </div>

              {!allChecksPassed && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-yellow-800">
                    Some checks failed. Please fix the issues before testing payments.
                    Check the PAYSTACK_CSP_TROUBLESHOOTING.md guide for solutions.
                  </AlertDescription>
                </Alert>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Configuration Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>
            <strong>Environment:</strong> {import.meta.env.MODE}
          </div>
          <div>
            <strong>Protocol:</strong> {window.location.protocol}
          </div>
          <div>
            <strong>Host:</strong> {window.location.host}
          </div>
          <div>
            <strong>Paystack Key:</strong> {PAYSTACK_PUBLIC_KEY ? 
              `${PAYSTACK_PUBLIC_KEY.substring(0, 10)}...` : 'Not configured'
            }
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
