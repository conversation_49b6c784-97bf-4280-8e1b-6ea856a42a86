/**
 * Test utility for the simplified manual enrollment system
 */

import { enrollmentManagementService } from '@/services/enrollmentManagementService';

export const testSimpleEnrollmentSystem = async () => {
  console.log('🧪 Testing Simplified Manual Enrollment System...');
  
  try {
    // Test 1: Check available courses (this should always work)
    console.log('📚 Test 1: Loading available courses...');
    const courses = await enrollmentManagementService.getAvailableCourses();
    console.log(`✅ Found ${courses.length} available courses`);
    
    if (courses.length > 0) {
      console.log('📋 Sample courses:', courses.slice(0, 3).map(c => ({
        title: c.title,
        price: c.price,
        currency: c.currency,
        enrollmentCount: c.enrollmentCount
      })));
    } else {
      console.log('⚠️ No courses available - please create some courses first');
    }

    // Test 2: Test email validation
    console.log('📧 Test 2: Testing email validation...');
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      'invalid-email',
      '<EMAIL>'
    ];
    
    testEmails.forEach(email => {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      console.log(`📧 ${email}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    });

    // Test 3: Test enrollment data structure
    console.log('📊 Test 3: Testing enrollment data structure...');
    const sampleEnrollmentData = {
      userEmail: '<EMAIL>',
      userName: 'Test User',
      courseId: courses.length > 0 ? courses[0].id : 'sample-course-id',
      adminNote: 'Test enrollment from simplified system'
    };
    
    console.log('📋 Sample enrollment data:', sampleEnrollmentData);

    // Test 4: Check existing enrollments
    console.log('📚 Test 4: Checking existing enrollments...');
    const enrollments = await enrollmentManagementService.getAllEnrollments(5);
    console.log(`✅ Found ${enrollments.length} existing enrollments`);
    
    if (enrollments.length > 0) {
      const enrollment = enrollments[0];
      console.log('📋 Sample enrollment:', {
        user: enrollment.userName,
        email: enrollment.userEmail,
        course: enrollment.courseTitle,
        type: enrollment.paymentInfo ? 'Paid' : 'Manual',
        enrolledAt: enrollment.enrolledAt.toLocaleDateString()
      });
    }

    console.log('🎉 Simplified enrollment system tests completed successfully!');
    
    return {
      coursesAvailable: courses.length,
      enrollmentsFound: enrollments.length,
      systemReady: courses.length > 0,
      success: true
    };
    
  } catch (error) {
    console.error('❌ Simplified enrollment system test failed:', error);
    return {
      error: error.message,
      success: false
    };
  }
};

export const logSimpleEnrollmentInstructions = () => {
  console.log('📋 Simplified Manual Enrollment Instructions:');
  console.log('');
  console.log('🎯 How to use the new simplified system:');
  console.log('1. Click "Manual Enrollment" button');
  console.log('2. Enter the user\'s email address (required)');
  console.log('3. Optionally enter the user\'s display name');
  console.log('4. Select a course from the dropdown');
  console.log('5. Add an optional admin note');
  console.log('6. Click "Enroll User" to complete');
  console.log('');
  console.log('✅ Key Benefits:');
  console.log('• No complex user loading - just enter email directly');
  console.log('• Real-time email validation');
  console.log('• Works with any valid email address');
  console.log('• Automatic user lookup if they exist in system');
  console.log('• Email notifications sent automatically');
  console.log('• Simple and reliable - no permission issues');
  console.log('');
  console.log('📧 Email Handling:');
  console.log('• System automatically looks up existing users by email');
  console.log('• If user exists: uses their actual user ID');
  console.log('• If user doesn\'t exist: creates enrollment with email');
  console.log('• Email notification sent in both cases');
  console.log('• User can access course when they create account');
  console.log('');
  console.log('🚀 System is ready and simplified!');
};

export const validateEmailAddress = (email: string): { isValid: boolean; message: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email) {
    return { isValid: false, message: '' };
  }
  
  if (emailRegex.test(email)) {
    return { isValid: true, message: 'Valid email address' };
  } else {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
};

// Auto-run instructions
if (typeof window !== 'undefined') {
  console.log('📧 FreeCodeLap Simplified Manual Enrollment System');
  logSimpleEnrollmentInstructions();
}
