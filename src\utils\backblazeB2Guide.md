# Backblaze B2 CORS Configuration Guide

## Issue
Videos hosted on Backblaze B2 are failing to load in the video player due to CORS (Cross-Origin Resource Sharing) restrictions.

**Error**: `❌ Video URL that failed: https://ahmedtakal.s3.us-east-005.backblazeb2.com/videos/ssstik.io_1750581602196.mp4`

## Root Cause
Backblaze B2 buckets need proper CORS configuration to allow web browsers to access video files from different domains.

## Solution: Configure CORS Rules

### Step 1: Access Backblaze B2 Console
1. Go to [Backblaze B2 Console](https://secure.backblaze.com/b2_buckets.htm)
2. Log in to your account
3. Find your bucket: `ahmedtakal`

### Step 2: Configure CORS Rules
1. Click on your bucket name
2. Go to "Bucket Settings" or "CORS Rules"
3. Add the following CORS configuration:

```json
[
  {
    "corsRuleName": "allowVideoAccess",
    "allowedOrigins": [
      "*"
    ],
    "allowedHeaders": [
      "*"
    ],
    "allowedOperations": [
      "b2_download_file_by_id",
      "b2_download_file_by_name"
    ],
    "maxAgeSeconds": 3600
  }
]
```

### Step 3: Alternative CORS Configuration (More Secure)
If you want to restrict to specific domains:

```json
[
  {
    "corsRuleName": "allowVideoAccess",
    "allowedOrigins": [
      "https://your-domain.com",
      "https://localhost:3000",
      "https://localhost:5173"
    ],
    "allowedHeaders": [
      "Content-Type",
      "Range",
      "Authorization"
    ],
    "allowedOperations": [
      "b2_download_file_by_id",
      "b2_download_file_by_name"
    ],
    "maxAgeSeconds": 3600
  }
]
```

### Step 4: Verify Bucket is Public
1. Make sure your bucket is set to "Public"
2. Check that files have public read permissions

### Step 5: Test the Configuration
1. Wait 5-10 minutes for changes to propagate
2. Try accessing the video URL directly in browser
3. Test the video player in your application

## Alternative Solutions

### Option 1: Use Backblaze B2 CDN
Configure a CDN (like Cloudflare) in front of your B2 bucket for better performance and easier CORS management.

### Option 2: Proxy Through Your Server
Create an endpoint on your server that proxies video requests to B2, avoiding CORS issues entirely.

### Option 3: Use Different Video Hosting
Consider alternatives like:
- AWS S3 with CloudFront
- Google Cloud Storage
- Vimeo Pro
- YouTube (unlisted videos)

## Testing Commands

### Test CORS with curl:
```bash
curl -H "Origin: https://your-domain.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://ahmedtakal.s3.us-east-005.backblazeb2.com/videos/ssstik.io_1750581602196.mp4
```

### Test direct access:
```bash
curl -I https://ahmedtakal.s3.us-east-005.backblazeb2.com/videos/ssstik.io_1750581602196.mp4
```

## Expected Response Headers
After CORS is configured, you should see these headers:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, HEAD, OPTIONS
Access-Control-Allow-Headers: *
Content-Type: video/mp4
```

## Troubleshooting

### If CORS still doesn't work:
1. Check bucket permissions
2. Verify file is publicly accessible
3. Clear browser cache
4. Try incognito/private browsing mode
5. Check browser console for detailed error messages

### Common Issues:
- **File not public**: Make sure individual files are set to public
- **Bucket not public**: Bucket must allow public access
- **Wrong CORS rules**: Double-check the JSON syntax
- **Propagation delay**: Wait 10-15 minutes after changes

## Contact Support
If issues persist, contact Backblaze B2 support with:
- Bucket name: `ahmedtakal`
- File URL that's failing
- CORS configuration you applied
- Browser console error messages
