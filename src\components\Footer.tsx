
import React from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Github, Users, MessageCircle } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: "Live Classes", href: "#courses" },
    { name: "Recorded Courses", href: "#courses" },
    { name: "About Instructor", href: "#about" },
    { name: "Testimonials", href: "#testimonials" },
    { name: "FAQ", href: "#faq" },
    { name: "Contact", href: "#contact" }
  ];

  const courseLinks = [
    { name: "Complete FlutterFlow Course", href: "#" },
    { name: "Advanced Integrations", href: "#" },
    { name: "AI & ChatGPT Integration", href: "#" },
    { name: "Firebase Masterclass", href: "#" },
    { name: "Revenue Cat & Payments", href: "#" },
    { name: "Custom Widgets", href: "#" }
  ];

  const supportLinks = [
    { name: "Getting Started Guide", href: "#" },
    { name: "Student Resources", href: "#" },
    { name: "Community Forum", href: "#" },
    { name: "Course Updates", href: "#" },
    { name: "Technical Support", href: "#" },
    { name: "Refund Policy", href: "/refund-policy" }
  ];

  return (
    <footer className="section-dark text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-12">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              FlutterFlow Courses
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Master FlutterFlow development with expert-led courses. Build real apps, integrate AI, and monetize your skills with our comprehensive training programs.
            </p>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300">Nairobi, Kenya</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300">+254 712 345 678</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Courses */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Popular Courses</h4>
            <ul className="space-y-3">
              {courseLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Support</h4>
            <ul className="space-y-3">
              {supportLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Links & Newsletter */}
        <div className="border-t border-gray-800 pt-8 mb-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="flex items-center space-x-6">
              <span className="text-gray-300 font-medium">Follow us:</span>
              <div className="flex space-x-4">
                <a
                  href="https://github.com"
                  className="btn-animated btn-ghost p-3 rounded-full"
                >
                  <Github className="w-5 h-5" />
                </a>
                <a
                  href="https://linkedin.com"
                  className="btn-animated btn-blue p-3 rounded-full"
                >
                  <Users className="w-5 h-5" />
                </a>
                <a
                  href="https://wa.me/254712345678"
                  className="btn-animated btn-pink p-3 rounded-full"
                >
                  <MessageCircle className="w-5 h-5" />
                </a>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full text-center animate-bounce-in">
                <span className="font-semibold">🎓 500+ Students Trained</span>
              </div>
              <div className="bg-gradient-to-r from-pink-600 to-blue-600 text-white px-6 py-3 rounded-full text-center animate-bounce-in animate-delay-200">
                <span className="font-semibold">⭐ 4.9/5 Average Rating</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4 text-gray-400 text-sm">
            <div>
              <p>&copy; {currentYear} FlutterFlow Courses. All rights reserved.</p>
            </div>
            <div className="flex flex-wrap gap-6">
              <Link to="/privacy-policy" className="hover:text-blue-400 transition-colors duration-300">Privacy Policy</Link>
              <Link to="/terms-of-service" className="hover:text-blue-400 transition-colors duration-300">Terms of Service</Link>
              <Link to="/cookie-policy" className="hover:text-blue-400 transition-colors duration-300">Cookie Policy</Link>
              <Link to="/refund-policy" className="hover:text-blue-400 transition-colors duration-300">Refund Policy</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
