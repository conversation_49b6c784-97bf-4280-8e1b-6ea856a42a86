import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, CheckCircle, Info, Lightbulb, Copy } from 'lucide-react';

export const VideoTroubleshooter: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const [diagnosis, setDiagnosis] = useState<any>(null);

  const diagnoseUrl = (url: string) => {
    if (!url.trim()) return;

    const result: any = {
      url: url,
      issues: [],
      suggestions: [],
      severity: 'info'
    };

    // Check URL format
    if (!url.startsWith('http')) {
      result.issues.push('URL must start with http:// or https://');
      result.suggestions.push('Add https:// to the beginning of your URL');
      result.severity = 'error';
    }

    if (url.startsWith('http://')) {
      result.issues.push('HTTP URLs may be blocked by browsers for security');
      result.suggestions.push('Use HTTPS instead of HTTP if possible');
      result.severity = 'warning';
    }

    // YouTube URL checks
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      result.type = 'YouTube';
      
      if (url.includes('youtube.com/embed/')) {
        result.issues.push('YouTube embed URLs may not work with ReactPlayer');
        const videoId = url.split('embed/')[1]?.split('?')[0];
        if (videoId) {
          result.suggestions.push(`Try: https://www.youtube.com/watch?v=${videoId}`);
        }
        result.severity = 'warning';
      }
      
      if (url.includes('youtube.com/shorts/')) {
        result.issues.push('YouTube Shorts URLs are not supported');
        result.suggestions.push('Use regular YouTube video URLs instead');
        result.severity = 'error';
      }
      
      if (!url.includes('watch?v=') && !url.includes('youtu.be/')) {
        result.issues.push('Invalid YouTube URL format');
        result.suggestions.push('Use format: https://www.youtube.com/watch?v=VIDEO_ID');
        result.severity = 'error';
      }
    }

    // Vimeo URL checks
    else if (url.includes('vimeo.com')) {
      result.type = 'Vimeo';
      
      if (url.includes('player.vimeo.com')) {
        result.issues.push('Vimeo player URLs may not work');
        const videoId = url.split('video/')[1]?.split('?')[0];
        if (videoId) {
          result.suggestions.push(`Try: https://vimeo.com/${videoId}`);
        }
        result.severity = 'warning';
      }
    }

    // Direct video file checks
    else if (url.includes('.mp4') || url.includes('.webm') || url.includes('.ogg')) {
      result.type = 'Direct Video File';
      
      if (!url.includes('.mp4')) {
        result.issues.push('Non-MP4 formats may have compatibility issues');
        result.suggestions.push('MP4 format is most widely supported');
        result.severity = 'warning';
      }
    }

    // Google Drive checks
    else if (url.includes('drive.google.com')) {
      result.type = 'Google Drive';
      result.issues.push('Google Drive links often require special formatting');
      
      if (url.includes('/file/d/')) {
        const fileId = url.match(/\/d\/([a-zA-Z0-9-_]+)/)?.[1];
        if (fileId) {
          result.suggestions.push(`Try: https://drive.google.com/file/d/${fileId}/preview`);
          result.suggestions.push('Make sure the file is publicly accessible');
        }
      } else {
        result.suggestions.push('Use the file ID format: /file/d/FILE_ID/preview');
      }
      result.severity = 'warning';
    }

    // Unknown format
    else {
      result.type = 'Unknown';
      result.issues.push('Unknown video format or platform');
      result.suggestions.push('Try using YouTube, Vimeo, or direct MP4 files');
      result.severity = 'warning';
    }

    // General suggestions
    if (result.issues.length === 0) {
      result.suggestions.push('URL format looks good! If still not working, try the "Force Ready" button');
      result.severity = 'success';
    }

    setDiagnosis(result);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'border-red-500 bg-red-900/20';
      case 'warning':
        return 'border-yellow-500 bg-yellow-900/20';
      case 'success':
        return 'border-green-500 bg-green-900/20';
      default:
        return 'border-blue-500 bg-blue-900/20';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      default:
        return <Info className="w-5 h-5 text-blue-400" />;
    }
  };

  const commonIssues = [
    {
      issue: 'Video shows "Loading video..." forever',
      causes: [
        'ReactPlayer onReady event not firing',
        'Network connectivity issues',
        'CORS policy blocking the video',
        'Invalid video URL format'
      ],
      solutions: [
        'Click the "Force Ready" button in the player',
        'Try switching to the Simple player',
        'Test the URL with the Quick Video Test tool',
        'Check if the direct link works in a new tab'
      ]
    },
    {
      issue: 'Video URL works in browser but not in player',
      causes: [
        'CORS headers not allowing embedding',
        'Video requires authentication',
        'ReactPlayer doesn\'t support the format'
      ],
      solutions: [
        'Use a different video hosting service',
        'Convert to MP4 and host on a CORS-friendly server',
        'Try YouTube or Vimeo for better compatibility'
      ]
    },
    {
      issue: 'YouTube videos not playing',
      causes: [
        'Wrong URL format (embed vs watch)',
        'Video is private or restricted',
        'Age-restricted content'
      ],
      solutions: [
        'Use https://www.youtube.com/watch?v=VIDEO_ID format',
        'Make sure video is public',
        'Test with a different YouTube video'
      ]
    }
  ];

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">🔧 Video Troubleshooter</CardTitle>
        <p className="text-gray-400 text-sm">
          Diagnose and fix video playback issues
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* URL Diagnosis */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">URL Diagnosis</h3>
          <div className="flex gap-2">
            <Input
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              placeholder="Paste your video URL here"
              className="bg-gray-700 border-gray-600 text-white"
              onKeyPress={(e) => e.key === 'Enter' && diagnoseUrl(videoUrl)}
            />
            <Button 
              onClick={() => diagnoseUrl(videoUrl)}
              disabled={!videoUrl.trim()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Diagnose
            </Button>
          </div>

          {diagnosis && (
            <div className={`border rounded-lg p-4 ${getSeverityColor(diagnosis.severity)}`}>
              <div className="flex items-center gap-2 mb-3">
                {getSeverityIcon(diagnosis.severity)}
                <span className="font-medium text-white">
                  {diagnosis.type} - {diagnosis.severity.toUpperCase()}
                </span>
              </div>

              {diagnosis.issues.length > 0 && (
                <div className="mb-3">
                  <h4 className="text-white font-medium mb-2">Issues Found:</h4>
                  <ul className="space-y-1">
                    {diagnosis.issues.map((issue: string, index: number) => (
                      <li key={index} className="text-red-300 text-sm flex items-start gap-2">
                        <span className="text-red-400 mt-1">•</span>
                        {issue}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {diagnosis.suggestions.length > 0 && (
                <div>
                  <h4 className="text-white font-medium mb-2">Suggestions:</h4>
                  <ul className="space-y-2">
                    {diagnosis.suggestions.map((suggestion: string, index: number) => (
                      <li key={index} className="text-green-300 text-sm flex items-start gap-2">
                        <Lightbulb className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          {suggestion}
                          {suggestion.startsWith('Try: ') && (
                            <Button
                              size="sm"
                              onClick={() => copyToClipboard(suggestion.replace('Try: ', ''))}
                              className="ml-2 h-6 px-2 bg-green-600 hover:bg-green-700"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Common Issues */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">Common Issues & Solutions</h3>
          <div className="space-y-4">
            {commonIssues.map((item, index) => (
              <div key={index} className="bg-gray-900 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-2">{item.issue}</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-gray-400 font-medium mb-2">Possible Causes:</h5>
                    <ul className="space-y-1">
                      {item.causes.map((cause, i) => (
                        <li key={i} className="text-gray-300 text-sm flex items-start gap-2">
                          <span className="text-red-400 mt-1">•</span>
                          {cause}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h5 className="text-gray-400 font-medium mb-2">Solutions:</h5>
                    <ul className="space-y-1">
                      {item.solutions.map((solution, i) => (
                        <li key={i} className="text-gray-300 text-sm flex items-start gap-2">
                          <span className="text-green-400 mt-1">•</span>
                          {solution}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Fixes */}
        <div className="bg-gray-900 p-4 rounded-lg">
          <h3 className="text-white font-medium mb-3">Quick Fixes to Try</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="text-gray-400 font-medium mb-2">In the Player:</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Click "Force Ready" button</li>
                <li>• Switch to Simple player</li>
                <li>• Click "Retry" button</li>
                <li>• Try "Open Direct" link</li>
              </ul>
            </div>
            <div>
              <h4 className="text-gray-400 font-medium mb-2">URL Fixes:</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Change HTTP to HTTPS</li>
                <li>• Use YouTube watch URLs</li>
                <li>• Test with MP4 files</li>
                <li>• Check public accessibility</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoTroubleshooter;
