import React, { useState } from 'react';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

const CreateCourse = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const createSampleCourses = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    const sampleCourses = [
      {
        id: 'flutterflow-complete',
        title: 'Complete FlutterFlow Masterclass',
        description: 'Master FlutterFlow from beginner to advanced. Build real-world mobile apps without coding.',
        instructor: '<PERSON>',
        level: 'beginner',
        price: 99.99,
        originalPrice: 199.99,
        duration: 720,
        thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Build professional mobile apps with FlutterFlow',
          'Master advanced UI/UX design principles',
          'Integrate APIs and backend services',
          'Deploy apps to App Store and Google Play'
        ],
        tags: ['FlutterFlow', 'No-Code', 'Mobile Development', 'App Development'],
        category: 'Development',
        isPublished: true,
        featured: true,
        enrollmentCount: 2500,
        rating: 4.9,
        reviewCount: 320
      },
      {
        id: 'ai-coding-complete',
        title: 'AI-Powered Coding Bootcamp',
        description: 'Learn to code 10x faster using AI tools like Cursor, GitHub Copilot, and ChatGPT.',
        instructor: 'Ahmed Takal',
        level: 'intermediate',
        price: 149.99,
        originalPrice: 299.99,
        duration: 480,
        thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Master AI-assisted coding techniques',
          'Use Cursor and VS Code effectively',
          'Build projects 10x faster with AI',
          'Automate repetitive coding tasks'
        ],
        tags: ['AI', 'Coding', 'Productivity', 'Automation'],
        category: 'Programming',
        isPublished: true,
        featured: true,
        enrollmentCount: 1800,
        rating: 4.8,
        reviewCount: 240
      },
      {
        id: 'react-advanced',
        title: 'Advanced React Development',
        description: 'Master advanced React concepts and build scalable web applications.',
        instructor: 'Ahmed Takal',
        level: 'advanced',
        price: 129.99,
        originalPrice: 249.99,
        duration: 600,
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Master React hooks and context',
          'Build scalable component architectures',
          'Implement state management with Redux',
          'Optimize React app performance'
        ],
        tags: ['React', 'JavaScript', 'Web Development', 'Frontend'],
        category: 'Web Development',
        isPublished: true,
        featured: false,
        enrollmentCount: 1200,
        rating: 4.7,
        reviewCount: 180
      },
      {
        id: 'python-automation',
        title: 'Python Automation Mastery',
        description: 'Automate your workflow with Python. From web scraping to task automation.',
        instructor: 'Ahmed Takal',
        level: 'intermediate',
        price: 89.99,
        originalPrice: 179.99,
        duration: 420,
        thumbnail: 'https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Automate repetitive tasks with Python',
          'Build web scrapers and data collectors',
          'Create automated workflows',
          'Deploy automation scripts to cloud'
        ],
        tags: ['Python', 'Automation', 'Scripting', 'Productivity'],
        category: 'Programming',
        isPublished: true,
        featured: false,
        enrollmentCount: 950,
        rating: 4.6,
        reviewCount: 125
      },
      {
        id: 'ui-ux-design',
        title: 'UI/UX Design Fundamentals',
        description: 'Learn modern UI/UX design principles and create beautiful user interfaces.',
        instructor: 'Ahmed Takal',
        level: 'beginner',
        price: 79.99,
        originalPrice: 159.99,
        duration: 360,
        thumbnail: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Master UI/UX design principles',
          'Create wireframes and prototypes',
          'Design responsive interfaces',
          'Conduct user research and testing'
        ],
        tags: ['UI/UX', 'Design', 'Figma', 'Prototyping'],
        category: 'Design',
        isPublished: true,
        featured: false,
        enrollmentCount: 1500,
        rating: 4.8,
        reviewCount: 200
      },
      {
        id: 'nodejs-backend',
        title: 'Node.js Backend Development',
        description: 'Build scalable backend APIs with Node.js, Express, and MongoDB.',
        instructor: 'Ahmed Takal',
        level: 'intermediate',
        price: 119.99,
        originalPrice: 239.99,
        duration: 540,
        thumbnail: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=600&h=400&fit=crop',
        learningOutcomes: [
          'Build RESTful APIs with Node.js',
          'Implement authentication and authorization',
          'Work with databases and ORMs',
          'Deploy applications to production'
        ],
        tags: ['Node.js', 'Backend', 'API', 'MongoDB'],
        category: 'Backend Development',
        isPublished: true,
        featured: false,
        enrollmentCount: 800,
        rating: 4.7,
        reviewCount: 95
      }
    ];

    try {
      for (const course of sampleCourses) {
        const courseRef = doc(db, 'courses', course.id);
        await setDoc(courseRef, course);
        console.log(`✅ Created course: ${course.title}`);
      }
      
      setSuccess(`✅ Successfully created ${sampleCourses.length} sample courses!`);
      console.log('🎉 All sample courses created successfully!');
    } catch (err: any) {
      console.error('❌ Error creating courses:', err);
      setError(`Failed to create courses: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 py-12">
      <div className="container mx-auto px-4 max-w-2xl">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white text-center">
              Create Sample Courses
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {error && (
              <Alert className="bg-red-900/20 border-red-700">
                <AlertDescription className="text-red-300">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="bg-green-900/20 border-green-700">
                <AlertDescription className="text-green-300">
                  {success}
                </AlertDescription>
              </Alert>
            )}

            <div className="text-center">
              <p className="text-gray-300 mb-6">
                Click the button below to create 6 sample courses in Firestore.
                This will populate your course catalog with professional content.
              </p>

              <Button
                onClick={createSampleCourses}
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {loading ? 'Creating Courses...' : 'Create Sample Courses'}
              </Button>
            </div>

            <div className="text-sm text-gray-400">
              <p className="font-semibold mb-2">This will create:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Complete FlutterFlow Masterclass ($99.99)</li>
                <li>AI-Powered Coding Bootcamp ($149.99)</li>
                <li>Advanced React Development ($129.99)</li>
                <li>Python Automation Mastery ($89.99)</li>
                <li>UI/UX Design Fundamentals ($79.99)</li>
                <li>Node.js Backend Development ($119.99)</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CreateCourse;
