import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, RefreshCw, CheckCircle, XCircle, Clock, CreditCard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const RefundPolicy: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/')}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Refund Policy</h1>
              <p className="text-sm text-gray-600">Last updated: December 12, 2024</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="bg-green-50 border-b">
            <CardTitle className="flex items-center space-x-2 text-green-900">
              <RefreshCw className="w-6 h-6" />
              <span>FreeCodeLap Refund Policy</span>
            </CardTitle>
            <p className="text-green-700 mt-2">
              We stand behind our courses with a comprehensive 30-day money-back guarantee.
            </p>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* Money-Back Guarantee */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <CheckCircle className="w-6 h-6 text-green-600" />
                <span>30-Day Money-Back Guarantee</span>
              </h2>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-green-900 mb-3">Our Promise:</h3>
                    <p className="text-green-800 text-sm mb-3">
                      We're confident you'll love our courses. If you're not completely satisfied within 30 days 
                      of purchase, we'll refund your money - no questions asked.
                    </p>
                    <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                      <li>Full refund within 30 days</li>
                      <li>Simple refund process</li>
                      <li>No hidden conditions</li>
                      <li>Fast processing (5-7 business days)</li>
                    </ul>
                  </div>
                  <div className="bg-white p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-3">Refund Timeline:</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm font-bold">1</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Request Submitted</p>
                          <p className="text-xs text-gray-600">Within 30 days of purchase</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm font-bold">2</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Review & Approval</p>
                          <p className="text-xs text-gray-600">1-2 business days</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 text-sm font-bold">3</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Refund Processed</p>
                          <p className="text-xs text-gray-600">3-5 business days</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Eligibility Criteria */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Refund Eligibility Criteria</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 mb-3 flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5" />
                    <span>Eligible for Refund</span>
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-green-900 text-sm">Within 30 Days:</strong>
                        <p className="text-green-800 text-xs">Request made within 30 days of purchase date</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-green-900 text-sm">Limited Progress:</strong>
                        <p className="text-green-800 text-xs">Less than 30% of course content accessed</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-green-900 text-sm">Valid Reason:</strong>
                        <p className="text-green-800 text-xs">Technical issues, content quality, or dissatisfaction</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-green-900 text-sm">Account in Good Standing:</strong>
                        <p className="text-green-800 text-xs">No violations of terms of service</p>
                      </div>
                    </li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-medium text-red-900 mb-3 flex items-center space-x-2">
                    <XCircle className="w-5 h-5" />
                    <span>Not Eligible for Refund</span>
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-red-900 text-sm">After 30 Days:</strong>
                        <p className="text-red-800 text-xs">Request made more than 30 days after purchase</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-red-900 text-sm">High Progress:</strong>
                        <p className="text-red-800 text-xs">More than 30% of course content accessed</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-red-900 text-sm">Certificate Issued:</strong>
                        <p className="text-red-800 text-xs">Course completed and certificate downloaded</p>
                      </div>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2"></span>
                      <div>
                        <strong className="text-red-900 text-sm">Policy Violations:</strong>
                        <p className="text-red-800 text-xs">Account terminated for terms violations</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            {/* How to Request */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">How to Request a Refund</h2>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="font-medium text-blue-900 mb-4">Step-by-Step Process:</h3>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                        <div>
                          <h4 className="font-medium text-blue-900">Contact Support</h4>
                          <p className="text-blue-800 text-sm">Email <NAME_EMAIL> with "Refund Request" in the subject line</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                        <div>
                          <h4 className="font-medium text-blue-900">Provide Information</h4>
                          <p className="text-blue-800 text-sm">Include your order number, purchase date, and reason for refund</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                        <div>
                          <h4 className="font-medium text-blue-900">Wait for Review</h4>
                          <p className="text-blue-800 text-sm">We'll review your request within 1-2 business days</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-3">Required Information:</h4>
                      <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                        <li>Full name and email address</li>
                        <li>Course name and purchase date</li>
                        <li>Payment method used</li>
                        <li>Reason for refund request</li>
                        <li>Any technical issues experienced</li>
                      </ul>
                      <div className="mt-4 p-3 bg-gray-50 rounded">
                        <p className="text-gray-700 text-xs">
                          <strong>Tip:</strong> The more details you provide, the faster we can process your request.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Processing Times */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <span>Refund Processing Times</span>
              </h2>
              <div className="space-y-4">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 className="font-medium text-yellow-900 mb-2">Review Period</h3>
                    <p className="text-2xl font-bold text-yellow-600 mb-1">1-2 Days</p>
                    <p className="text-yellow-800 text-sm">
                      We review your request and verify eligibility criteria
                    </p>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">Processing Time</h3>
                    <p className="text-2xl font-bold text-blue-600 mb-1">3-5 Days</p>
                    <p className="text-blue-800 text-sm">
                      Refund processed and sent to your original payment method
                    </p>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-medium text-green-900 mb-2">Bank Processing</h3>
                    <p className="text-2xl font-bold text-green-600 mb-1">2-7 Days</p>
                    <p className="text-green-800 text-sm">
                      Your bank or payment provider processes the refund
                    </p>
                  </div>
                </div>
                
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Total Timeline: 6-14 Business Days</h3>
                  <p className="text-gray-700 text-sm">
                    From request submission to money appearing in your account. Processing times may vary 
                    depending on your payment method and bank.
                  </p>
                </div>
              </div>
            </section>

            {/* Payment Method Specific */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <CreditCard className="w-5 h-5 text-purple-600" />
                <span>Refund by Payment Method</span>
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-3">Credit/Debit Cards</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-blue-800">Refunded to original card</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-blue-800">Processing: 3-7 business days</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-blue-800">Full amount minus processing fees</span>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 mb-3">M-Pesa</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-800">Refunded to original M-Pesa number</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-800">Processing: 1-3 business days</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-800">SMS notification when complete</span>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h3 className="font-medium text-purple-900 mb-3">Bank Transfer</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-purple-800">Refunded to original bank account</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-purple-800">Processing: 2-5 business days</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-purple-800">Bank statement will show refund</span>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h3 className="font-medium text-orange-900 mb-3">Processing Fees</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      <span className="text-orange-800">Paystack processing fees are non-refundable</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      <span className="text-orange-800">Typical fee: 2.9% + KES 20 for cards</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      <span className="text-orange-800">M-Pesa fee: 1.5% of transaction</span>
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Special Circumstances */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Special Circumstances</h2>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 mb-2">Technical Issues</h3>
                  <p className="text-yellow-800 text-sm mb-2">
                    If you experience technical problems that prevent you from accessing course content:
                  </p>
                  <ul className="list-disc list-inside text-yellow-800 text-sm space-y-1">
                    <li>Contact support immediately for assistance</li>
                    <li>We'll work to resolve the issue first</li>
                    <li>Refund available if issue cannot be resolved</li>
                    <li>Extended refund period for technical issues</li>
                  </ul>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">Course Quality Issues</h3>
                  <p className="text-blue-800 text-sm mb-2">
                    If course content doesn't match the description or has quality issues:
                  </p>
                  <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                    <li>Full refund available regardless of progress</li>
                    <li>Detailed feedback helps us improve</li>
                    <li>Alternative course recommendations provided</li>
                    <li>Priority processing for quality-related refunds</li>
                  </ul>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 mb-2">Duplicate Purchases</h3>
                  <p className="text-green-800 text-sm mb-2">
                    Accidentally purchased the same course twice:
                  </p>
                  <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                    <li>Immediate full refund for duplicate purchase</li>
                    <li>No 30-day limit for duplicate purchases</li>
                    <li>Keep access to original purchase</li>
                    <li>Fast-track processing (1-2 days)</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Contact Information */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Contact for Refunds</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-blue-900 mb-3">Refund Support:</h3>
                    <div className="space-y-2 text-blue-800 text-sm">
                      <p><strong>Email:</strong> <EMAIL></p>
                      <p><strong>Subject Line:</strong> "Refund Request - [Course Name]"</p>
                      <p><strong>Phone:</strong> +254721962181</p>
                      <p><strong>Response Time:</strong> Within 24 hours</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-900 mb-3">Business Hours:</h3>
                    <div className="space-y-2 text-blue-800 text-sm">
                      <p><strong>Monday - Friday:</strong> 9:00 AM - 6:00 PM EAT</p>
                      <p><strong>Saturday:</strong> 10:00 AM - 4:00 PM EAT</p>
                      <p><strong>Sunday:</strong> Closed</p>
                      <p><strong>Holidays:</strong> Limited support</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4 p-4 bg-white rounded-lg">
                  <p className="text-blue-900 text-sm">
                    <strong>Emergency Support:</strong> For urgent payment or refund issues, call +254721962181 
                    and leave a detailed message. We'll respond within 4 hours during business days.
                  </p>
                </div>
              </div>
            </section>

            {/* Policy Updates */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Policy Updates</h2>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-4 rounded">
                <p className="text-blue-900 text-sm mb-2">
                  This refund policy may be updated to reflect changes in our services or legal requirements.
                </p>
                <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                  <li>Email notification of policy changes</li>
                  <li>30-day notice for material changes</li>
                  <li>Existing purchases honored under original terms</li>
                  <li>Updated policy applies to new purchases</li>
                </ul>
                <p className="text-blue-900 text-sm mt-2">
                  <strong>Last Updated:</strong> December 12, 2024
                </p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RefundPolicy;
