// Example Node.js/Express backend for Paystack integration
// This is a reference implementation - you'll need to set up your own backend

const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const cors = require('cors');

const app = express();

app.use(cors());
app.use(express.json());

// Paystack Configuration - SECURE: Using environment variables only
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

// Validate configuration
if (!PAYSTACK_SECRET_KEY) {
  console.error('🚨 CRITICAL: PAYSTACK_SECRET_KEY environment variable is missing!');
  console.error('Please set PAYSTACK_SECRET_KEY in your .env file');
  process.exit(1);
}

// Environment detection
const IS_LIVE_MODE = PAYSTACK_SECRET_KEY.startsWith('sk_live_');

console.log(`🔧 Paystack API Server starting in ${IS_LIVE_MODE ? 'LIVE' : 'TEST'} mode`);

if (IS_LIVE_MODE) {
  console.log('⚠️  LIVE MODE: Real payments will be processed!');
} else {
  console.log('🧪 TEST MODE: No real money will be charged');
}

// Initialize Paystack Transaction
app.post('/api/paystack/initialize', async (req, res) => {
  try {
    const { email, amount, currency, metadata, channels } = req.body;

    const response = await axios.post(
      `${PAYSTACK_BASE_URL}/transaction/initialize`,
      {
        email,
        amount: amount * 100, // Convert to kobo/cents
        currency: currency.toUpperCase(),
        metadata,
        channels,
        callback_url: `${req.protocol}://${req.get('host')}/api/paystack/callback`,
      },
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('Error initializing Paystack transaction:', error);
    res.status(400).json({ 
      error: error.response?.data?.message || error.message 
    });
  }
});

// Verify Paystack Transaction
app.get('/api/paystack/verify/:reference', async (req, res) => {
  try {
    const { reference } = req.params;

    const response = await axios.get(
      `${PAYSTACK_BASE_URL}/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('Error verifying Paystack transaction:', error);
    res.status(400).json({ 
      error: error.response?.data?.message || error.message 
    });
  }
});

// Paystack Webhook Handler
app.post('/api/paystack/webhook', (req, res) => {
  const hash = crypto.createHmac('sha512', process.env.PAYSTACK_SECRET_KEY).update(JSON.stringify(req.body)).digest('hex');
  
  if (hash === req.headers['x-paystack-signature']) {
    const event = req.body;
    
    // Handle the event
    switch (event.event) {
      case 'charge.success':
        const transaction = event.data;
        console.log('Payment succeeded:', transaction.reference);
        
        // TODO: Enroll user in course
        // TODO: Send confirmation email
        // TODO: Update database
        
        break;
      case 'charge.failed':
        const failedTransaction = event.data;
        console.log('Payment failed:', failedTransaction.reference);
        
        // TODO: Handle failed payment
        
        break;
      default:
        console.log(`Unhandled event type ${event.event}`);
    }
  }

  res.status(200).send('OK');
});

// Paystack Callback Handler
app.get('/api/paystack/callback', (req, res) => {
  const { reference, trxref } = req.query;
  
  // Redirect to frontend with payment reference
  res.redirect(`${process.env.FRONTEND_URL}/payment/callback?reference=${reference || trxref}`);
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Paystack API server running on port ${PORT}`);
});

module.exports = app;
