import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/contexts/AuthContext";
import { CourseProvider } from "@/contexts/CourseContext";

import { SimpleCurrencyProvider } from "@/contexts/SimpleCurrencyContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import Dashboard from "./pages/Dashboard";
import AdminPanel from "./pages/AdminPanel";
import CourseManagement from "./components/admin/CourseManagement";


import ComprehensiveCourseForm from "./pages/ComprehensiveCourseForm";

import CourseDetail from "./pages/CourseDetail";


import LiveClasses from "./pages/LiveClasses";
import AllCourses from "./pages/AllCourses";
import NotFound from "./pages/NotFound";
import CheckoutPage from "./pages/CheckoutPage";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import CookiePolicy from "./pages/CookiePolicy";
import RefundPolicy from "./pages/RefundPolicy";
import Profile from "./pages/Profile";
import CurrencyTestPage from "./pages/CurrencyTestPage";
import MyCourses from "./pages/MyCourses";

import Settings from "./pages/Settings";
import CourseContent from "./pages/CourseContent";
import CreateCourse from "./pages/CreateCourse";

import CookieConsent from "./components/legal/CookieConsent";
import { ErrorBoundary } from "./components/ErrorBoundary";


const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <SimpleCurrencyProvider>
          <CourseProvider>
              <TooltipProvider>
              <BrowserRouter>

                <Toaster />
                <Sonner />
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/forgot-password" element={<ForgotPassword />} />
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/course/:courseId"
                    element={<CourseDetail />}
                  />


                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminPanel />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/panel"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminPanel />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/course-form"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <ComprehensiveCourseForm />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/courses/edit/:courseId"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <ComprehensiveCourseForm />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/courses"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <CourseManagement />
                      </ProtectedRoute>
                    }
                  />






                  <Route
                    path="/live-classes"
                    element={<LiveClasses />}
                  />
                  <Route
                    path="/courses"
                    element={<AllCourses />}
                  />
                  <Route
                    path="/course/:courseId"
                    element={<CourseDetail />}
                  />
                  <Route
                    path="/course/:courseId/content"
                    element={
                      <ProtectedRoute>
                        <CourseContent />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/checkout/:courseId"
                    element={
                      <ProtectedRoute>
                        <CheckoutPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* User Dashboard Pages */}
                  <Route
                    path="/profile"
                    element={
                      <ProtectedRoute>
                        <Profile />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/my-courses"
                    element={
                      <ProtectedRoute>
                        <MyCourses />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/settings"
                    element={
                      <ProtectedRoute>
                        <Settings />
                      </ProtectedRoute>
                    }
                  />


                  {/* Test Pages */}
                  <Route path="/currency-test" element={<CurrencyTestPage />} />
                  <Route path="/create-courses" element={<CreateCourse />} />

                  {/* Legal Pages */}
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  <Route path="/terms-of-service" element={<TermsOfService />} />
                  <Route path="/cookie-policy" element={<CookiePolicy />} />
                  <Route path="/refund-policy" element={<RefundPolicy />} />

                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>

                {/* Cookie Consent Banner */}
                <CookieConsent />

              </BrowserRouter>
              </TooltipProvider>
          </CourseProvider>
        </SimpleCurrencyProvider>
      </AuthProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
