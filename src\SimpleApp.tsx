import React from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";

// Simple pages without Firebase/Payment dependencies
import SimpleIndex from "./pages/SimpleIndex";
import SimpleAllCourses from "./pages/SimpleAllCourses";

const SimpleApp = () => (
  <TooltipProvider>
    <Toaster />
    <Sonner />
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<SimpleIndex />} />
        <Route path="/courses" element={<SimpleAllCourses />} />
        <Route path="*" element={<SimpleIndex />} />
      </Routes>
    </BrowserRouter>
  </TooltipProvider>
);

export default SimpleApp;
