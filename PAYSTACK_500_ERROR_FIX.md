# 🚨 Paystack 500 Internal Server Error Fix

## Error Description
```
Failed to load resource: the server responded with a status of 500 (Internal Server Error)
js.paystack.co/:1 Refused to frame 'https://checkout.paystack.com/' because it violates the following Content Security Policy directive
```

## 🔍 Root Causes

### 1. Content Security Policy (CSP) Issues
The CSP is blocking Paystack's iframe from loading properly.

### 2. Network/Connectivity Issues
- Slow internet connection
- DNS resolution problems
- Firewall blocking Paystack domains

### 3. Invalid API Keys
- Using wrong environment keys
- Expired or revoked keys
- Malformed key format

## ✅ Solutions Applied

### 1. Updated CSP Headers
Fixed the Content Security Policy in `index.html`:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self'; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.paystack.co https://fonts.googleapis.com https://apis.google.com https://www.gstatic.com https://gstatic.com; 
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://paystack.com; 
  font-src 'self' https://fonts.gstatic.com; 
  img-src 'self' data: https: https://www.google.com https://accounts.google.com https://paystack.com https://checkout.paystack.co; 
  connect-src 'self' https://api.paystack.co https://standard.paystack.co https://checkout.paystack.co https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://accounts.google.com; 
  frame-src 'self' https://js.paystack.co https://checkout.paystack.co https://standard.paystack.co https://paystack.com https://accounts.google.com; 
  child-src 'self' https://js.paystack.co https://checkout.paystack.co https://standard.paystack.co https://paystack.com;
">
```

### 2. Enhanced Error Handling
Updated `PaystackCheckout.tsx` with:
- Better script loading
- Timeout protection
- Detailed error logging
- Graceful fallbacks

### 3. Added Debug Tools
Created `PaystackTest.tsx` component for:
- Real-time diagnostics
- CSP validation
- API key verification
- Payment flow testing

## 🛠️ Immediate Actions

### 1. Clear Browser Cache
```bash
# Hard refresh
Ctrl+Shift+R (Windows/Linux)
Cmd+Shift+R (Mac)

# Or clear all data
Chrome DevTools → Application → Storage → Clear storage
```

### 2. Check Environment Variables
Verify your `.env` file:

```env
# For development
VITE_PAYSTACK_PUBLIC_KEY=pk_test_89ae2fc67b744d6b6fe3be4e2354bd7c287b8515

# For production
VITE_PAYSTACK_PUBLIC_KEY=pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3
```

### 3. Test Network Connectivity
```bash
# Test Paystack domains
ping js.paystack.co
ping checkout.paystack.co
ping api.paystack.co
```

### 4. Use Debug Panel
1. Go to Admin Panel → Debug tab
2. Run diagnostics
3. Check all tests pass
4. Try test payment

## 🔧 Alternative Solutions

### If CSP Still Blocks
Try this temporary permissive CSP for testing:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self' https:; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; 
  style-src 'self' 'unsafe-inline' https:; 
  img-src 'self' data: https:; 
  connect-src 'self' https:; 
  frame-src 'self' https:;
">
```

⚠️ **Warning**: Only use for testing, then revert to secure CSP.

### If Script Loading Fails
Add fallback script loading:

```html
<!-- In index.html -->
<script>
  window.addEventListener('load', function() {
    if (!window.PaystackPop) {
      console.warn('Paystack failed to load, retrying...');
      var script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      document.head.appendChild(script);
    }
  });
</script>
```

### If API Keys Invalid
1. Login to Paystack Dashboard
2. Go to Settings → API Keys
3. Generate new keys
4. Update environment variables
5. Restart development server

## 🔍 Debugging Steps

### 1. Check Console Errors
Open DevTools (F12) and look for:
- CSP violation errors
- Network request failures
- JavaScript errors
- Paystack loading issues

### 2. Network Tab Analysis
1. Open DevTools → Network
2. Try to make payment
3. Look for failed requests
4. Check response codes and errors

### 3. Test Different Browsers
- Chrome (latest)
- Firefox (latest)
- Safari (if on Mac)
- Edge (if on Windows)

### 4. Test Different Networks
- Try different WiFi
- Use mobile hotspot
- Test from different location

## 📞 Support Resources

### Paystack Support
- **Status Page**: https://status.paystack.com
- **Documentation**: https://paystack.com/docs
- **Support Email**: <EMAIL>
- **Developer Slack**: paystack-developers.slack.com

### Common Issues
1. **Invalid API Key**: Check key format and environment
2. **CSP Blocking**: Update frame-src directive
3. **Network Issues**: Check firewall and DNS
4. **Browser Issues**: Clear cache and disable extensions

## ✅ Success Indicators

After applying fixes, you should see:
- [ ] No CSP violations in console
- [ ] Paystack script loads successfully
- [ ] Payment iframe opens without errors
- [ ] Test transactions complete
- [ ] No 500 errors in network tab

## 🚀 Next Steps

1. **Test thoroughly** in development
2. **Deploy to staging** environment
3. **Test with real cards** (small amounts)
4. **Monitor error logs** in production
5. **Set up alerts** for payment failures

Remember: Always test payments thoroughly before going live!
