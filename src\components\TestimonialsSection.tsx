
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star } from 'lucide-react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Mobile App Developer",
      company: "Tech Startup, Nairobi",
      image: "/placeholder.svg",
      rating: 5,
      text: "This course transformed my career! I went from knowing nothing about app development to building and publishing 3 apps in just 2 months. The instructor's approach is practical and easy to follow."
    },
    {
      name: "<PERSON>",
      role: "Freelance Developer",
      company: "Remote",
      image: "/placeholder.svg",
      rating: 5,
      text: "The live classes are amazing! Being able to ask questions in real-time and get immediate feedback made all the difference. I now earn $2000+ monthly from FlutterFlow projects."
    },
    {
      name: "<PERSON>",
      role: "Student",
      company: "University of Nairobi",
      image: "/placeholder.svg",
      rating: 5,
      text: "As a computer science student, this course gave me practical skills that my university couldn't. The Firebase and AI integration modules were particularly valuable."
    },
    {
      name: "<PERSON>",
      role: "Business Owner",
      company: "E-commerce",
      image: "/placeholder.svg",
      rating: 5,
      text: "I needed an app for my business but couldn't afford expensive developers. This course helped me build exactly what I needed. The payment integration tutorials were perfect!"
    },
    {
      name: "<PERSON>pchoge",
      role: "Software Engineer",
      company: "Safaricom",
      image: "/placeholder.svg",
      rating: 5,
      text: "Even as an experienced developer, I learned so much about FlutterFlow's advanced features. The custom widget creation section was incredibly detailed and useful."
    },
    {
      name: "Mercy Akinyi",
      role: "Entrepreneur",
      company: "Startup Founder",
      image: "/placeholder.svg",
      rating: 5,
      text: "Built my MVP in just 3 weeks using what I learned here. The course paid for itself when I got my first client project worth $5000. Highly recommend!"
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: rating }, (_, i) => (
      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
    ));
  };

  return (
    <section id="testimonials" className="py-20 section-dark">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl font-bold text-white mb-4 gradient-text">
            What Our Students Say
          </h2>
          <p className="text-xl text-blue-200 max-w-2xl mx-auto">
            Join hundreds of successful developers who've transformed their careers with our FlutterFlow courses
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="card-elevated card-bounce shadow-2xl hover:shadow-3xl transition-all duration-500 border-0 animate-delay-200">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {renderStars(testimonial.rating)}
                </div>
                <p className="text-blue-200 mb-6 leading-relaxed">"{testimonial.text}"</p>
                <div className="flex items-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4 object-cover animate-heartbeat"
                  />
                  <div>
                    <h4 className="font-semibold text-white">{testimonial.name}</h4>
                    <p className="text-sm text-blue-300">{testimonial.role}</p>
                    <p className="text-xs text-blue-400">{testimonial.company}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-to-r from-blue-600 to-pink-600 rounded-2xl p-8 text-white text-center animate-bounce-in">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold mb-4">Ready to Join Our Success Stories?</h3>
            <p className="text-lg text-blue-100 mb-6">
              Don't just learn FlutterFlow – master it and build apps that make a real impact
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="glass-effect rounded-lg p-4 animate-scale-in animate-delay-100">
                <div className="text-2xl font-bold">4.9/5</div>
                <div className="text-sm text-blue-100">Average Rating</div>
              </div>
              <div className="glass-effect rounded-lg p-4 animate-scale-in animate-delay-200">
                <div className="text-2xl font-bold">500+</div>
                <div className="text-sm text-blue-100">Happy Students</div>
              </div>
              <div className="glass-effect rounded-lg p-4 animate-scale-in animate-delay-300">
                <div className="text-2xl font-bold">1000+</div>
                <div className="text-sm text-blue-100">Apps Built</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
