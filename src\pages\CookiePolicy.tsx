import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>t, <PERSON><PERSON>, Set<PERSON><PERSON>, <PERSON>, <PERSON>C<PERSON> } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const CookiePolicy: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/')}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900"><PERSON><PERSON></h1>
              <p className="text-sm text-gray-600">Last updated: December 12, 2024</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="bg-orange-50 border-b">
            <CardTitle className="flex items-center space-x-2 text-orange-900">
              <Cookie className="w-6 h-6" />
              <span>FreeCodeLap Cookie Policy</span>
            </CardTitle>
            <p className="text-orange-700 mt-2">
              Learn how we use cookies and similar technologies to enhance your learning experience.
            </p>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* What are Cookies */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">1. What Are Cookies?</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-900 text-sm mb-3">
                  Cookies are small text files stored on your device when you visit our website. They help us provide 
                  you with a better, faster, and more personalized experience.
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">Types of Data Stored:</h3>
                    <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                      <li>Login status and preferences</li>
                      <li>Course progress and bookmarks</li>
                      <li>Language and display settings</li>
                      <li>Shopping cart contents</li>
                      <li>Analytics and usage patterns</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-900 mb-2">How They Work:</h3>
                    <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                      <li>Stored locally on your device</li>
                      <li>Sent back to our servers on visits</li>
                      <li>Help recognize returning users</li>
                      <li>Enable personalized features</li>
                      <li>Improve platform performance</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Types of Cookies */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Types of Cookies We Use</h2>
              <div className="space-y-4">
                {/* Essential Cookies */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Settings className="w-5 h-5 text-red-600" />
                    <h3 className="font-medium text-red-900">Essential Cookies (Required)</h3>
                  </div>
                  <p className="text-red-800 text-sm mb-3">
                    These cookies are necessary for the website to function properly. They cannot be disabled.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-red-900 text-sm mb-1">Purpose:</h4>
                      <ul className="list-disc list-inside text-red-800 text-xs space-y-1">
                        <li>User authentication and login</li>
                        <li>Security and fraud prevention</li>
                        <li>Shopping cart functionality</li>
                        <li>Form submission and validation</li>
                        <li>Load balancing and performance</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-red-900 text-sm mb-1">Examples:</h4>
                      <ul className="list-disc list-inside text-red-800 text-xs space-y-1">
                        <li>Session ID cookies</li>
                        <li>Authentication tokens</li>
                        <li>CSRF protection tokens</li>
                        <li>Load balancer cookies</li>
                        <li>Cookie consent status</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Analytics Cookies */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <BarChart className="w-5 h-5 text-green-600" />
                    <h3 className="font-medium text-green-900">Analytics Cookies (Optional)</h3>
                  </div>
                  <p className="text-green-800 text-sm mb-3">
                    Help us understand how you use our platform to improve your experience.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-green-900 text-sm mb-1">What We Track:</h4>
                      <ul className="list-disc list-inside text-green-800 text-xs space-y-1">
                        <li>Pages visited and time spent</li>
                        <li>Course completion rates</li>
                        <li>Popular content and features</li>
                        <li>User journey and navigation</li>
                        <li>Device and browser information</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-green-900 text-sm mb-1">Benefits:</h4>
                      <ul className="list-disc list-inside text-green-800 text-xs space-y-1">
                        <li>Improve course content quality</li>
                        <li>Optimize platform performance</li>
                        <li>Identify technical issues</li>
                        <li>Enhance user interface design</li>
                        <li>Develop new features</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Preference Cookies */}
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Eye className="w-5 h-5 text-purple-600" />
                    <h3 className="font-medium text-purple-900">Preference Cookies (Optional)</h3>
                  </div>
                  <p className="text-purple-800 text-sm mb-3">
                    Remember your settings and preferences for a personalized experience.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-purple-900 text-sm mb-1">Settings Saved:</h4>
                      <ul className="list-disc list-inside text-purple-800 text-xs space-y-1">
                        <li>Language and region preferences</li>
                        <li>Theme and display settings</li>
                        <li>Video quality preferences</li>
                        <li>Notification preferences</li>
                        <li>Course sorting and filters</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-purple-900 text-sm mb-1">User Benefits:</h4>
                      <ul className="list-disc list-inside text-purple-800 text-xs space-y-1">
                        <li>Consistent experience across visits</li>
                        <li>No need to reset preferences</li>
                        <li>Faster navigation and loading</li>
                        <li>Personalized content recommendations</li>
                        <li>Improved accessibility options</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Marketing Cookies */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Cookie className="w-5 h-5 text-yellow-600" />
                    <h3 className="font-medium text-yellow-900">Marketing Cookies (Optional)</h3>
                  </div>
                  <p className="text-yellow-800 text-sm mb-3">
                    Currently not used. We may implement these in the future for targeted advertising.
                  </p>
                  <div className="text-yellow-800 text-xs">
                    <strong>Future Use:</strong> If implemented, these would help show relevant course recommendations 
                    and promotional content based on your interests and learning history.
                  </div>
                </div>
              </div>
            </section>

            {/* Third-Party Cookies */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">3. Third-Party Services</h2>
              <div className="space-y-4">
                <p className="text-gray-700">
                  We use trusted third-party services that may set their own cookies:
                </p>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Firebase (Google)</h3>
                    <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                      <li>User authentication and login</li>
                      <li>Database and storage services</li>
                      <li>Performance monitoring</li>
                      <li>Crash reporting and analytics</li>
                    </ul>
                    <p className="text-blue-700 text-xs mt-2">
                      <strong>Privacy Policy:</strong> <a href="https://policies.google.com/privacy" className="underline">Google Privacy Policy</a>
                    </p>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium text-green-900 mb-2">Paystack</h3>
                    <ul className="list-disc list-inside text-green-800 text-sm space-y-1">
                      <li>Secure payment processing</li>
                      <li>Fraud detection and prevention</li>
                      <li>Transaction analytics</li>
                      <li>Payment method preferences</li>
                    </ul>
                    <p className="text-green-700 text-xs mt-2">
                      <strong>Privacy Policy:</strong> <a href="https://paystack.com/privacy" className="underline">Paystack Privacy Policy</a>
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Cookie Management */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">4. Managing Your Cookie Preferences</h2>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">Cookie Consent Banner</h3>
                  <p className="text-blue-800 text-sm mb-3">
                    When you first visit our site, you'll see a cookie consent banner where you can:
                  </p>
                  <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                    <li>Accept all cookies for the best experience</li>
                    <li>Reject optional cookies (analytics, preferences)</li>
                    <li>Customize your cookie preferences</li>
                    <li>Learn more about each cookie type</li>
                  </ul>
                </div>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Browser Settings</h3>
                    <p className="text-gray-700 text-sm mb-2">
                      You can also manage cookies through your browser:
                    </p>
                    <ul className="list-disc list-inside text-gray-700 text-xs space-y-1">
                      <li>Block all cookies (may break functionality)</li>
                      <li>Delete existing cookies</li>
                      <li>Set cookie preferences per website</li>
                      <li>Enable/disable third-party cookies</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">Account Settings</h3>
                    <p className="text-gray-700 text-sm mb-2">
                      Logged-in users can manage preferences:
                    </p>
                    <ul className="list-disc list-inside text-gray-700 text-xs space-y-1">
                      <li>Analytics data collection</li>
                      <li>Personalization features</li>
                      <li>Email and notification preferences</li>
                      <li>Data sharing settings</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Cookie Lifespan */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">5. Cookie Duration and Storage</h2>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium text-green-900 mb-2">Session Cookies</h3>
                    <p className="text-green-800 text-sm mb-2">
                      Temporary cookies deleted when you close your browser:
                    </p>
                    <ul className="list-disc list-inside text-green-800 text-xs space-y-1">
                      <li>Login session management</li>
                      <li>Shopping cart contents</li>
                      <li>Form data and navigation</li>
                      <li>Security tokens</li>
                    </ul>
                  </div>
                  
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Persistent Cookies</h3>
                    <p className="text-blue-800 text-sm mb-2">
                      Stored for specific periods (up to 2 years):
                    </p>
                    <ul className="list-disc list-inside text-blue-800 text-xs space-y-1">
                      <li>Remember login status (30 days)</li>
                      <li>User preferences (1 year)</li>
                      <li>Analytics data (2 years)</li>
                      <li>Course progress (permanent)</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 mb-2">Automatic Cleanup</h3>
                  <p className="text-yellow-800 text-sm">
                    We automatically clean up expired cookies and unused data. You can also manually clear 
                    cookies through your browser settings or by logging out of your account.
                  </p>
                </div>
              </div>
            </section>

            {/* Impact of Disabling */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">6. Impact of Disabling Cookies</h2>
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-medium text-red-900 mb-2">Essential Cookies Disabled:</h3>
                  <ul className="list-disc list-inside text-red-800 text-sm space-y-1">
                    <li>Cannot log in or maintain login sessions</li>
                    <li>Shopping cart will not work</li>
                    <li>Forms may not submit properly</li>
                    <li>Security features may be compromised</li>
                    <li>Course progress may not be saved</li>
                  </ul>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 mb-2">Optional Cookies Disabled:</h3>
                  <ul className="list-disc list-inside text-yellow-800 text-sm space-y-1">
                    <li>Less personalized experience</li>
                    <li>Settings reset on each visit</li>
                    <li>No usage analytics for improvements</li>
                    <li>Generic content recommendations</li>
                    <li>May need to reconfigure preferences</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Updates */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">7. Policy Updates</h2>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-4 rounded">
                <p className="text-blue-900 text-sm mb-2">
                  We may update this Cookie Policy to reflect changes in our practices or legal requirements.
                </p>
                <ul className="list-disc list-inside text-blue-800 text-sm space-y-1">
                  <li>Notice of material changes via email</li>
                  <li>Updated consent banners for new cookies</li>
                  <li>Opportunity to review and update preferences</li>
                  <li>Continued use implies acceptance</li>
                </ul>
                <p className="text-blue-900 text-sm mt-2">
                  <strong>Last Updated:</strong> December 12, 2024
                </p>
              </div>
            </section>

            {/* Contact */}
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">8. Contact Us</h2>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <p className="text-gray-900 mb-4">
                  Questions about our cookie practices? Contact us:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Support Contact:</h3>
                    <p className="text-gray-700 text-sm">
                      Email: <EMAIL><br />
                      Phone: +254721962181<br />
                      Response Time: Within 24 hours
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Data Protection:</h3>
                    <p className="text-gray-700 text-sm">
                      For privacy and data protection inquiries,<br />
                      please use the same contact information<br />
                      with "Privacy" in the subject line.
                    </p>
                  </div>
                </div>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CookiePolicy;
