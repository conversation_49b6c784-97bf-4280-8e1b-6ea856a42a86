import React, { createContext, useContext, useState, useEffect } from 'react';
import { Course, UserProgress, VideoProgress, QuizAttempt, AssignmentSubmission } from '@/types/course';
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';

interface CourseContextType {
  // Course data
  currentCourse: Course | null;
  userProgress: UserProgress | null;
  loading: boolean;
  
  // Course navigation
  loadCourse: (courseId: string) => Promise<void>;
  getCurrentLesson: () => any;
  getNextLesson: () => any;
  getPreviousLesson: () => any;
  navigateToLesson: (lessonId: string) => void;
  
  // Progress tracking
  updateLessonProgress: (lessonId: string, watchTime: number, completed: boolean) => Promise<void>;
  updateVideoProgress: (lessonId: string, progress: Partial<VideoProgress>) => Promise<void>;
  markLessonComplete: (lessonId: string) => Promise<void>;
  markAssignmentComplete: (assignmentId: string) => Promise<void>;
  markQuizComplete: (quizId: string, attempt: QuizAttempt) => Promise<void>;
  
  // Submissions
  submitAssignment: (assignmentId: string, submission: Partial<AssignmentSubmission>) => Promise<void>;
  submitQuizAttempt: (quizId: string, attempt: Partial<QuizAttempt>) => Promise<QuizAttempt>;
  
  // State
  currentLessonId: string | null;
  currentModuleId: string | null;
  videoProgress: Record<string, VideoProgress>;
  quizAttempts: Record<string, QuizAttempt[]>;
  assignmentSubmissions: Record<string, AssignmentSubmission[]>;
}

const CourseContext = createContext<CourseContextType | undefined>(undefined);

export function useCourse() {
  const context = useContext(CourseContext);
  if (context === undefined) {
    console.error('useCourse must be used within a CourseProvider');
    // Return a default context instead of throwing to prevent blank page
    return {
      currentCourse: null,
      userProgress: null,
      loading: false,
      loadCourse: async () => {},
      getCurrentLesson: () => null,
      getNextLesson: () => null,
      getPreviousLesson: () => null,
      navigateToLesson: () => {},
      updateLessonProgress: async () => {},
      updateVideoProgress: async () => {},
      markLessonComplete: async () => {},
      markAssignmentComplete: async () => {},
      markQuizComplete: async () => {},
      submitAssignment: async () => {},
      submitQuizAttempt: async () => ({ id: '', quizId: '', userId: '', answers: [], score: 0, maxScore: 0, percentage: 0, startedAt: new Date(), timeSpent: 0, passed: false, attemptNumber: 1 }),
      currentLessonId: null,
      currentModuleId: null,
      videoProgress: {},
      quizAttempts: {},
      assignmentSubmissions: {}
    } as CourseContextType;
  }
  return context;
}

export function CourseProvider({ children }: { children: React.ReactNode }) {
  const { currentUser } = useAuth();
  const [currentCourse, setCurrentCourse] = useState<Course | null>(null);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentLessonId, setCurrentLessonId] = useState<string | null>(null);
  const [currentModuleId, setCurrentModuleId] = useState<string | null>(null);
  const [videoProgress, setVideoProgress] = useState<Record<string, VideoProgress>>({});
  const [quizAttempts, setQuizAttempts] = useState<Record<string, QuizAttempt[]>>({});
  const [assignmentSubmissions, setAssignmentSubmissions] = useState<Record<string, AssignmentSubmission[]>>({});

  // Load course data
  const loadCourse = async (courseId: string) => {
    console.log('CourseContext: Starting to load course:', courseId);
    setLoading(true);

    try {
      // Add a small delay to see the loading state
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load course from Firestore
      const course = await courseService.getCourseWithModules(courseId);
      console.log('CourseContext: Loaded course data from Firestore:', course);

      if (course) {
        setCurrentCourse(course);
        console.log('CourseContext: Set current course:', course.title);

        // Load user progress (only if enrolled)
        if (currentUser) {
          const progress = await loadUserProgress(courseId, currentUser.uid);
          setUserProgress(progress);
          console.log('CourseContext: Loaded user progress:', progress);

          // Set current lesson/module from progress
          if (progress?.currentLessonId) {
            setCurrentLessonId(progress.currentLessonId);
            console.log('CourseContext: Set current lesson from progress:', progress.currentLessonId);
          } else if (course.modules.length > 0 && course.modules[0].lessons.length > 0) {
            // Set first free lesson as current if no progress
            const firstModule = course.modules[0];
            const firstFreeLesson = firstModule.lessons.find(lesson => lesson.isFree);
            if (firstFreeLesson) {
              setCurrentLessonId(firstFreeLesson.id);
              setCurrentModuleId(firstModule.id);
              console.log('CourseContext: Set first free lesson as current:', firstFreeLesson.title);
            }
          }

          if (progress?.currentModuleId) {
            setCurrentModuleId(progress.currentModuleId);
          }
        } else {
          console.log('CourseContext: No current user, skipping progress load');
        }
      } else {
        console.error('CourseContext: Course not found for ID:', courseId);
        setCurrentCourse(null);
      }
    } catch (error) {
      console.error('CourseContext: Error loading course:', error);
      setCurrentCourse(null);
    } finally {
      setLoading(false);
      console.log('CourseContext: Finished loading course');
    }
  };

  // Load user progress (only if user is actually enrolled)
  const loadUserProgress = async (courseId: string, userId: string): Promise<UserProgress | null> => {
    // In a real app, this would fetch from your backend
    const existingProgress = localStorage.getItem(`progress_${userId}_${courseId}`);

    if (existingProgress) {
      try {
        const progress = JSON.parse(existingProgress);
        // Only return progress if it has an enrolledAt timestamp (indicating actual enrollment)
        if (progress.enrolledAt && progress.userId === userId && progress.courseId === courseId) {
          return progress;
        }
      } catch (error) {
        console.error('Error parsing progress data:', error);
      }
    }

    // Don't create progress automatically - only create when user actually enrolls
    return null;
  };

  // Get current lesson
  const getCurrentLesson = () => {
    if (!currentCourse || !currentLessonId) return null;
    
    for (const module of currentCourse.modules) {
      const lesson = module.lessons.find(l => l.id === currentLessonId);
      if (lesson) return lesson;
    }
    return null;
  };

  // Get next lesson
  const getNextLesson = () => {
    if (!currentCourse || !currentLessonId) return null;
    
    let foundCurrent = false;
    for (const module of currentCourse.modules) {
      for (const lesson of module.lessons) {
        if (foundCurrent) return lesson;
        if (lesson.id === currentLessonId) foundCurrent = true;
      }
    }
    return null;
  };

  // Get previous lesson
  const getPreviousLesson = () => {
    if (!currentCourse || !currentLessonId) return null;
    
    let previousLesson = null;
    for (const module of currentCourse.modules) {
      for (const lesson of module.lessons) {
        if (lesson.id === currentLessonId) return previousLesson;
        previousLesson = lesson;
      }
    }
    return null;
  };

  // Navigate to lesson
  const navigateToLesson = (lessonId: string) => {
    setCurrentLessonId(lessonId);

    // Find and set current module
    if (currentCourse) {
      for (const module of currentCourse.modules) {
        if (module.lessons.some(l => l.id === lessonId)) {
          setCurrentModuleId(module.id);
          break;
        }
      }
    }

    // Update user progress (only for enrolled users)
    if (userProgress && currentUser) {
      const updatedProgress = {
        ...userProgress,
        currentLessonId: lessonId,
        currentModuleId: currentModuleId,
        lastAccessedAt: new Date()
      };
      setUserProgress(updatedProgress);

      // Save to localStorage (in real app, save to backend)
      localStorage.setItem(
        `progress_${currentUser.uid}_${userProgress.courseId}`,
        JSON.stringify(updatedProgress)
      );
    }
  };

  // Update lesson progress (only for enrolled users)
  const updateLessonProgress = async (lessonId: string, watchTime: number, completed: boolean) => {
    if (!userProgress || !currentUser) {
      console.log('CourseContext: Cannot update progress - user not enrolled or no progress data');
      return;
    }

    const updatedProgress = { ...userProgress };
    updatedProgress.totalWatchTime += watchTime;
    updatedProgress.lastAccessedAt = new Date();

    if (completed && !updatedProgress.completedLessons.includes(lessonId)) {
      updatedProgress.completedLessons.push(lessonId);

      // Recalculate completion percentage
      const totalLessons = currentCourse?.modules.reduce((total, module) =>
        total + module.lessons.length, 0) || 0;
      updatedProgress.completionPercentage =
        (updatedProgress.completedLessons.length / totalLessons) * 100;
    }

    setUserProgress(updatedProgress);
    localStorage.setItem(
      `progress_${currentUser.uid}_${userProgress.courseId}`,
      JSON.stringify(updatedProgress)
    );
  };

  // Update video progress
  const updateVideoProgress = async (lessonId: string, progress: Partial<VideoProgress>) => {
    if (!currentUser) return;
    
    const existing = videoProgress[lessonId] || {
      lessonId,
      userId: currentUser.uid,
      currentTime: 0,
      duration: 0,
      watchedSegments: [],
      completionPercentage: 0,
      lastWatchedAt: new Date(),
      playbackSpeed: 1,
      bookmarks: []
    };
    
    const updated = { ...existing, ...progress, lastWatchedAt: new Date() };
    
    setVideoProgress(prev => ({
      ...prev,
      [lessonId]: updated
    }));
    
    // Save to localStorage
    localStorage.setItem(
      `videoProgress_${currentUser.uid}_${lessonId}`,
      JSON.stringify(updated)
    );
  };

  // Mark lesson complete
  const markLessonComplete = async (lessonId: string) => {
    await updateLessonProgress(lessonId, 0, true);
  };

  // Mark assignment complete
  const markAssignmentComplete = async (assignmentId: string) => {
    if (!userProgress || !currentUser) return;
    
    if (!userProgress.completedAssignments.includes(assignmentId)) {
      const updatedProgress = {
        ...userProgress,
        completedAssignments: [...userProgress.completedAssignments, assignmentId],
        lastAccessedAt: new Date()
      };
      
      setUserProgress(updatedProgress);
      localStorage.setItem(
        `progress_${currentUser.uid}_${userProgress.courseId}`,
        JSON.stringify(updatedProgress)
      );
    }
  };

  // Mark quiz complete
  const markQuizComplete = async (quizId: string, attempt: QuizAttempt) => {
    if (!userProgress || !currentUser) return;
    
    if (!userProgress.completedQuizzes.includes(quizId) && attempt.passed) {
      const updatedProgress = {
        ...userProgress,
        completedQuizzes: [...userProgress.completedQuizzes, quizId],
        lastAccessedAt: new Date()
      };
      
      setUserProgress(updatedProgress);
      localStorage.setItem(
        `progress_${currentUser.uid}_${userProgress.courseId}`,
        JSON.stringify(updatedProgress)
      );
    }
  };

  // Submit assignment
  const submitAssignment = async (assignmentId: string, submission: Partial<AssignmentSubmission>) => {
    if (!currentUser) return;
    
    const newSubmission: AssignmentSubmission = {
      id: `submission_${Date.now()}`,
      assignmentId,
      userId: currentUser.uid,
      content: submission.content || '',
      files: submission.files || [],
      submittedAt: new Date(),
      status: 'submitted'
    };
    
    setAssignmentSubmissions(prev => ({
      ...prev,
      [assignmentId]: [...(prev[assignmentId] || []), newSubmission]
    }));
    
    // Save to localStorage
    localStorage.setItem(
      `submissions_${currentUser.uid}_${assignmentId}`,
      JSON.stringify([...(assignmentSubmissions[assignmentId] || []), newSubmission])
    );
    
    await markAssignmentComplete(assignmentId);
  };

  // Submit quiz attempt
  const submitQuizAttempt = async (quizId: string, attempt: Partial<QuizAttempt>): Promise<QuizAttempt> => {
    if (!currentUser) throw new Error('User not authenticated');
    
    const existingAttempts = quizAttempts[quizId] || [];
    const attemptNumber = existingAttempts.length + 1;
    
    const newAttempt: QuizAttempt = {
      id: `attempt_${Date.now()}`,
      quizId,
      userId: currentUser.uid,
      answers: attempt.answers || [],
      score: attempt.score || 0,
      maxScore: attempt.maxScore || 0,
      percentage: attempt.percentage || 0,
      startedAt: attempt.startedAt || new Date(),
      completedAt: new Date(),
      timeSpent: attempt.timeSpent || 0,
      passed: attempt.passed || false,
      attemptNumber
    };
    
    setQuizAttempts(prev => ({
      ...prev,
      [quizId]: [...existingAttempts, newAttempt]
    }));
    
    // Save to localStorage
    localStorage.setItem(
      `attempts_${currentUser.uid}_${quizId}`,
      JSON.stringify([...existingAttempts, newAttempt])
    );
    
    if (newAttempt.passed) {
      await markQuizComplete(quizId, newAttempt);
    }
    
    return newAttempt;
  };

  const value: CourseContextType = {
    currentCourse,
    userProgress,
    loading,
    loadCourse,
    getCurrentLesson,
    getNextLesson,
    getPreviousLesson,
    navigateToLesson,
    updateLessonProgress,
    updateVideoProgress,
    markLessonComplete,
    markAssignmentComplete,
    markQuizComplete,
    submitAssignment,
    submitQuizAttempt,
    currentLessonId,
    currentModuleId,
    videoProgress,
    quizAttempts,
    assignmentSubmissions
  };

  return (
    <CourseContext.Provider value={value}>
      {children}
    </CourseContext.Provider>
  );
}
