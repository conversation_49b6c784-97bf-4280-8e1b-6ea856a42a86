import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { courseService } from '@/services/courseService';
import { enrollmentService } from '@/services/enrollmentService';
import { Course } from '@/types/course';
import { 
  BookOpen, 
  Clock, 
  PlayCircle, 
  CheckCircle, 
  Award,
  Calendar,
  Download,
  Star,
  TrendingUp,
  Target,
  Users,
  Filter
} from 'lucide-react';

export default function MyCourses() {
  const { currentUser } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [enrolledCourseIds, setEnrolledCourseIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, in-progress, completed

  useEffect(() => {
    const loadData = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Load all courses
        const coursesData = await courseService.getPublishedCourses();

        // Load user enrollments
        console.log('🔍 MyCourses: Loading enrollments for user:', currentUser.uid, 'email:', currentUser.email);
        const userEnrollments = await enrollmentService.getUserEnrollments(currentUser.uid);
        const enrolledIds = userEnrollments.map(enrollment => enrollment.courseId);

        // Filter courses to only show enrolled ones
        const enrolledCourses = coursesData.filter(course => enrolledIds.includes(course.id));

        setCourses(enrolledCourses);
        setEnrolledCourseIds(enrolledIds);

        console.log('📚 MyCourses: Found', enrolledCourses.length, 'enrolled courses out of', coursesData.length, 'total courses');
        console.log('📚 MyCourses: Enrolled course IDs:', enrolledIds);
        console.log('📚 MyCourses: User enrollments:', userEnrollments);
      } catch (error) {
        console.error('Error loading enrolled courses:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentUser]);

  // Mock progress data - in real app, this would come from user progress tracking
  const getProgress = (courseId: string) => {
    const progressMap: { [key: string]: number } = {
      'course-1': 75,
      'course-2': 30,
      'course-3': 100
    };
    return progressMap[courseId] || 0;
  };

  const getStatus = (progress: number) => {
    if (progress === 0) return 'not-started';
    if (progress === 100) return 'completed';
    return 'in-progress';
  };

  const filteredCourses = courses.filter(course => {
    const progress = getProgress(course.id);
    const status = getStatus(progress);
    
    if (filter === 'in-progress') return status === 'in-progress';
    if (filter === 'completed') return status === 'completed';
    return true; // 'all'
  });

  const stats = [
    { label: 'Total Courses', value: courses.length.toString(), icon: BookOpen, color: 'text-blue-400' },
    { label: 'In Progress', value: courses.filter(c => getStatus(getProgress(c.id)) === 'in-progress').length.toString(), icon: Clock, color: 'text-yellow-400' },
    { label: 'Completed', value: courses.filter(c => getStatus(getProgress(c.id)) === 'completed').length.toString(), icon: CheckCircle, color: 'text-green-400' },
    { label: 'Certificates', value: courses.filter(c => getStatus(getProgress(c.id)) === 'completed').length.toString(), icon: Award, color: 'text-purple-400' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <Navigation />
      
      <div className="container mx-auto px-4 py-24">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">My Courses</h1>
          <p className="text-xl text-gray-300">Track your learning progress and continue your FlutterFlow journey</p>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <Card key={index} className="bg-gray-800 border-gray-700 shadow-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">{stat.label}</p>
                    <p className="text-3xl font-bold text-white">{stat.value}</p>
                  </div>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-4 mb-8">
          <Button
            onClick={() => setFilter('all')}
            className={`${filter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
          >
            <Filter className="w-4 h-4 mr-2" />
            All Courses
          </Button>
          <Button
            onClick={() => setFilter('in-progress')}
            className={`${filter === 'in-progress' ? 'bg-yellow-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
          >
            <Clock className="w-4 h-4 mr-2" />
            In Progress
          </Button>
          <Button
            onClick={() => setFilter('completed')}
            className={`${filter === 'completed' ? 'bg-green-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Completed
          </Button>
        </div>

        {/* Courses Grid */}
        {loading ? (
          <div className="grid lg:grid-cols-2 gap-8">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="bg-gray-800 border-gray-700 shadow-xl animate-pulse">
                <div className="h-48 bg-gray-700"></div>
                <CardContent className="p-6">
                  <div className="h-6 bg-gray-700 rounded mb-4"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredCourses.length > 0 ? (
          <div className="grid lg:grid-cols-2 gap-8">
            {filteredCourses.map((course) => {
              const progress = getProgress(course.id);
              const status = getStatus(progress);
              
              return (
                <Card key={course.id} className="bg-gray-800 border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <div className="relative">
                    <div className="h-48 overflow-hidden">
                      <img
                        src={course.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=400&fit=crop'}
                        alt={course.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    
                    {/* Status Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge className={`
                        ${status === 'completed' ? 'bg-green-600 text-white' : 
                          status === 'in-progress' ? 'bg-yellow-600 text-white' : 
                          'bg-gray-600 text-white'}
                      `}>
                        {status === 'completed' ? 'Completed' : 
                         status === 'in-progress' ? 'In Progress' : 
                         'Not Started'}
                      </Badge>
                    </div>

                    {/* Progress Badge */}
                    <div className="absolute top-4 right-4">
                      <div className="bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                        {progress}% Complete
                      </div>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">
                      {course.title}
                    </h3>
                    <p className="text-gray-300 mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-gray-400 mb-2">
                        <span>Progress</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>

                    {/* Course Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-6">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1 text-blue-400" />
                          <span>{Math.floor((course.duration || 180) / 60)}h {(course.duration || 180) % 60}m</span>
                        </div>
                        <div className="flex items-center">
                          <BookOpen className="w-4 h-4 mr-1 text-purple-400" />
                          <span>{course.modules?.length || 8} modules</span>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                        <span>{course.rating || '4.8'}</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <Button
                        className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                        onClick={() => window.location.href = `/course/${course.id}/content`}
                      >
                        <PlayCircle className="w-4 h-4 mr-2" />
                        {status === 'not-started' ? 'Start Course' : 'Continue Learning'}
                      </Button>
                      
                      {status === 'completed' && (
                        <Button 
                          variant="outline" 
                          className="border-gray-600 text-gray-300 hover:bg-gray-700"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Certificate
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="bg-gray-700 rounded-full w-32 h-32 flex items-center justify-center mx-auto mb-8">
              <BookOpen className="w-16 h-16 text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">No Courses Found</h3>
            <p className="text-gray-300 mb-8 max-w-md mx-auto">
              {filter === 'all' 
                ? "You haven't enrolled in any courses yet. Start your FlutterFlow journey today!"
                : `No ${filter.replace('-', ' ')} courses found.`
              }
            </p>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              onClick={() => window.location.href = '/courses'}
            >
              <BookOpen className="w-4 h-4 mr-2" />
              Browse Courses
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
