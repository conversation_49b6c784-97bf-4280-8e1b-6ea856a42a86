import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Eye, EyeOff, Mail, Lock, User, AlertTriangle, Shield } from 'lucide-react';
import { UserRole } from '@/types/user';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: 'login' | 'register';
}

export function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {
  const [mode, setMode] = useState<'login' | 'register' | 'forgot'>(defaultMode);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { toast } = useToast();
  const { loginWithEmail, registerWithEmail, loginWithGoogle, resetPassword } = useAuth();

  // Form state
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: '',
    firstName: '',
    lastName: '',
    role: 'user' as UserRole
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      displayName: '',
      firstName: '',
      lastName: '',
      role: 'user'
    });
    setErrors({});
    setShowPassword(false);
  };

  // Enhanced email validation function
  const validateEmail = (email: string): boolean => {
    // More comprehensive email regex
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    // Basic checks
    if (!email || email.trim() === '') return false;
    if (email.length > 254) return false; // RFC 5321 limit
    if (email.indexOf('@') === -1) return false;
    if (email.indexOf('@') !== email.lastIndexOf('@')) return false; // Multiple @ symbols
    if (email.startsWith('.') || email.endsWith('.')) return false;
    if (email.includes('..')) return false; // Consecutive dots

    // Test with regex
    return emailRegex.test(email.trim().toLowerCase());
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Enhanced email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (mode === 'register') {
      // Display name validation
      if (!formData.displayName) {
        newErrors.displayName = 'Full name is required';
      }

      // Password validation
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      // Confirm password validation
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    } else if (mode === 'login') {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Final email validation before Firebase call
      if (!validateEmail(formData.email)) {
        setErrors({ email: 'Invalid email address. Please enter a valid email.' });
        setLoading(false);
        return;
      }

      if (mode === 'login') {
        await loginWithEmail({
          email: formData.email.trim().toLowerCase(), // Normalize email
          password: formData.password
        });
        toast({
          title: "Welcome back!",
          description: "You have successfully logged in.",
        });
      } else if (mode === 'register') {
        await registerWithEmail({
          email: formData.email.trim().toLowerCase(), // Normalize email
          password: formData.password,
          displayName: formData.displayName
        });
        toast({
          title: "Account created!",
          description: "Please check your email to verify your account.",
        });
      } else if (mode === 'forgot') {
        await resetPassword(formData.email);
        toast({
          title: "Reset email sent!",
          description: "Check your email for password reset instructions.",
        });
        setMode('login');
        return;
      }

      resetForm();
      onClose();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    
    try {
      await loginWithGoogle();
      toast({
        title: "Welcome!",
        description: "You have successfully logged in with Google.",
      });
      resetForm();
      onClose();
    } catch (error: any) {
      if (error.message === 'redirect_initiated') {
        toast({
          title: "Redirecting...",
          description: "You will be redirected to Google for authentication.",
        });
        onClose(); // Close modal during redirect
        return;
      }

      // Handle popup cancellation gracefully
      if (error.message === 'popup_cancelled') {
        // Don't show error toast for user cancellation, just reset loading
        return;
      }

      // Handle authentication errors with more specific guidance
      let errorTitle = "Google Login Error";
      let errorDescription = error.message || "Failed to sign in with Google. Please try again.";

      if (error.code === 'auth/popup-blocked' || error.message === 'popup_blocked') {
        errorTitle = "Popup Blocked";
        errorDescription = "Your browser blocked the Google sign-in popup. Please allow popups for this site in your browser settings and try again.";
      } else if (error.code === 'auth/popup-closed-by-user') {
        errorTitle = "Sign-in Cancelled";
        errorDescription = "You closed the sign-in window. Please try again if you want to sign in.";
      } else if (error.code === 'auth/internal-error') {
        errorTitle = "Configuration Error";
        errorDescription = "Google Sign-in configuration issue. Please try email/password login or contact support.";
      } else if (error.code === 'auth/unauthorized-domain') {
        errorTitle = "Domain Not Authorized";
        errorDescription = `This domain (${window.location.origin}) needs to be authorized in Firebase Console. Please contact support.`;
      } else if (error.code === 'auth/network-request-failed') {
        errorTitle = "Network Error";
        errorDescription = "Please check your internet connection and try again.";
      } else if (error.message.includes('timeout') || error.message === 'GOOGLE_SIGNIN_TIMEOUT') {
        errorTitle = "Sign-in Timeout";
        errorDescription = `Google sign-in timeout. This usually means ${window.location.origin} is not authorized in Firebase Console. Please check the configuration guide.`;
      }

      toast({
        title: errorTitle,
        description: errorDescription,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-slate-900 border-slate-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold text-white">
            {mode === 'login' && 'Welcome Back to FreeCodeLap'}
            {mode === 'register' && 'Join FreeCodeLap'}
            {mode === 'forgot' && 'Reset Your Password'}
          </DialogTitle>
          <p className="text-center text-slate-400 mt-2">
            {mode === 'login' && 'Sign in to continue your learning journey'}
            {mode === 'register' && 'Start learning FlutterFlow development today'}
            {mode === 'forgot' && 'Enter your email to receive reset instructions'}
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Google Sign-in Button */}
          {mode !== 'forgot' && (
            <>
              <Button
                type="button"
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 font-medium py-3 transition-all duration-200 shadow-sm hover:shadow-md"
                onClick={handleGoogleLogin}
                disabled={loading}
              >
                <div className="flex items-center justify-center">
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span className="text-gray-700 font-medium">
                    {loading ? 'Signing in...' : 'Continue with Google'}
                  </span>
                </div>
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full border-slate-600" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-slate-900 px-3 text-slate-400 font-medium">
                    Or continue with email
                  </span>
                </div>
              </div>
            </>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-5">
            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-slate-200 font-medium">Email Address</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`pl-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500 ${errors.email ? 'border-red-500 focus:border-red-500' : ''}`}
                  disabled={loading}
                />
              </div>
              {errors.email && <p className="text-sm text-red-400 flex items-center gap-1">
                <AlertTriangle className="w-4 h-4" />
                {errors.email}
              </p>}
            </div>

            {/* Register Fields */}
            {mode === 'register' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="displayName" className="text-slate-200 font-medium">Full Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <Input
                      id="displayName"
                      type="text"
                      placeholder="Enter your full name"
                      value={formData.displayName}
                      onChange={(e) => handleInputChange('displayName', e.target.value)}
                      className={`pl-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500 ${errors.displayName ? 'border-red-500 focus:border-red-500' : ''}`}
                      disabled={loading}
                    />
                  </div>
                  {errors.displayName && <p className="text-sm text-red-400 flex items-center gap-1">
                    <AlertTriangle className="w-4 h-4" />
                    {errors.displayName}
                  </p>}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-slate-200 font-medium">First Name</Label>
                    <Input
                      id="firstName"
                      type="text"
                      placeholder="First name"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500"
                      disabled={loading}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-slate-200 font-medium">Last Name</Label>
                    <Input
                      id="lastName"
                      type="text"
                      placeholder="Last name"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500"
                      disabled={loading}
                    />
                  </div>
                </div>


              </>
            )}

            {/* Password Field */}
            {mode !== 'forgot' && (
              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-200 font-medium">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className={`pl-10 pr-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500 ${errors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={loading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 h-4 w-4 text-slate-400 hover:text-slate-200 transition-colors"
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </button>
                </div>
                {errors.password && <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.password}
                </p>}
              </div>
            )}

            {/* Confirm Password Field */}
            {mode === 'register' && (
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-slate-200 font-medium">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                  <Input
                    id="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    className={`pl-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-blue-500 ${errors.confirmPassword ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={loading}
                  />
                </div>
                {errors.confirmPassword && <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.confirmPassword}
                </p>}
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 transition-all duration-200 shadow-lg hover:shadow-xl"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Please wait...
                </div>
              ) : (
                mode === 'login' ? 'Sign In to FreeCodeLap' :
                mode === 'register' ? 'Create Your Account' :
                'Send Reset Email'
              )}
            </Button>
          </form>

          {/* Mode Switching */}
          <div className="text-center space-y-3">
            {mode === 'login' && (
              <>
                <button
                  type="button"
                  onClick={() => setMode('forgot')}
                  className="text-sm text-slate-400 hover:text-blue-400 transition-colors"
                >
                  Forgot your password?
                </button>
                <div>
                  <span className="text-sm text-slate-400">Don't have an account? </span>
                  <button
                    type="button"
                    onClick={() => setMode('register')}
                    className="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors"
                  >
                    Sign up for free
                  </button>
                </div>
              </>
            )}

            {mode === 'register' && (
              <div>
                <span className="text-sm text-slate-400">Already have an account? </span>
                <button
                  type="button"
                  onClick={() => setMode('login')}
                  className="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors"
                >
                  Sign in
                </button>
              </div>
            )}

            {mode === 'forgot' && (
              <button
                type="button"
                onClick={() => setMode('login')}
                className="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors"
              >
                ← Back to sign in
              </button>
            )}
          </div>

          {/* Security Notice */}
          <div className="text-xs text-slate-400 text-center bg-slate-800/50 p-4 rounded-lg border border-slate-700">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Shield className="w-4 h-4 text-green-400" />
              <span className="font-medium text-slate-300">Secure & Protected</span>
            </div>
            <p>Your data is encrypted and protected with enterprise-grade security. We never share your information.</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
