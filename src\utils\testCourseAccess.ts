/**
 * Comprehensive test for course access after enrollment fixes
 */

import { enrollmentService } from '@/services/enrollmentService';
import { courseService } from '@/services/courseService';
import { enrollmentManagementService } from '@/services/enrollmentManagementService';

export const testCourseAccessFix = async (userId: string, courseId: string) => {
  console.log('🧪 COMPREHENSIVE COURSE ACCESS TEST');
  console.log('=====================================');
  console.log('User ID:', userId);
  console.log('Course ID:', courseId);
  console.log('');

  try {
    // Test 1: Check both enrollment services
    console.log('🔍 Test 1: Checking both enrollment services...');
    
    const enrollmentServiceResult = await enrollmentService.isUserEnrolled(userId, courseId);
    console.log('📊 enrollmentService.isUserEnrolled():', enrollmentServiceResult);
    
    const courseServiceResult = await courseService.isUserEnrolled(userId, courseId);
    console.log('📊 courseService.isUserEnrolled():', courseServiceResult);
    
    console.log('');

    // Test 2: Check user enrollments
    console.log('🔍 Test 2: Getting user enrollments...');
    const userEnrollments = await courseService.getUserEnrollments(userId);
    console.log('📊 User enrollments:', userEnrollments);
    console.log('📊 Is course in enrollments:', userEnrollments.includes(courseId));
    console.log('');

    // Test 3: Check all enrollments for the course
    console.log('🔍 Test 3: Getting all enrollments for course...');
    const courseEnrollments = await enrollmentManagementService.getCourseEnrollments(courseId);
    console.log('📊 Total course enrollments:', courseEnrollments.length);
    
    courseEnrollments.forEach((enrollment, index) => {
      console.log(`📋 Enrollment ${index + 1}:`, {
        id: enrollment.id,
        userId: enrollment.userId,
        userEmail: enrollment.userEmail,
        userName: enrollment.userName,
        type: enrollment.paymentInfo ? 'Paid' : 'Manual'
      });
    });
    console.log('');

    // Test 4: Check if user matches any enrollment
    console.log('🔍 Test 4: Checking user match with enrollments...');
    
    // Get user email
    let userEmail = '';
    try {
      const { doc, getDoc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (userDoc.exists()) {
        userEmail = userDoc.data().email || '';
        console.log('📧 User email:', userEmail);
      }
    } catch (error) {
      console.log('⚠️ Could not get user email:', error);
    }

    const matchingEnrollments = courseEnrollments.filter(enrollment => {
      return enrollment.userId === userId || 
             enrollment.userId === userEmail ||
             enrollment.userEmail === userEmail;
    });

    console.log('📊 Matching enrollments:', matchingEnrollments.length);
    matchingEnrollments.forEach((enrollment, index) => {
      console.log(`✅ Match ${index + 1}:`, {
        matchType: enrollment.userId === userId ? 'User ID' : 
                  enrollment.userId === userEmail ? 'Email as User ID' : 'Email Match',
        enrollment: {
          userId: enrollment.userId,
          userEmail: enrollment.userEmail,
          userName: enrollment.userName
        }
      });
    });

    console.log('');
    console.log('🎯 FINAL DIAGNOSIS:');
    console.log('==================');
    
    const shouldHaveAccess = matchingEnrollments.length > 0;
    const actuallyHasAccess = enrollmentServiceResult && courseServiceResult;
    
    console.log('📊 Should have access (based on enrollments):', shouldHaveAccess);
    console.log('📊 Actually has access (both services):', actuallyHasAccess);
    
    if (shouldHaveAccess && actuallyHasAccess) {
      console.log('✅ SUCCESS: User has proper course access!');
    } else if (shouldHaveAccess && !actuallyHasAccess) {
      console.log('❌ ISSUE: User should have access but services say no');
      console.log('💡 Possible fixes:');
      console.log('   - Check enrollment service logic');
      console.log('   - Verify email matching');
      console.log('   - Check Firestore security rules');
    } else if (!shouldHaveAccess && !actuallyHasAccess) {
      console.log('ℹ️ CORRECT: User is not enrolled and has no access');
    } else {
      console.log('⚠️ UNEXPECTED: User has access but no enrollment found');
    }

    return {
      shouldHaveAccess,
      actuallyHasAccess,
      enrollmentServiceResult,
      courseServiceResult,
      matchingEnrollments: matchingEnrollments.length,
      userEnrollments: userEnrollments.length,
      success: shouldHaveAccess === actuallyHasAccess
    };

  } catch (error) {
    console.error('❌ Course access test failed:', error);
    return {
      error: error.message,
      success: false
    };
  }
};

export const quickAccessTest = async (userEmail: string, courseId: string) => {
  console.log('⚡ QUICK ACCESS TEST');
  console.log('===================');
  console.log('Email:', userEmail);
  console.log('Course:', courseId);
  
  try {
    const { collection, query, where, getDocs } = await import('firebase/firestore');
    const { db } = await import('@/lib/firebase');
    
    const enrollmentsRef = collection(db, 'enrollments');
    
    // Check all possible enrollment matches
    const checks = [
      { name: 'Email as userId', query: query(enrollmentsRef, where('userId', '==', userEmail), where('courseId', '==', courseId)) },
      { name: 'Original email field', query: query(enrollmentsRef, where('originalEmail', '==', userEmail), where('courseId', '==', courseId)) }
    ];
    
    let hasAccess = false;
    
    for (const check of checks) {
      const results = await getDocs(check.query);
      console.log(`📊 ${check.name}:`, results.docs.length, 'results');
      if (results.docs.length > 0) {
        hasAccess = true;
        results.docs.forEach((doc, index) => {
          const data = doc.data();
          console.log(`  📋 Result ${index + 1}:`, {
            userId: data.userId,
            originalEmail: data.originalEmail,
            enrolledUserName: data.enrolledUserName,
            status: data.status
          });
        });
      }
    }
    
    console.log('🎯 Final result:', hasAccess ? '✅ HAS ACCESS' : '❌ NO ACCESS');
    return hasAccess;
    
  } catch (error) {
    console.error('❌ Quick test failed:', error);
    return false;
  }
};

// Helper to run tests from browser console
if (typeof window !== 'undefined') {
  (window as any).testCourseAccessFix = testCourseAccessFix;
  (window as any).quickAccessTest = quickAccessTest;
  console.log('🧪 Course access test functions available:');
  console.log('• testCourseAccessFix(userId, courseId)');
  console.log('• quickAccessTest(userEmail, courseId)');
}
