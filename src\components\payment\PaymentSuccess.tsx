import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight, BookOpen, Play } from 'lucide-react';

interface PaymentSuccessProps {
  courseId: string;
  courseTitle: string;
  amount: number;
  currency: string;
  paymentData: any;
  autoRedirectSeconds?: number;
}

const PaymentSuccess: React.FC<PaymentSuccessProps> = ({
  courseId,
  courseTitle,
  amount,
  currency,
  paymentData,
  autoRedirectSeconds = 5,
}) => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(autoRedirectSeconds);

  useEffect(() => {
    console.log('🎉 PaymentSuccess component mounted, starting countdown');

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          console.log('⏰ Countdown finished, redirecting to course content');
          // Redirect to course content
          navigate(`/course/${courseId}/content`);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      console.log('🧹 PaymentSuccess cleanup');
      clearInterval(timer);
    };
  }, [courseId, navigate]);

  const handleStartLearning = () => {
    navigate(`/course/${courseId}/content`);
  };

  const handleViewCourse = () => {
    navigate(`/course/${courseId}/content`);
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg shadow-2xl border-0 bg-white">
        <CardContent className="p-8 text-center">
          {/* Success Icon */}
          <div className="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-6">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto" />
          </div>

          {/* Success Message */}
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Successful! 🎉
          </h1>
          
          <p className="text-gray-600 mb-6">
            You have successfully enrolled in <strong>{courseTitle}</strong>
          </p>

          {/* Payment Details */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-gray-900 mb-3">Payment Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Course:</span>
                <span className="font-medium text-gray-900">{courseTitle}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium text-gray-900">
                  {formatAmount(amount, currency)}
                </span>
              </div>
              {paymentData?.reference && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Reference:</span>
                  <span className="font-medium text-gray-900 font-mono text-xs">
                    {paymentData.reference}
                  </span>
                </div>
              )}
              {paymentData?.trans && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-medium text-gray-900 font-mono text-xs">
                    {paymentData.trans}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="font-medium text-green-600">✅ Completed</span>
              </div>
            </div>
          </div>

          {/* Auto-redirect Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center space-x-2 text-blue-800">
              <Play className="w-5 h-5" />
              <span className="font-medium">
                Starting your course in {countdown} seconds...
              </span>
            </div>
            <p className="text-blue-600 text-sm mt-1">
              You'll be redirected automatically
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleStartLearning}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold"
            >
              <Play className="w-5 h-5 mr-2" />
              Start Learning Now
            </Button>
            
            <Button
              onClick={handleViewCourse}
              variant="outline"
              className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3"
            >
              <BookOpen className="w-5 h-5 mr-2" />
              View Course Details
            </Button>
          </div>

          {/* Additional Info */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              🎓 Welcome to FreeCodeLap! Your learning journey starts now.
            </p>
            <p className="text-xs text-gray-400 mt-2">
              You can access this course anytime from your dashboard.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentSuccess;
