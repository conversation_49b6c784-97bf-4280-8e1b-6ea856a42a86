# HTTPS Setup for Secure Payment Processing

## 🔒 Why HTTPS is Required

The "automatic payment method filling disabled because this form doesn't use secure connection" error occurs because:

1. **Browser Security**: Modern browsers require HTTPS for payment autofill
2. **PCI DSS Compliance**: Payment processing requires secure connections
3. **User Trust**: SSL certificates build user confidence
4. **Data Protection**: Encrypts sensitive payment information

## 🛠️ Development Setup (localhost)

### Option 1: Use mkcert (Recommended)

```bash
# Install mkcert
# On macOS
brew install mkcert

# On Windows (with Chocolatey)
choco install mkcert

# On Linux
sudo apt install libnss3-tools
wget -O mkcert https://github.com/FiloSottile/mkcert/releases/download/v1.4.4/mkcert-v1.4.4-linux-amd64
chmod +x mkcert
sudo mv mkcert /usr/local/bin/

# Create local CA
mkcert -install

# Generate certificates for localhost
mkcert localhost 127.0.0.1 ::1

# This creates:
# localhost+2.pem (certificate)
# localhost+2-key.pem (private key)
```

### Option 2: Update Vite Configuration

Create or update `vite.config.ts`:

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import fs from 'fs'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    https: {
      key: fs.readFileSync('./localhost+2-key.pem'),
      cert: fs.readFileSync('./localhost+2.pem'),
    },
    host: 'localhost',
    port: 8080,
  },
})
```

### Option 3: Use Development Proxy

```bash
# Install local-ssl-proxy
npm install -g local-ssl-proxy

# Run your app normally
npm run dev

# In another terminal, create HTTPS proxy
local-ssl-proxy --source 8443 --target 8080 --cert localhost+2.pem --key localhost+2-key.pem

# Access via: https://localhost:8443
```

## 🚀 Production Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Automatic HTTPS with custom domain
vercel --prod
```

### Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Build and deploy
npm run build
netlify deploy --prod --dir=dist
```

### Custom Server with Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d yourdomain.com

# Configure your web server (nginx/apache) with SSL
```

## 🔧 Environment Variables

Create `.env.local` for development:

```env
# Development
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key
VITE_API_URL=https://localhost:8080

# Production
VITE_PAYSTACK_PUBLIC_KEY=pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3
VITE_API_URL=https://yourdomain.com
```

## 🛡️ Security Checklist

### ✅ Required for Payment Processing:
- [ ] HTTPS enabled (SSL certificate)
- [ ] Secure payment processor (Paystack)
- [ ] PCI DSS compliant hosting
- [ ] Content Security Policy headers
- [ ] Secure cookie settings
- [ ] Input validation and sanitization

### ✅ Form Security Features:
- [ ] `autocomplete` attributes on payment fields
- [ ] `autoCorrect="off"` and `spellCheck="false"`
- [ ] Proper `name` attributes for autofill
- [ ] SSL/TLS encryption indicators
- [ ] Security badges and trust signals

### ✅ Browser Compatibility:
- [ ] Chrome: Requires HTTPS for payment autofill
- [ ] Firefox: Requires HTTPS for secure forms
- [ ] Safari: Requires HTTPS for payment APIs
- [ ] Edge: Requires HTTPS for autofill features

## 🔍 Testing Payment Security

### Local Testing:
```bash
# Start with HTTPS
npm run dev:https

# Test payment forms at:
# https://localhost:8080/course/course-1/checkout
```

### Security Validation:
1. **SSL Labs Test**: https://www.ssllabs.com/ssltest/
2. **Security Headers**: https://securityheaders.com/
3. **Mozilla Observatory**: https://observatory.mozilla.org/

## 🚨 Common Issues & Solutions

### Issue: "Not Secure" in Browser
**Solution**: Ensure HTTPS is properly configured with valid SSL certificate

### Issue: Payment Autofill Disabled
**Solution**: Use HTTPS and proper form attributes (`autocomplete`, `name`)

### Issue: Mixed Content Warnings
**Solution**: Ensure all resources (scripts, images, APIs) use HTTPS

### Issue: CSP Violations
**Solution**: Update Content Security Policy to allow Paystack domains

## 📞 Support

If you encounter issues:
1. Check browser console for security errors
2. Verify SSL certificate validity
3. Test with different browsers
4. Contact Paystack support for payment-specific issues

## 🔗 Useful Links

- [Paystack Security Guide](https://paystack.com/docs/security/)
- [MDN Web Security](https://developer.mozilla.org/en-US/docs/Web/Security)
- [OWASP Security Guidelines](https://owasp.org/www-project-web-security-testing-guide/)
- [Let's Encrypt](https://letsencrypt.org/)
