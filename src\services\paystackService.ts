/**
 * Secure Paystack Payment Service for FreeCodeLap
 * Handles all payment operations with proper error handling and security
 */

import { db } from '@/lib/firebase';
import { createPayment, updatePayment } from '@/lib/firestore';
import { Payment } from '@/types/schema';
import { Timestamp } from 'firebase/firestore';

// Environment variables
const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;
const PAYSTACK_SECRET_KEY = import.meta.env.VITE_PAYSTACK_SECRET_KEY;

// Validate configuration
if (!PAYSTACK_PUBLIC_KEY) {
  console.error('❌ VITE_PAYSTACK_PUBLIC_KEY is missing from environment variables');
}

if (!PAYSTACK_SECRET_KEY) {
  console.error('❌ VITE_PAYSTACK_SECRET_KEY is missing from environment variables');
}

// Types
export interface PaymentInitData {
  userId: string;
  courseId: string;
  courseTitle: string;
  courseInstructor: string;
  amount: number; // Amount in USD
  currency: string; // 'USD'
  customerEmail: string;
  customerName?: string;
  customerPhone?: string;
}

export interface PaystackConfig {
  key: string;
  email: string;
  amount: number; // Amount in kobo (KES * 100)
  currency: string;
  reference: string;
  channels: string[];
  metadata: {
    userId: string;
    courseId: string;
    courseTitle: string;
    custom_fields: Array<{
      display_name: string;
      variable_name: string;
      value: string;
    }>;
  };
  callback: (response: any) => void;
  onClose: () => void;
}

export interface PaymentVerificationResult {
  success: boolean;
  message: string;
  data?: any;
  payment?: Payment;
}

// Exchange rate service (simplified - in production, use a real API)
const USD_TO_KES_RATE = 130; // This should come from a real exchange rate API

class PaystackService {
  private generateReference(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `freecodelap_${timestamp}_${random}`;
  }

  private convertUSDToKES(usdAmount: number): number {
    return Math.round(usdAmount * USD_TO_KES_RATE);
  }

  /**
   * Initialize a payment and create payment record
   */
  async initializePayment(paymentData: PaymentInitData): Promise<{ paymentId: string; config: PaystackConfig }> {
    try {
      // Convert USD to KES
      const kesAmount = this.convertUSDToKES(paymentData.amount);
      const reference = this.generateReference();

      // Create payment record in Firestore
      const payment: Omit<Payment, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: paymentData.userId,
        courseId: paymentData.courseId,
        amount: kesAmount * 100, // Convert to kobo
        currency: 'KES',
        displayAmount: kesAmount,
        status: 'pending',
        paymentMethod: 'card', // Will be updated based on actual payment method
        paymentProvider: 'paystack',
        paystackDetails: {
          reference: reference,
        },
        customerEmail: paymentData.customerEmail,
        customerName: paymentData.customerName,
        customerPhone: paymentData.customerPhone,
        courseTitle: paymentData.courseTitle,
        courseInstructor: paymentData.courseInstructor,
        metadata: {
          originalUSDAmount: paymentData.amount,
          exchangeRate: USD_TO_KES_RATE,
          convertedAt: new Date().toISOString(),
        },
      };

      const paymentId = await createPayment(payment);

      // Create Paystack configuration
      const config: PaystackConfig = {
        key: PAYSTACK_PUBLIC_KEY,
        email: paymentData.customerEmail,
        amount: kesAmount * 100, // Amount in kobo
        currency: 'KES',
        reference: reference,
        channels: ['card', 'mobile_money', 'bank_transfer', 'bank', 'ussd'],
        metadata: {
          userId: paymentData.userId,
          courseId: paymentData.courseId,
          courseTitle: paymentData.courseTitle,
          custom_fields: [
            {
              display_name: 'Course',
              variable_name: 'course_title',
              value: paymentData.courseTitle,
            },
            {
              display_name: 'Instructor',
              variable_name: 'instructor',
              value: paymentData.courseInstructor,
            },
            {
              display_name: 'Platform',
              variable_name: 'platform',
              value: 'FreeCodeLap',
            },
            {
              display_name: 'Payment ID',
              variable_name: 'payment_id',
              value: paymentId,
            },
          ],
        },
        callback: (response) => this.handlePaymentCallback(response, paymentId),
        onClose: () => this.handlePaymentClose(paymentId),
      };

      return { paymentId, config };
    } catch (error) {
      console.error('❌ Error initializing payment:', error);
      throw new Error('Failed to initialize payment. Please try again.');
    }
  }

  /**
   * Handle successful payment callback
   */
  private async handlePaymentCallback(response: any, paymentId: string): Promise<void> {
    try {
      console.log('💳 Payment callback received:', response);

      // Verify the payment with Paystack
      const verification = await this.verifyPayment(response.reference);

      if (verification.success) {
        // Update payment record
        await updatePayment(paymentId, {
          status: 'successful',
          paystackDetails: {
            ...verification.data,
            transactionId: verification.data.id?.toString(),
            authorizationCode: verification.data.authorization?.authorization_code,
            channel: verification.data.channel,
            cardType: verification.data.authorization?.card_type,
            last4: verification.data.authorization?.last4,
            bank: verification.data.authorization?.bank,
          },
          paidAt: Timestamp.now(),
        });

        console.log('✅ Payment verified and updated successfully');
      } else {
        // Update payment as failed
        await updatePayment(paymentId, {
          status: 'failed',
        });

        console.error('❌ Payment verification failed:', verification.message);
      }
    } catch (error) {
      console.error('❌ Error handling payment callback:', error);
      
      // Update payment as failed
      try {
        await updatePayment(paymentId, {
          status: 'failed',
        });
      } catch (updateError) {
        console.error('❌ Error updating failed payment:', updateError);
      }
    }
  }

  /**
   * Handle payment close/cancellation
   */
  private async handlePaymentClose(paymentId: string): Promise<void> {
    try {
      await updatePayment(paymentId, {
        status: 'cancelled',
      });
      console.log('ℹ️ Payment cancelled by user');
    } catch (error) {
      console.error('❌ Error handling payment close:', error);
    }
  }

  /**
   * Verify payment with Paystack API
   */
  async verifyPayment(reference: string): Promise<PaymentVerificationResult> {
    try {
      const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.status && data.data.status === 'success') {
        return {
          success: true,
          message: 'Payment verified successfully',
          data: data.data,
        };
      } else {
        return {
          success: false,
          message: data.message || 'Payment verification failed',
          data: data.data,
        };
      }
    } catch (error) {
      console.error('❌ Error verifying payment:', error);
      return {
        success: false,
        message: 'Failed to verify payment with Paystack',
      };
    }
  }

  /**
   * Get public key for frontend
   */
  getPublicKey(): string {
    return PAYSTACK_PUBLIC_KEY;
  }

  /**
   * Check if service is properly configured
   */
  isConfigured(): boolean {
    return !!(PAYSTACK_PUBLIC_KEY && PAYSTACK_SECRET_KEY);
  }
}

// Export singleton instance
export const paystackService = new PaystackService();
