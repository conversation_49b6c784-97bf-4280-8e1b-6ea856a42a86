import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { enrollmentService } from '@/services/enrollmentService';
import { enrollmentManagementService, EnrollmentWithUserDetails } from '@/services/enrollmentManagementService';
import { courseService } from '@/services/courseService';
import { Enrollment } from '@/types/schema';
import { Course } from '@/types/course';
import { exportService } from '@/services/exportService';
import {
  Users,
  Search,
  UserPlus,
  Trash2,
  Eye,
  Download,
  FileText,
  Loader2,
  RefreshCw,
  Calendar,
  BookOpen,
  DollarSign,
  Award,
  Filter
} from 'lucide-react';

interface EnrollmentManagementProps {
  className?: string;
}

export const EnrollmentManagement: React.FC<EnrollmentManagementProps> = ({ className }) => {
  const { toast } = useToast();
  const [enrollments, setEnrollments] = useState<EnrollmentWithUserDetails[]>([]);
  const [filteredEnrollments, setFilteredEnrollments] = useState<EnrollmentWithUserDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  
  // Manual enrollment modal state - Updated for authenticated users only
  const [showManualEnrollModal, setShowManualEnrollModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [selectedCourseForEnrollment, setSelectedCourseForEnrollment] = useState<string>('');
  const [availableCourses, setAvailableCourses] = useState<CourseSearchResult[]>([]);
  const [authenticatedUsers, setAuthenticatedUsers] = useState<UserProfile[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [adminNote, setAdminNote] = useState('');
  const [enrolling, setEnrolling] = useState(false);
  const [exporting, setExporting] = useState(false);

  // Load data
  useEffect(() => {
    loadEnrollments();
    loadAvailableCourses();
  }, []);

  // Load users when modal opens
  useEffect(() => {
    if (showManualEnrollModal) {
      loadAuthenticatedUsers();
    }
  }, [showManualEnrollModal]);

  // Filter enrollments
  useEffect(() => {
    filterEnrollments();
  }, [enrollments, searchTerm, selectedCourse, selectedStatus]);

  const loadEnrollments = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading enrollments...');
      const data = await enrollmentManagementService.getAllEnrollments(100);
      console.log('✅ Enrollments loaded successfully:', data.length);
      console.log('📋 Enrollment data:', data);
      setEnrollments(data);
    } catch (error: any) {
      console.error('❌ Error loading enrollments:', error);

      // More specific error messages
      let errorMessage = "Failed to load enrollments. Please try again.";
      if (error?.code === 'permission-denied') {
        errorMessage = "Permission denied. Please ensure you have admin access.";
      } else if (error?.code === 'unavailable') {
        errorMessage = "Service temporarily unavailable. Please try again later.";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error Loading Enrollments",
        description: errorMessage,
        variant: "destructive",
      });

      // Set empty state on error
      setEnrollments([]);
    } finally {
      setLoading(false);
    }
  };

  const loadAuthenticatedUsers = async () => {
    try {
      setLoadingUsers(true);
      console.log('👥 Loading authenticated users...');
      const users = await enrollmentManagementService.getAllUsers();
      console.log('✅ Authenticated users loaded:', users.length);
      setAuthenticatedUsers(users);
    } catch (error: any) {
      console.error('❌ Error loading users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  const loadAvailableCourses = async () => {
    try {
      const courses = await enrollmentManagementService.getAvailableCourses();
      setAvailableCourses(courses);
    } catch (error) {
      console.error('Error loading courses:', error);
    }
  };



  const filterEnrollments = () => {
    let filtered = enrollments;

    // Filter by search term (user email or name)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(enrollment =>
        enrollment.userEmail.toLowerCase().includes(searchLower) ||
        enrollment.userName.toLowerCase().includes(searchLower) ||
        enrollment.courseTitle.toLowerCase().includes(searchLower)
      );
    }

    // Filter by course
    if (selectedCourse !== 'all') {
      filtered = filtered.filter(enrollment => enrollment.courseId === selectedCourse);
    }

    // Filter by status (paid vs manual)
    if (selectedStatus === 'paid') {
      filtered = filtered.filter(enrollment => enrollment.paymentInfo);
    } else if (selectedStatus === 'manual') {
      filtered = filtered.filter(enrollment => !enrollment.paymentInfo);
    }

    setFilteredEnrollments(filtered);
  };

  const resetEnrollmentForm = () => {
    setSelectedUserId('');
    setSelectedCourseForEnrollment('');
    setAdminNote('');
  };

  const handleExportEnrollments = async (format: 'csv' | 'json' = 'csv') => {
    try {
      setExporting(true);

      // Create export options with current filters
      const exportOptions = {
        format,
        dateRange: undefined, // Could be extended to include date filtering
        includeFields: undefined // Could be extended to allow field selection
      };

      await exportService.exportEnrollments(exportOptions);

      toast({
        title: "Export Successful",
        description: `Enrollments data has been exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export enrollments data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };



  const handleManualEnrollment = async () => {
    // Validate inputs
    if (!selectedUserId) {
      toast({
        title: "Error",
        description: "Please select a user",
        variant: "destructive",
      });
      return;
    }

    if (!selectedCourseForEnrollment) {
      toast({
        title: "Error",
        description: "Please select a course",
        variant: "destructive",
      });
      return;
    }

    try {
      setEnrolling(true);

      // Find the selected user and course details
      const selectedUser = authenticatedUsers.find(u => u.uid === selectedUserId);
      const selectedCourse = availableCourses.find(c => c.id === selectedCourseForEnrollment);

      console.log('🎯 Enrolling authenticated user:', {
        userId: selectedUserId,
        userEmail: selectedUser?.email,
        userName: selectedUser?.displayName,
        course: selectedCourse?.title
      });

      // Enroll the user using their authenticated userId
      await enrollmentManagementService.manuallyEnrollAuthenticatedUser(
        selectedUserId, // Use authenticated userId
        selectedCourseForEnrollment,
        adminNote || 'Manually enrolled by admin'
      );

      // Send email notification
      try {
        await enrollmentManagementService.sendEnrollmentNotification(
          selectedUser?.email || '',
          selectedUser?.displayName || 'User',
          selectedCourse?.title || 'Course',
          adminNote || 'You have been manually enrolled by an administrator'
        );

        toast({
          title: "Success",
          description: `${selectedUser?.displayName} (${selectedUser?.email}) has been enrolled and notified via email`,
        });
      } catch (emailError) {
        console.error('Email notification failed:', emailError);
        toast({
          title: "Enrolled Successfully",
          description: `${selectedUser?.displayName} has been enrolled, but email notification failed`,
          variant: "default",
        });
      }

      // Reset form and reload data
      resetEnrollmentForm();
      setShowManualEnrollModal(false);
      loadEnrollments();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to enroll user",
        variant: "destructive",
      });
    } finally {
      setEnrolling(false);
    }
  };

  const handleRemoveEnrollment = async (enrollmentId: string, userName: string) => {
    if (!confirm(`Are you sure you want to remove ${userName}'s enrollment?`)) {
      return;
    }

    try {
      await enrollmentManagementService.removeEnrollment(enrollmentId);
      toast({
        title: "Success",
        description: "Enrollment removed successfully",
      });
      loadEnrollments();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove enrollment",
        variant: "destructive",
      });
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getUniqueCoursesFromEnrollments = () => {
    const courseMap = new Map();
    enrollments.forEach(enrollment => {
      if (!courseMap.has(enrollment.courseId)) {
        courseMap.set(enrollment.courseId, {
          id: enrollment.courseId,
          title: enrollment.courseTitle
        });
      }
    });
    return Array.from(courseMap.values());
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Enrollment Management</h2>
          <p className="text-gray-400">View and manage course enrollments</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={loadEnrollments}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          {/* Export Buttons */}
          <Button
            onClick={() => handleExportEnrollments('csv')}
            disabled={exporting}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            {exporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </>
            )}
          </Button>

          <Button
            onClick={() => handleExportEnrollments('json')}
            disabled={exporting}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <FileText className="h-4 w-4 mr-2" />
            Export JSON
          </Button>

          <Dialog
            open={showManualEnrollModal}
            onOpenChange={(open) => {
              setShowManualEnrollModal(open);
              if (!open) {
                resetEnrollmentForm(); // Reset form when modal closes
              }
            }}
          >
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <UserPlus className="h-4 w-4 mr-2" />
                Manual Enrollment
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl">
              <DialogHeader>
                <DialogTitle>Manually Enroll User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {/* User Selection */}
                <div>
                  <Label>Select Authenticated User *</Label>
                  {loadingUsers ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <span className="text-gray-400">Loading users...</span>
                    </div>
                  ) : (
                    <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                      <SelectTrigger className="bg-gray-700 border-gray-600">
                        <SelectValue placeholder="Choose an authenticated user to enroll" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700">
                        {authenticatedUsers.length > 0 ? (
                          authenticatedUsers.map(user => (
                            <SelectItem key={user.uid} value={user.uid}>
                              <div className="flex flex-col">
                                <span className="font-medium">{user.displayName}</span>
                                <span className="text-sm text-gray-400">{user.email}</span>
                                <span className="text-xs text-gray-500">Role: {user.role}</span>
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-users" disabled>
                            No authenticated users found
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  )}
                  <p className="text-xs text-gray-400 mt-1">
                    Only authenticated users with Firebase Auth accounts can be enrolled
                  </p>
                </div>

                {/* Preview */}
                {selectedUserId && (
                  <div className="p-3 bg-blue-900/20 border border-blue-600 rounded">
                    <div className="text-blue-400 font-medium">📧 Enrollment Preview:</div>
                    {(() => {
                      const selectedUser = authenticatedUsers.find(u => u.uid === selectedUserId);
                      return selectedUser ? (
                        <>
                          <div className="text-white font-medium">{selectedUser.displayName}</div>
                          <div className="text-gray-300 text-sm">{selectedUser.email}</div>
                          <div className="text-gray-400 text-xs">
                            Authenticated user will be enrolled and notified via email
                          </div>
                        </>
                      ) : null;
                    })()}
                  </div>
                )}

                {/* Course Selection */}
                <div>
                  <Label>Select Course</Label>
                  <Select value={selectedCourseForEnrollment} onValueChange={setSelectedCourseForEnrollment}>
                    <SelectTrigger className="bg-gray-700 border-gray-600">
                      <SelectValue placeholder="Choose a course to enroll user in" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      {availableCourses.length > 0 ? (
                        availableCourses.map(course => (
                          <SelectItem key={course.id} value={course.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{course.title}</span>
                              <span className="text-sm text-gray-400">
                                {course.price === 0 ? 'Free' : `${course.currency} ${course.price.toLocaleString()}`}
                                {course.enrollmentCount > 0 && ` • ${course.enrollmentCount} enrolled`}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-courses" disabled>
                          No courses available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {availableCourses.length === 0 && (
                    <p className="text-sm text-gray-400 mt-1">
                      No courses available for enrollment
                    </p>
                  )}
                </div>

                {/* Admin Note */}
                <div>
                  <Label>Admin Note (Optional)</Label>
                  <Textarea
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                    placeholder="Reason for manual enrollment..."
                    className="bg-gray-700 border-gray-600"
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowManualEnrollModal(false);
                      resetEnrollmentForm();
                    }}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleManualEnrollment}
                    disabled={enrolling || !selectedUserId || !selectedCourseForEnrollment}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {enrolling ? 'Enrolling...' : 'Enroll User'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label className="text-gray-300">Search Users</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by email or name..."
                  className="pl-10 bg-gray-700 border-gray-600 text-white"
                />
              </div>
            </div>
            <div>
              <Label className="text-gray-300">Filter by Course</Label>
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="all">All Courses</SelectItem>
                  {getUniqueCoursesFromEnrollments().map(course => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-gray-300">Filter by Type</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="all">All Enrollments</SelectItem>
                  <SelectItem value="paid">Paid Enrollments</SelectItem>
                  <SelectItem value="manual">Manual Enrollments</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total Enrollments</p>
                <p className="text-2xl font-bold text-white">{enrollments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Paid Enrollments</p>
                <p className="text-2xl font-bold text-white">
                  {enrollments.filter(e => e.paymentInfo).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center">
              <UserPlus className="h-8 w-8 text-purple-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Manual Enrollments</p>
                <p className="text-2xl font-bold text-white">
                  {enrollments.filter(e => !e.paymentInfo).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-yellow-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Certificates Earned</p>
                <p className="text-2xl font-bold text-white">
                  {enrollments.filter(e => e.certificateEarned).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enrollments Table */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Enrollments ({filteredEnrollments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
              <span className="ml-2 text-gray-400">Loading enrollments...</span>
            </div>
          ) : filteredEnrollments.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">No enrollments found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Course</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Enrolled</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Progress</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Type</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEnrollments.map((enrollment) => (
                    <tr key={enrollment.id} className="border-b border-gray-700 hover:bg-gray-750">
                      <td className="py-3 px-4">
                        <div>
                          <p className="text-white font-medium">{enrollment.userName}</p>
                          <p className="text-gray-400 text-sm">{enrollment.userEmail}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <p className="text-white font-medium">{enrollment.courseTitle}</p>
                          <p className="text-gray-400 text-sm">
                            {enrollment.coursePrice > 0 ? `${enrollment.coursePrice} KES` : 'Free'}
                          </p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-gray-300 text-sm">{formatDate(enrollment.enrolledAt)}</p>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-700 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-400 h-2 rounded-full"
                              style={{ width: `${enrollment.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-300">{enrollment.progress}%</span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {enrollment.paymentInfo ? (
                          <Badge className="bg-green-600 text-white">Paid</Badge>
                        ) : (
                          <Badge className="bg-purple-600 text-white">Manual</Badge>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-white"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-red-400"
                            onClick={() => handleRemoveEnrollment(enrollment.id, enrollment.userName)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
