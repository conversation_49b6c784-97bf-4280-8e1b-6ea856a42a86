
import React, { useState } from 'react';
import { CheckCircle, Smartphone, Code, Database, CreditCard, Bot, Globe, BarChart } from 'lucide-react';
import { AuthModal } from '@/components/auth/AuthModal';
import { useCourseEnrollment } from '@/hooks/useCourseEnrollment';

const WhatYoullLearnSection = () => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'register'>('register');

  const { handleStartLearning, handleViewFreePreview, isEnrolling } = useCourseEnrollment();

  const onAuthRequired = () => {
    setAuthModalMode('register');
    setIsAuthModalOpen(true);
  };
  const skills = [
    {
      icon: <Smartphone className="w-8 h-8 text-blue-600" />,
      title: "Mobile App Development",
      description: "Build native iOS and Android apps without coding"
    },
    {
      icon: <Database className="w-8 h-8 text-green-600" />,
      title: "Firebase Integration",
      description: "Authentication, Firestore, Storage, and Cloud Functions"
    },
    {
      icon: <Code className="w-8 h-8 text-purple-600" />,
      title: "Custom Widgets",
      description: "Create reusable components and advanced UI elements"
    },
    {
      icon: <Bot className="w-8 h-8 text-orange-600" />,
      title: "AI Integration",
      description: "ChatGPT, OpenAI APIs, and intelligent chatbots"
    },
    {
      icon: <CreditCard className="w-8 h-8 text-indigo-600" />,
      title: "Payment Systems",
      description: "Stripe, RevenueCat, and subscription management"
    },
    {
      icon: <Globe className="w-8 h-8 text-teal-600" />,
      title: "API Integration",
      description: "REST APIs, third-party services, and data handling"
    },
    {
      icon: <BarChart className="w-8 h-8 text-red-600" />,
      title: "App Monetization",
      description: "AdMob integration and revenue optimization"
    },
    {
      icon: <CheckCircle className="w-8 h-8 text-emerald-600" />,
      title: "App Store Publishing",
      description: "Deploy to Google Play Store and Apple App Store"
    }
  ];

  const courseOutline = [
    "FlutterFlow fundamentals and UI design principles",
    "Responsive design for mobile, tablet, and web",
    "State management and data binding",
    "Navigation and routing strategies",
    "Form validation and user input handling",
    "Image handling and media management",
    "Push notifications and real-time updates",
    "App testing and debugging techniques",
    "Performance optimization best practices",
    "Deployment and maintenance strategies"
  ];

  return (
    <section className="py-20 section-dark">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl font-bold text-white mb-4 gradient-text">
            What You'll Master
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            From beginner basics to advanced integrations, learn everything you need to build
            professional mobile and web applications
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 mb-20">
          <div>
            <h3 className="text-2xl font-bold text-white mb-8">Core Skills You'll Develop</h3>
            <div className="grid sm:grid-cols-2 gap-6">
              {skills.map((skill, index) => (
                <div
                  key={index}
                  className="card-elevated card-bounce p-6 rounded-xl shadow-2xl hover:shadow-3xl transition-all duration-500 border-0 animate-delay-200"
                >
                  <div className="mb-4">{skill.icon}</div>
                  <h4 className="text-lg font-semibold text-white mb-2">{skill.title}</h4>
                  <p className="text-blue-200 text-sm leading-relaxed">{skill.description}</p>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-2xl font-bold text-white mb-8">Complete Course Outline</h3>
            <div className="card-elevated card-slide-right p-8 rounded-xl shadow-2xl border-0">
              <ul className="space-y-4">
                {courseOutline.map((item, index) => (
                  <li key={index} className="flex items-start space-x-3 animate-fade-in-left" style={{animationDelay: `${index * 0.1}s`}}>
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-200 leading-relaxed">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-600 to-pink-600 rounded-2xl p-8 text-white text-center animate-slide-in-bottom">
          <h3 className="text-2xl font-bold mb-4">Ready to Build Your First App?</h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Join thousands of developers who've transformed their careers with FlutterFlow.
            Start building professional apps today!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              type="button"
              className="btn-animated btn-pink transform hover:scale-105 disabled:opacity-50"
              onClick={() => handleStartLearning(onAuthRequired)}
              disabled={isEnrolling}
            >
              {isEnrolling ? 'Processing...' : 'Start Learning Today'}
            </button>
            <button
              type="button"
              className="btn-animated btn-ghost transform hover:scale-105"
              onClick={handleViewFreePreview}
            >
              View Free Preview
            </button>
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultMode={authModalMode}
      />
    </section>
  );
};

export default WhatYoullLearnSection;
