/**
 * Test utility for manual enrollment functionality
 */

import { enrollmentManagementService } from '@/services/enrollmentManagementService';

export const testManualEnrollmentFlow = async () => {
  console.log('🧪 Testing Manual Enrollment Flow...');
  
  try {
    // Test 1: Check if we can load users
    console.log('👥 Test 1: Attempting to load users...');
    const users = await enrollmentManagementService.getAllUsers();
    console.log(`📊 Result: ${users.length} users loaded`);
    
    if (users.length === 0) {
      console.log('ℹ️ No users loaded - this is expected if not admin. Manual email entry will work.');
    } else {
      console.log('✅ Users loaded successfully - admin permissions confirmed');
      console.log('📋 Sample users:', users.slice(0, 3).map(u => ({
        email: u.email,
        name: u.displayName,
        role: u.role
      })));
    }

    // Test 2: Check available courses
    console.log('📚 Test 2: Loading available courses...');
    const courses = await enrollmentManagementService.getAvailableCourses();
    console.log(`📊 Result: ${courses.length} courses available`);
    
    if (courses.length > 0) {
      console.log('📋 Sample courses:', courses.slice(0, 3).map(c => ({
        title: c.title,
        price: c.price,
        currency: c.currency
      })));
    }

    // Test 3: Test user search functionality
    console.log('🔍 Test 3: Testing user search...');
    const searchResults = await enrollmentManagementService.searchUsers('test');
    console.log(`📊 Search result: ${searchResults.length} users found for "test"`);

    // Test 4: Test email-based search
    console.log('📧 Test 4: Testing email-based search...');
    const emailSearchResults = await enrollmentManagementService.searchUsers('@');
    console.log(`📊 Email search result: ${emailSearchResults.length} users with @ symbol`);

    console.log('🎉 Manual enrollment flow tests completed!');
    
    return {
      usersLoaded: users.length,
      coursesAvailable: courses.length,
      searchWorks: searchResults.length >= 0,
      emailSearchWorks: emailSearchResults.length >= 0,
      canProceedWithManualEntry: courses.length > 0,
      success: true
    };
    
  } catch (error) {
    console.error('❌ Manual enrollment test failed:', error);
    return {
      error: error.message,
      success: false
    };
  }
};

export const logManualEnrollmentInstructions = () => {
  console.log('📋 Manual Enrollment Instructions:');
  console.log('');
  console.log('🔧 How to use Manual Enrollment:');
  console.log('1. Click "Manual Enrollment" button');
  console.log('2. If users load automatically - select from the list');
  console.log('3. If no users load - type an email address to search');
  console.log('4. If search doesn\'t find the user - type the full email and click "Use manually"');
  console.log('5. Select a course from the dropdown');
  console.log('6. Add an optional admin note');
  console.log('7. Click "Enroll User" to complete');
  console.log('');
  console.log('✅ Features:');
  console.log('• Auto-load users (if admin permissions)');
  console.log('• Real-time user search');
  console.log('• Manual email entry fallback');
  console.log('• Email notifications (simulated in dev)');
  console.log('• Course selection with details');
  console.log('• Admin notes support');
  console.log('');
  console.log('🚀 System is ready for use!');
};

// Auto-run instructions
if (typeof window !== 'undefined') {
  console.log('📋 FreeCodeLap Manual Enrollment System');
  logManualEnrollmentInstructions();
}
