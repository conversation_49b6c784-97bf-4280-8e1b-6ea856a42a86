import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Save } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

interface Lesson {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  duration: number;
  isFree: boolean;
  order: number;
}

export const SimpleCourseForm: React.FC = () => {
  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    price: 3500,
    thumbnail: '',
    instructor: 'Ahmed <PERSON>kal',
    category: 'Programming',
    level: 'Beginner'
  });

  const [lessons, setLessons] = useState<Lesson[]>([
    {
      id: 'lesson-1',
      title: '',
      description: '',
      videoUrl: '',
      duration: 60,
      isFree: false,
      order: 1
    }
  ]);

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const addLesson = () => {
    const newLesson: Lesson = {
      id: `lesson-${lessons.length + 1}`,
      title: '',
      description: '',
      videoUrl: '',
      duration: 60,
      isFree: false,
      order: lessons.length + 1
    };
    setLessons([...lessons, newLesson]);
  };

  const removeLesson = (index: number) => {
    if (lessons.length > 1) {
      setLessons(lessons.filter((_, i) => i !== index));
    }
  };

  const updateLesson = (index: number, field: keyof Lesson, value: any) => {
    const updatedLessons = lessons.map((lesson, i) => 
      i === index ? { ...lesson, [field]: value } : lesson
    );
    setLessons(updatedLessons);
  };

  const generateCourseId = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50) + '-' + Date.now();
  };

  const saveCourse = async () => {
    if (!courseData.title.trim()) {
      setError('Course title is required');
      return;
    }

    if (lessons.some(lesson => !lesson.title.trim() || !lesson.videoUrl.trim())) {
      setError('All lessons must have a title and video URL');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const courseId = generateCourseId(courseData.title);
      
      const courseDoc = {
        id: courseId,
        title: courseData.title,
        description: courseData.description,
        price: courseData.price,
        currency: 'KES',
        thumbnail: courseData.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500',
        instructor: courseData.instructor,
        instructorId: 'ahmed-takal',
        category: courseData.category,
        level: courseData.level,
        duration: `${lessons.length} lessons`,
        totalDuration: lessons.reduce((total, lesson) => total + lesson.duration, 0),
        isPublished: true,
        featured: false,
        enrollmentCount: 0,
        rating: 0,
        reviewCount: 0,
        allowComments: true,
        allowDownloads: true,
        certificateEnabled: true,
        accessType: 'lifetime',
        status: 'published',
        tags: ['Course', 'Learning'],
        requirements: ['Basic computer skills'],
        learningOutcomes: ['Learn new skills'],
        targetAudience: ['Students', 'Professionals'],
        lessons: lessons.map(lesson => ({
          id: lesson.id,
          title: lesson.title,
          description: lesson.description,
          videoUrl: lesson.videoUrl,
          duration: lesson.duration,
          isFree: lesson.isFree,
          order: lesson.order,
          type: 'video',
          textContent: '',
          resources: [],
          isPreview: lesson.isFree,
          isCompleted: false,
          watchTime: 0
        })),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      console.log('📝 Creating course with structure:', courseDoc);

      const docRef = await addDoc(collection(db, 'courses'), courseDoc);
      
      setSuccess(`✅ Course created successfully! ID: ${docRef.id}`);
      console.log('✅ Course created with ID:', docRef.id);

      // Reset form
      setCourseData({
        title: '',
        description: '',
        price: 3500,
        thumbnail: '',
        instructor: 'Ahmed Takal',
        category: 'Programming',
        level: 'Beginner'
      });
      setLessons([{
        id: 'lesson-1',
        title: '',
        description: '',
        videoUrl: '',
        duration: 60,
        isFree: false,
        order: 1
      }]);

    } catch (err: any) {
      console.error('❌ Error creating course:', err);
      setError(err.message || 'Failed to create course');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">📚 Simple Course Creator</CardTitle>
        <p className="text-gray-400 text-sm">
          Create courses with direct lessons structure (no modules)
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Success/Error Messages */}
        {success && (
          <div className="bg-green-900/20 border border-green-500 p-3 rounded-lg">
            <div className="text-green-400">{success}</div>
          </div>
        )}
        
        {error && (
          <div className="bg-red-900/20 border border-red-500 p-3 rounded-lg">
            <div className="text-red-400">{error}</div>
          </div>
        )}

        {/* Course Basic Info */}
        <div className="space-y-4">
          <h3 className="text-white font-medium">Course Information</h3>
          
          <div>
            <Label className="text-gray-300">Course Title *</Label>
            <Input
              value={courseData.title}
              onChange={(e) => setCourseData({...courseData, title: e.target.value})}
              placeholder="Enter course title"
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>

          <div>
            <Label className="text-gray-300">Description</Label>
            <Textarea
              value={courseData.description}
              onChange={(e) => setCourseData({...courseData, description: e.target.value})}
              placeholder="Course description"
              className="bg-gray-700 border-gray-600 text-white"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-gray-300">Price (KES)</Label>
              <Input
                type="number"
                value={courseData.price}
                onChange={(e) => setCourseData({...courseData, price: Number(e.target.value)})}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <div>
              <Label className="text-gray-300">Category</Label>
              <Input
                value={courseData.category}
                onChange={(e) => setCourseData({...courseData, category: e.target.value})}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          <div>
            <Label className="text-gray-300">Thumbnail URL</Label>
            <Input
              value={courseData.thumbnail}
              onChange={(e) => setCourseData({...courseData, thumbnail: e.target.value})}
              placeholder="https://example.com/image.jpg"
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>
        </div>

        {/* Lessons */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-white font-medium">Lessons</h3>
            <Button onClick={addLesson} size="sm" className="bg-green-600 hover:bg-green-700">
              <Plus className="w-4 h-4 mr-1" />
              Add Lesson
            </Button>
          </div>

          {lessons.map((lesson, index) => (
            <div key={lesson.id} className="bg-gray-700 p-4 rounded-lg border border-gray-600">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-white font-medium">Lesson {index + 1}</h4>
                {lessons.length > 1 && (
                  <Button 
                    onClick={() => removeLesson(index)} 
                    size="sm" 
                    variant="destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <Label className="text-gray-300">Lesson Title *</Label>
                  <Input
                    value={lesson.title}
                    onChange={(e) => updateLesson(index, 'title', e.target.value)}
                    placeholder="Enter lesson title"
                    className="bg-gray-600 border-gray-500 text-white"
                  />
                </div>

                <div>
                  <Label className="text-gray-300">Video URL *</Label>
                  <Input
                    value={lesson.videoUrl}
                    onChange={(e) => updateLesson(index, 'videoUrl', e.target.value)}
                    placeholder="https://youtube.com/watch?v=... or direct video URL"
                    className="bg-gray-600 border-gray-500 text-white"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-gray-300">Duration (minutes)</Label>
                    <Input
                      type="number"
                      value={lesson.duration}
                      onChange={(e) => updateLesson(index, 'duration', Number(e.target.value))}
                      className="bg-gray-600 border-gray-500 text-white"
                    />
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <input
                      type="checkbox"
                      checked={lesson.isFree}
                      onChange={(e) => updateLesson(index, 'isFree', e.target.checked)}
                      className="rounded"
                    />
                    <Label className="text-gray-300">Free Preview</Label>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Save Button */}
        <Button 
          onClick={saveCourse} 
          disabled={loading}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          <Save className="w-4 h-4 mr-2" />
          {loading ? 'Creating Course...' : 'Create Course'}
        </Button>

        {/* Structure Info */}
        <div className="bg-gray-900 p-4 rounded-lg">
          <h4 className="text-white font-medium mb-2">Generated Structure</h4>
          <div className="text-sm text-gray-300">
            This will create a course document with a "lessons" array containing all your lessons directly.
            No separate modules collection needed.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SimpleCourseForm;
