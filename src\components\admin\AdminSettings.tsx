import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { exportService } from '@/services/exportService';
import {
  Settings,
  Download,
  Database,
  Mail,
  Shield,
  Palette,
  Globe,
  CreditCard,
  Bell,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface PlatformSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportEmail: string;
  defaultCurrency: string;
  timezone: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  emailVerificationRequired: boolean;
  autoApproveReviews: boolean;
  maxFileUploadSize: number;
  sessionTimeout: number;
}

interface PaymentSettings {
  paystackPublicKey: string;
  paystackSecretKey: string;
  testMode: boolean;
  defaultCurrency: string;
  taxRate: number;
  refundPolicy: string;
}

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  enableEmailNotifications: boolean;
}

export const AdminSettings: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [exporting, setExporting] = useState(false);
  
  // Settings state
  const [platformSettings, setPlatformSettings] = useState<PlatformSettings>({
    siteName: 'FreeCodeLap',
    siteDescription: 'Learn coding with Ahmed Takal',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    defaultCurrency: 'USD',
    timezone: 'Africa/Nairobi',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    autoApproveReviews: false,
    maxFileUploadSize: 10, // MB
    sessionTimeout: 24 // hours
  });

  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    paystackPublicKey: 'pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3',
    paystackSecretKey: '************************************************',
    testMode: false,
    defaultCurrency: 'KES',
    taxRate: 0,
    refundPolicy: 'Refunds are processed within 7 business days.'
  });

  const [emailSettings, setEmailSettings] = useState<EmailSettings>({
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'FreeCodeLap',
    enableEmailNotifications: true
  });

  const handleExportData = async (type: 'enrollments' | 'users' | 'courses' | 'reviews' | 'all', format: 'csv' | 'json' = 'csv') => {
    try {
      setExporting(true);
      
      switch (type) {
        case 'enrollments':
          await exportService.exportEnrollments({ format });
          break;
        case 'users':
          await exportService.exportUsers({ format });
          break;
        case 'courses':
          await exportService.exportCourses({ format });
          break;
        case 'reviews':
          await exportService.exportReviews({ format });
          break;
        case 'all':
          await exportService.exportAllData({ format });
          break;
      }
      
      toast({
        title: "Export Successful",
        description: `${type} data has been exported successfully`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  const handleSaveSettings = async (settingsType: 'platform' | 'payment' | 'email') => {
    try {
      setSaving(true);
      
      // In a real app, you would save to Firestore or your backend
      // For now, we'll just save to localStorage
      const settingsKey = `admin_${settingsType}_settings`;
      let settingsData;
      
      switch (settingsType) {
        case 'platform':
          settingsData = platformSettings;
          break;
        case 'payment':
          settingsData = paymentSettings;
          break;
        case 'email':
          settingsData = emailSettings;
          break;
      }
      
      localStorage.setItem(settingsKey, JSON.stringify(settingsData));
      
      toast({
        title: "Settings Saved",
        description: `${settingsType} settings have been saved successfully`,
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const loadSettings = () => {
    try {
      const platformData = localStorage.getItem('admin_platform_settings');
      const paymentData = localStorage.getItem('admin_payment_settings');
      const emailData = localStorage.getItem('admin_email_settings');
      
      if (platformData) {
        setPlatformSettings(JSON.parse(platformData));
      }
      if (paymentData) {
        setPaymentSettings(JSON.parse(paymentData));
      }
      if (emailData) {
        setEmailSettings(JSON.parse(emailData));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Admin Settings</h2>
          <p className="text-gray-400">Manage platform settings and export data</p>
        </div>
      </div>

      <Tabs defaultValue="platform" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-800">
          <TabsTrigger value="platform" className="text-gray-300 data-[state=active]:text-white">
            <Settings className="h-4 w-4 mr-2" />
            Platform
          </TabsTrigger>
          <TabsTrigger value="payment" className="text-gray-300 data-[state=active]:text-white">
            <CreditCard className="h-4 w-4 mr-2" />
            Payment
          </TabsTrigger>
          <TabsTrigger value="email" className="text-gray-300 data-[state=active]:text-white">
            <Mail className="h-4 w-4 mr-2" />
            Email
          </TabsTrigger>
          <TabsTrigger value="export" className="text-gray-300 data-[state=active]:text-white">
            <Download className="h-4 w-4 mr-2" />
            Export
          </TabsTrigger>
        </TabsList>

        {/* Platform Settings */}
        <TabsContent value="platform">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Platform Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-gray-300">Site Name</Label>
                  <Input
                    value={platformSettings.siteName}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteName: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">Contact Email</Label>
                  <Input
                    type="email"
                    value={platformSettings.contactEmail}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, contactEmail: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">Support Email</Label>
                  <Input
                    type="email"
                    value={platformSettings.supportEmail}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, supportEmail: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">Default Currency</Label>
                  <Select value={platformSettings.defaultCurrency} onValueChange={(value) => setPlatformSettings(prev => ({ ...prev, defaultCurrency: value }))}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-gray-300">Timezone</Label>
                  <Select value={platformSettings.timezone} onValueChange={(value) => setPlatformSettings(prev => ({ ...prev, timezone: value }))}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="Africa/Nairobi">Africa/Nairobi</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">America/New_York</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-gray-300">Max File Upload Size (MB)</Label>
                  <Input
                    type="number"
                    value={platformSettings.maxFileUploadSize}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, maxFileUploadSize: parseInt(e.target.value) }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
              </div>

              <div>
                <Label className="text-gray-300">Site Description</Label>
                <Textarea
                  value={platformSettings.siteDescription}
                  onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
                  className="bg-gray-700 border-gray-600 text-white"
                  rows={3}
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Platform Controls</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div>
                      <Label className="text-white">Maintenance Mode</Label>
                      <p className="text-sm text-gray-400">Disable site for maintenance</p>
                    </div>
                    <Switch
                      checked={platformSettings.maintenanceMode}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, maintenanceMode: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div>
                      <Label className="text-white">User Registration</Label>
                      <p className="text-sm text-gray-400">Allow new user registrations</p>
                    </div>
                    <Switch
                      checked={platformSettings.registrationEnabled}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, registrationEnabled: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div>
                      <Label className="text-white">Email Verification</Label>
                      <p className="text-sm text-gray-400">Require email verification</p>
                    </div>
                    <Switch
                      checked={platformSettings.emailVerificationRequired}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, emailVerificationRequired: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div>
                      <Label className="text-white">Auto-approve Reviews</Label>
                      <p className="text-sm text-gray-400">Automatically approve course reviews</p>
                    </div>
                    <Switch
                      checked={platformSettings.autoApproveReviews}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, autoApproveReviews: checked }))}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('platform')}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Save Platform Settings
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Settings */}
        <TabsContent value="payment">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Payment Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-400 mr-2" />
                  <span className="text-yellow-400 font-medium">Security Notice</span>
                </div>
                <p className="text-yellow-300 text-sm mt-1">
                  Keep your payment credentials secure. Never share them publicly.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-gray-300">Paystack Public Key</Label>
                  <Input
                    value={paymentSettings.paystackPublicKey}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, paystackPublicKey: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="pk_live_..."
                  />
                </div>
                <div>
                  <Label className="text-gray-300">Paystack Secret Key</Label>
                  <Input
                    type="password"
                    value={paymentSettings.paystackSecretKey}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, paystackSecretKey: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="sk_live_..."
                  />
                </div>
                <div>
                  <Label className="text-gray-300">Default Currency</Label>
                  <Select value={paymentSettings.defaultCurrency} onValueChange={(value) => setPaymentSettings(prev => ({ ...prev, defaultCurrency: value }))}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="NGN">NGN - Nigerian Naira</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-gray-300">Tax Rate (%)</Label>
                  <Input
                    type="number"
                    value={paymentSettings.taxRate}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, taxRate: parseFloat(e.target.value) }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <Label className="text-gray-300">Refund Policy</Label>
                <Textarea
                  value={paymentSettings.refundPolicy}
                  onChange={(e) => setPaymentSettings(prev => ({ ...prev, refundPolicy: e.target.value }))}
                  className="bg-gray-700 border-gray-600 text-white"
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <Label className="text-white">Test Mode</Label>
                  <p className="text-sm text-gray-400">Use test API keys for development</p>
                </div>
                <Switch
                  checked={paymentSettings.testMode}
                  onCheckedChange={(checked) => setPaymentSettings(prev => ({ ...prev, testMode: checked }))}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('payment')}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Save Payment Settings
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Email Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-gray-300">SMTP Host</Label>
                  <Input
                    value={emailSettings.smtpHost}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpHost: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">SMTP Port</Label>
                  <Input
                    type="number"
                    value={emailSettings.smtpPort}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPort: parseInt(e.target.value) }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="587"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">SMTP Username</Label>
                  <Input
                    value={emailSettings.smtpUsername}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpUsername: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">SMTP Password</Label>
                  <Input
                    type="password"
                    value={emailSettings.smtpPassword}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPassword: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="your-app-password"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">From Email</Label>
                  <Input
                    type="email"
                    value={emailSettings.fromEmail}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, fromEmail: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-gray-300">From Name</Label>
                  <Input
                    value={emailSettings.fromName}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, fromName: e.target.value }))}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <Label className="text-white">Email Notifications</Label>
                  <p className="text-sm text-gray-400">Enable automatic email notifications</p>
                </div>
                <Switch
                  checked={emailSettings.enableEmailNotifications}
                  onCheckedChange={(checked) => setEmailSettings(prev => ({ ...prev, enableEmailNotifications: checked }))}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('email')}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Save Email Settings
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Export Data */}
        <TabsContent value="export">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Individual Exports */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Database className="h-5 w-5 mr-2" />
                  Export Individual Data
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Button
                    onClick={() => handleExportData('enrollments')}
                    disabled={exporting}
                    className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Enrollments
                  </Button>
                  <Button
                    onClick={() => handleExportData('users')}
                    disabled={exporting}
                    className="w-full justify-start bg-purple-600 hover:bg-purple-700 text-white font-medium disabled:opacity-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Users
                  </Button>
                  <Button
                    onClick={() => handleExportData('courses')}
                    disabled={exporting}
                    className="w-full justify-start bg-green-600 hover:bg-green-700 text-white font-medium disabled:opacity-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Courses
                  </Button>
                  <Button
                    onClick={() => handleExportData('reviews')}
                    disabled={exporting}
                    className="w-full justify-start bg-orange-600 hover:bg-orange-700 text-white font-medium disabled:opacity-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Reviews
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Complete Export */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Complete Platform Export
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-400 text-sm">
                  Export all platform data including users, courses, enrollments, and reviews in a single file.
                </p>
                <div className="space-y-3">
                  <Button
                    onClick={() => handleExportData('all', 'json')}
                    disabled={exporting}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50"
                  >
                    {exporting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Exporting...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Export All Data (JSON)
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={() => handleExportData('all', 'csv')}
                    disabled={exporting}
                    variant="outline"
                    className="w-full border-blue-500 text-blue-400 hover:bg-blue-600 hover:text-white font-medium disabled:opacity-50"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export All Data (CSV)
                  </Button>
                </div>
                
                <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-4 mt-4">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-blue-400 mr-2" />
                    <span className="text-blue-400 font-medium">Export Information</span>
                  </div>
                  <ul className="text-blue-300 text-sm mt-2 space-y-1">
                    <li>• CSV format is suitable for spreadsheet applications</li>
                    <li>• JSON format preserves data structure and relationships</li>
                    <li>• Large exports may take a few moments to process</li>
                    <li>• Files are downloaded directly to your device</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
