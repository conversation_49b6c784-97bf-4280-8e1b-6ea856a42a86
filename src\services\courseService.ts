import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '@/lib/firebase';
import { Course, CourseModule, Lesson, Assignment, Quiz } from '@/types/course';
import {
  coursesService,
  modulesService,
  lessonsService,
  assignmentsService,
  quizzesService,
  enrollmentsService
} from '@/services/firestoreService';

// Collection names
const COLLECTIONS = {
  COURSES: 'courses',
  MODULES: 'modules', 
  LESSONS: 'lessons',
  ASSIGNMENTS: 'assignments',
  QUIZZES: 'quizzes',
  USER_PROGRESS: 'userProgress',
  ENROLLMENTS: 'enrollments'
};

// Course Management
export const courseService = {
  // Create a new course
  async createCourse(courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.COURSES), {
        ...courseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isPublished: false,
        enrollmentCount: 0,
        rating: 0,
        reviewCount: 0
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating course:', error);
      throw error;
    }
  },

  // Get all courses (published and unpublished)
  async getAllCourses(): Promise<Course[]> {
    try {
      const snapshot = await getDocs(collection(db, COLLECTIONS.COURSES));

      if (snapshot.empty) {
        return [];
      }

      const allCourses = snapshot.docs.map(doc => {
        const data = doc.data();

        // Handle different field name variations for compatibility
        const isPublished = data.isPublished !== false && data.published !== false;

        const course = {
          id: doc.id,
          title: data.title || 'Untitled Course',
          description: data.description || 'No description available',
          shortDescription: data.shortDescription || data.description || 'No description available',
          instructor: data.instructor || 'Ahmed Takal',
          instructorId: data.instructorId || 'ahmed-takal',
          level: (data.level || data.skillLevel || 'Beginner') as 'Beginner' | 'Intermediate' | 'Advanced',
          category: data.category || 'FlutterFlow Development',
          price: Number(data.price) || 0,
          originalPrice: data.originalPrice ? Number(data.originalPrice) : undefined,
          currency: data.currency || 'KES',
          duration: data.duration || '8 weeks',
          totalDuration: Number(data.totalDuration) || 480,
          thumbnail: data.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
          videoUrl: data.videoUrl,
          learningOutcomes: Array.isArray(data.learningOutcomes) ? data.learningOutcomes.filter(Boolean) : [
            'Build professional mobile apps with FlutterFlow',
            'Master advanced UI/UX design principles',
            'Integrate APIs and backend services'
          ],
          requirements: Array.isArray(data.requirements) ? data.requirements : ['Basic computer skills'],
          targetAudience: Array.isArray(data.targetAudience) ? data.targetAudience : ['Beginners', 'Entrepreneurs'],
          tags: Array.isArray(data.tags) ? data.tags.filter(Boolean) : ['FlutterFlow', 'Mobile Development'],
          isPublished: isPublished,
          published: isPublished, // For backward compatibility
          featured: data.featured || false,
          status: data.status || (isPublished ? 'published' : 'draft'),
          enrollmentCount: Number(data.enrollmentCount || data.enrollments) || 0,
          enrolledCount: Number(data.enrolledCount) || 0,
          rating: Number(data.rating) || 0,
          reviewCount: Number(data.reviewCount || data.reviews) || 0,
          modules: Array.isArray(data.modules) ? data.modules : [],
          allowComments: data.allowComments !== false,
          allowDownloads: data.allowDownloads !== false,
          certificateEnabled: data.certificateEnabled !== false,
          accessType: data.accessType || 'lifetime',
          accessDuration: data.accessDuration,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : new Date(),
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date(),
        } as Course;

        return course;
      });

      return allCourses;
    } catch (error) {
      throw error;
    }
  },

  // Get all published courses
  async getPublishedCourses(): Promise<Course[]> {
    try {
      // Get all courses without any filtering first
      const snapshot = await getDocs(collection(db, COLLECTIONS.COURSES));

      if (snapshot.empty) {
        return [];
      }

      const allCourses = snapshot.docs.map(doc => {
        const data = doc.data();

        // Handle different field name variations for compatibility
        const isPublished = data.isPublished !== false && data.published !== false;

        const processedCourse = {
          id: doc.id,
          title: data.title || 'Untitled Course',
          description: data.description || 'No description available',
          shortDescription: data.shortDescription || data.description || 'No description available',
          instructor: data.instructor || 'Ahmed Takal',
          instructorId: data.instructorId || 'ahmed-takal',
          level: (data.level || data.skillLevel || 'Beginner') as 'Beginner' | 'Intermediate' | 'Advanced',
          category: data.category || 'FlutterFlow Development',
          price: Number(data.price) || 0,
          originalPrice: data.originalPrice ? Number(data.originalPrice) : undefined,
          currency: data.currency || 'KES',
          duration: data.duration || '8 weeks',
          totalDuration: Number(data.totalDuration) || 480,
          thumbnail: data.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
          videoUrl: data.videoUrl,
          learningOutcomes: Array.isArray(data.learningOutcomes) ? data.learningOutcomes.filter(Boolean) : [
            'Build professional mobile apps with FlutterFlow',
            'Master advanced UI/UX design principles',
            'Integrate APIs and backend services'
          ],
          requirements: Array.isArray(data.requirements) ? data.requirements : ['Basic computer skills'],
          targetAudience: Array.isArray(data.targetAudience) ? data.targetAudience : ['Beginners', 'Entrepreneurs'],
          tags: Array.isArray(data.tags) ? data.tags.filter(Boolean) : ['FlutterFlow', 'Mobile Development'],
          isPublished: isPublished,
          published: isPublished, // For backward compatibility
          featured: data.featured || false,
          status: data.status || (isPublished ? 'published' : 'draft'),
          enrollmentCount: Number(data.enrollmentCount || data.enrollments) || 0,
          enrolledCount: Number(data.enrolledCount) || 0,
          rating: Number(data.rating) || 0,
          reviewCount: Number(data.reviewCount || data.reviews) || 0,
          modules: Array.isArray(data.modules) ? data.modules : [],
          allowComments: data.allowComments !== false,
          allowDownloads: data.allowDownloads !== false,
          certificateEnabled: data.certificateEnabled !== false,
          accessType: data.accessType || 'lifetime',
          accessDuration: data.accessDuration,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : new Date(),
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date(),
        };

        return processedCourse;
      }) as Course[];

      // Filter for published courses - check both isPublished and published fields for compatibility
      const publishedCourses = allCourses.filter(course =>
        course.isPublished === true || (course as any).published === true
      );

      // Return published courses, or all courses if none are marked as published
      if (publishedCourses.length === 0 && allCourses.length > 0) {
        return allCourses;
      }

      return publishedCourses;
    } catch (error) {
      return []; // Return empty array instead of throwing
    }
  },

  // Get course by ID with lessons (new simplified structure)
  async getCourseWithModules(courseId: string): Promise<Course | null> {
    try {
      console.log('🔍 Loading course with lessons for ID:', courseId);

      // Get course directly from Firestore
      const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
      const courseSnap = await getDoc(courseRef);

      if (!courseSnap.exists()) {
        console.log('❌ Course not found:', courseId);
        return null;
      }

      const courseData = { id: courseSnap.id, ...courseSnap.data() };
      console.log('📊 Raw course data:', courseData);

      // Handle both old module structure and new direct lessons structure
      let modules = [];

      if (Array.isArray(courseData.lessons) && courseData.lessons.length > 0) {
        // NEW STRUCTURE: Direct lessons in course document
        console.log('✅ Found direct lessons in course:', courseData.lessons.length);
        modules = [{
          id: `${courseData.id}-main-module`,
          courseId: courseData.id,
          title: 'Course Content',
          description: 'Main course content',
          order: 1,
          lessons: courseData.lessons.map((lesson: any, index: number) => ({
            id: lesson.id || `lesson-${index + 1}`,
            moduleId: `${courseData.id}-main-module`,
            title: lesson.title || `Lesson ${index + 1}`,
            description: lesson.description || '',
            type: lesson.type || 'video',
            order: lesson.order || index + 1,
            duration: lesson.duration || 60,
            videoUrl: lesson.videoUrl || lesson.url || '',
            textContent: lesson.textContent || '',
            resources: lesson.resources || [],
            isFree: lesson.isFree || false,
            isPreview: lesson.isPreview || false,
            isCompleted: lesson.isCompleted || false,
            watchTime: lesson.watchTime || 0
          })),
          assignments: [],
          quizzes: [],
          isLocked: false
        }];
      } else if (Array.isArray(courseData.modules) && courseData.modules.length > 0) {
        // OLD STRUCTURE: Embedded modules
        console.log('✅ Found embedded modules:', courseData.modules.length);
        modules = courseData.modules;
      } else {
        // FALLBACK: Create module from course videoUrl
        console.log('⚠️ No lessons or modules found, checking for videoUrl');
        if (courseData.videoUrl) {
          console.log('✅ Creating fallback module from videoUrl');
          modules = [{
            id: `${courseData.id}-fallback-module`,
            courseId: courseData.id,
            title: 'Course Video',
            description: 'Main course video content',
            order: 1,
            lessons: [{
              id: `${courseData.id}-main-lesson`,
              moduleId: `${courseData.id}-fallback-module`,
              title: courseData.title || 'Course Video',
              description: courseData.description || 'Main course video',
              type: 'video',
              order: 1,
              duration: courseData.totalDuration || 60,
              videoUrl: courseData.videoUrl,
              textContent: '',
              resources: [],
              isFree: false,
              isPreview: false,
              isCompleted: false,
              watchTime: 0
            }],
            assignments: [],
            quizzes: [],
            isLocked: false
          }];
        }
      }

      const processedCourse = {
        id: courseData.id,
        title: courseData.title || 'Untitled Course',
        description: courseData.description || 'No description available',
        shortDescription: courseData.shortDescription || '',
        instructor: courseData.instructor || 'Ahmed Takal',
        instructorId: courseData.instructorId || 'ahmed-takal',
        instructorAvatar: courseData.instructorAvatar || '',
        instructorBio: courseData.instructorBio || '',
        level: courseData.level || 'Beginner',
        category: courseData.category || 'Programming',
        price: Number(courseData.price) || 0,
        originalPrice: courseData.originalPrice ? Number(courseData.originalPrice) : undefined,
        currency: courseData.currency || 'KES',
        duration: courseData.duration || '8 weeks',
        totalDuration: Number(courseData.totalDuration) || 480,
        thumbnail: courseData.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500',
        videoUrl: courseData.videoUrl || '',
        requirements: Array.isArray(courseData.requirements) ? courseData.requirements : [
          'Basic understanding of mobile apps',
          'Computer with internet connection'
        ],
        learningOutcomes: Array.isArray(courseData.learningOutcomes) ? courseData.learningOutcomes.filter(Boolean) : [
          'Learn new skills and concepts',
          'Build practical projects',
          'Gain industry knowledge'
        ],
        tags: Array.isArray(courseData.tags) ? courseData.tags.filter(Boolean) : ['Course', 'Learning'],
        targetAudience: Array.isArray(courseData.targetAudience) ? courseData.targetAudience : [],
        modules: modules, // Use processed modules
        isPublished: courseData.isPublished !== false,
        featured: courseData.featured || false,
        status: courseData.status || (courseData.isPublished ? 'published' : 'draft'),
        enrollmentCount: Number(courseData.enrollmentCount) || 1200,
        enrolledCount: Number(courseData.enrolledCount) || 0,
        maxEnrollments: courseData.maxEnrollments,
        rating: Number(courseData.rating) || 4.8,
        reviewCount: Number(courseData.reviewCount) || 150,
        allowComments: courseData.allowComments !== false,
        allowDownloads: courseData.allowDownloads !== false,
        certificateEnabled: courseData.certificateEnabled !== false,
        accessType: courseData.accessType || 'lifetime',
        accessDuration: courseData.accessDuration,
        createdAt: courseData.createdAt?.toDate ? courseData.createdAt.toDate() : new Date(),
        updatedAt: courseData.updatedAt?.toDate ? courseData.updatedAt.toDate() : new Date(),
      } as Course;

      console.log('✅ Processed course:', {
        id: processedCourse.id,
        title: processedCourse.title,
        moduleCount: processedCourse.modules.length,
        firstModuleLessons: processedCourse.modules[0]?.lessons?.length || 0,
        firstLessonVideoUrl: processedCourse.modules[0]?.lessons?.[0]?.videoUrl || 'No video URL'
      });

      return processedCourse;
    } catch (error) {
      console.error('❌ Error fetching course with modules:', error);
      return null;
    }
  },

  // Update course
  async updateCourse(courseId: string, updates: Partial<Course>): Promise<void> {
    try {
      await updateDoc(doc(db, COLLECTIONS.COURSES, courseId), {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  },

  // Delete course and all related data
  async deleteCourse(courseId: string): Promise<void> {
    try {
      const batch = writeBatch(db);

      // Delete course
      batch.delete(doc(db, COLLECTIONS.COURSES, courseId));

      // Get and delete modules
      const modulesQuery = query(
        collection(db, COLLECTIONS.MODULES),
        where('courseId', '==', courseId)
      );
      const modulesSnapshot = await getDocs(modulesQuery);
      
      for (const moduleDoc of modulesSnapshot.docs) {
        batch.delete(moduleDoc.ref);
        
        // Delete lessons, assignments, quizzes for each module
        const moduleId = moduleDoc.id;
        
        const [lessonsSnapshot, assignmentsSnapshot, quizzesSnapshot] = await Promise.all([
          getDocs(query(collection(db, COLLECTIONS.LESSONS), where('moduleId', '==', moduleId))),
          getDocs(query(collection(db, COLLECTIONS.ASSIGNMENTS), where('moduleId', '==', moduleId))),
          getDocs(query(collection(db, COLLECTIONS.QUIZZES), where('moduleId', '==', moduleId)))
        ]);

        lessonsSnapshot.docs.forEach(doc => batch.delete(doc.ref));
        assignmentsSnapshot.docs.forEach(doc => batch.delete(doc.ref));
        quizzesSnapshot.docs.forEach(doc => batch.delete(doc.ref));
      }

      await batch.commit();
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  }
};

// Module Management
export const moduleService = {
  async createModule(moduleData: Omit<CourseModule, 'id' | 'lessons' | 'assignments' | 'quizzes'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.MODULES), moduleData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating module:', error);
      throw error;
    }
  },

  async updateModule(moduleId: string, updates: Partial<CourseModule>): Promise<void> {
    try {
      await updateDoc(doc(db, COLLECTIONS.MODULES, moduleId), updates);
    } catch (error) {
      console.error('Error updating module:', error);
      throw error;
    }
  },

  async deleteModule(moduleId: string): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      // Delete module
      batch.delete(doc(db, COLLECTIONS.MODULES, moduleId));
      
      // Delete all lessons, assignments, quizzes in this module
      const [lessonsSnapshot, assignmentsSnapshot, quizzesSnapshot] = await Promise.all([
        getDocs(query(collection(db, COLLECTIONS.LESSONS), where('moduleId', '==', moduleId))),
        getDocs(query(collection(db, COLLECTIONS.ASSIGNMENTS), where('moduleId', '==', moduleId))),
        getDocs(query(collection(db, COLLECTIONS.QUIZZES), where('moduleId', '==', moduleId)))
      ]);

      lessonsSnapshot.docs.forEach(doc => batch.delete(doc.ref));
      assignmentsSnapshot.docs.forEach(doc => batch.delete(doc.ref));
      quizzesSnapshot.docs.forEach(doc => batch.delete(doc.ref));

      await batch.commit();
    } catch (error) {
      console.error('Error deleting module:', error);
      throw error;
    }
  }
};

// Lesson Management
export const lessonService = {
  async createLesson(lessonData: Omit<Lesson, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.LESSONS), lessonData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating lesson:', error);
      throw error;
    }
  },

  async updateLesson(lessonId: string, updates: Partial<Lesson>): Promise<void> {
    try {
      await updateDoc(doc(db, COLLECTIONS.LESSONS, lessonId), updates);
    } catch (error) {
      console.error('Error updating lesson:', error);
      throw error;
    }
  },

  async deleteLesson(lessonId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.LESSONS, lessonId));
    } catch (error) {
      console.error('Error deleting lesson:', error);
      throw error;
    }
  }
};

// Assignment Management
export const assignmentService = {
  async createAssignment(assignmentData: Omit<Assignment, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.ASSIGNMENTS), assignmentData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating assignment:', error);
      throw error;
    }
  },

  async updateAssignment(assignmentId: string, updates: Partial<Assignment>): Promise<void> {
    try {
      await updateDoc(doc(db, COLLECTIONS.ASSIGNMENTS, assignmentId), updates);
    } catch (error) {
      console.error('Error updating assignment:', error);
      throw error;
    }
  },

  async deleteAssignment(assignmentId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.ASSIGNMENTS, assignmentId));
    } catch (error) {
      console.error('Error deleting assignment:', error);
      throw error;
    }
  }
};

// Quiz Management
export const quizService = {
  async createQuiz(quizData: Omit<Quiz, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.QUIZZES), quizData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating quiz:', error);
      throw error;
    }
  },

  async updateQuiz(quizId: string, updates: Partial<Quiz>): Promise<void> {
    try {
      await updateDoc(doc(db, COLLECTIONS.QUIZZES, quizId), updates);
    } catch (error) {
      console.error('Error updating quiz:', error);
      throw error;
    }
  },

  async deleteQuiz(quizId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.QUIZZES, quizId));
    } catch (error) {
      console.error('Error deleting quiz:', error);
      throw error;
    }
  }
};

// User Enrollment
export const enrollmentService = {
  async enrollUser(userId: string, courseId: string): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.ENROLLMENTS), {
        userId,
        courseId,
        enrolledAt: serverTimestamp(),
        status: 'active'
      });

      // Update course enrollment count
      const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
      const courseDoc = await getDoc(courseRef);
      if (courseDoc.exists()) {
        const currentCount = courseDoc.data().enrollmentCount || 0;
        await updateDoc(courseRef, {
          enrollmentCount: currentCount + 1
        });
      }
    } catch (error) {
      console.error('Error enrolling user:', error);
      throw error;
    }
  },

  async getUserEnrollments(userId: string): Promise<string[]> {
    try {
      console.log('🔍 CourseService: Getting enrollments for user:', userId);

      const enrollmentIds = new Set<string>();

      // Check by user ID (with or without status)
      const userIdQuery = query(
        collection(db, COLLECTIONS.ENROLLMENTS),
        where('userId', '==', userId)
      );
      const userIdSnapshot = await getDocs(userIdQuery);
      userIdSnapshot.docs.forEach(doc => {
        enrollmentIds.add(doc.data().courseId);
      });

      // Also check by email for email-based enrollments
      try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const userEmail = userData.email;

          if (userEmail) {
            // Check by email as userId
            const emailQuery = query(
              collection(db, COLLECTIONS.ENROLLMENTS),
              where('userId', '==', userEmail)
            );
            const emailSnapshot = await getDocs(emailQuery);
            emailSnapshot.docs.forEach(doc => {
              enrollmentIds.add(doc.data().courseId);
            });

            // Check by originalEmail field
            const originalEmailQuery = query(
              collection(db, COLLECTIONS.ENROLLMENTS),
              where('originalEmail', '==', userEmail)
            );
            const originalEmailSnapshot = await getDocs(originalEmailQuery);
            originalEmailSnapshot.docs.forEach(doc => {
              enrollmentIds.add(doc.data().courseId);
            });
          }
        }
      } catch (emailCheckError) {
        console.log('⚠️ CourseService: Could not check email-based enrollments:', emailCheckError);
      }

      const enrollments = Array.from(enrollmentIds);
      console.log('✅ CourseService: Found', enrollments.length, 'enrollments');
      return enrollments;
    } catch (error) {
      console.error('❌ CourseService: Error fetching user enrollments:', error);
      throw error;
    }
  },

  async isUserEnrolled(userId: string, courseId: string): Promise<boolean> {
    try {
      console.log('🔍 CourseService: Checking enrollment for user:', userId, 'course:', courseId);

      // First check with user ID (with or without status)
      const userIdQuery = query(
        collection(db, COLLECTIONS.ENROLLMENTS),
        where('userId', '==', userId),
        where('courseId', '==', courseId)
      );
      const userIdSnapshot = await getDocs(userIdQuery);

      if (!userIdSnapshot.empty) {
        console.log('✅ CourseService: Found enrollment by user ID');
        return true;
      }

      // If not found by user ID, check by email (for email-based enrollments)
      try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const userEmail = userData.email;

          if (userEmail) {
            console.log('🔍 CourseService: Checking enrollment by email:', userEmail);

            // Check by email as userId
            const emailQuery = query(
              collection(db, COLLECTIONS.ENROLLMENTS),
              where('userId', '==', userEmail),
              where('courseId', '==', courseId)
            );
            const emailSnapshot = await getDocs(emailQuery);

            if (!emailSnapshot.empty) {
              console.log('✅ CourseService: Found enrollment by email');
              return true;
            }

            // Check by originalEmail field
            const originalEmailQuery = query(
              collection(db, COLLECTIONS.ENROLLMENTS),
              where('originalEmail', '==', userEmail),
              where('courseId', '==', courseId)
            );
            const originalEmailSnapshot = await getDocs(originalEmailQuery);

            if (!originalEmailSnapshot.empty) {
              console.log('✅ CourseService: Found enrollment by original email');
              return true;
            }
          }
        }
      } catch (emailCheckError) {
        console.log('⚠️ CourseService: Could not check email-based enrollment:', emailCheckError);
      }

      console.log('❌ CourseService: No enrollment found');
      return false;
    } catch (error) {
      console.error('❌ CourseService: Error checking enrollment:', error);
      return false;
    }
  },

  async enrollUserWithPayment(userId: string, courseId: string, paymentData: any): Promise<void> {
    try {
      // Create enrollment with payment information
      await addDoc(collection(db, COLLECTIONS.ENROLLMENTS), {
        userId,
        courseId,
        enrolledAt: serverTimestamp(),
        status: 'active',
        paymentInfo: {
          transactionId: paymentData.transaction_id,
          reference: paymentData.reference,
          amount: paymentData.amount,
          currency: paymentData.currency,
          paymentMethod: paymentData.payment_method,
          paidAt: serverTimestamp()
        }
      });

      // Update course enrollment count
      const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
      const courseDoc = await getDoc(courseRef);
      if (courseDoc.exists()) {
        const currentCount = courseDoc.data().enrollmentCount || 0;
        await updateDoc(courseRef, {
          enrollmentCount: currentCount + 1
        });
      }

      console.log('User enrolled with payment successfully');
    } catch (error) {
      console.error('Error enrolling user with payment:', error);
      throw error;
    }
  }
};
