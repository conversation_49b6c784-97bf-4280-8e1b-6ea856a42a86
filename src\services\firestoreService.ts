import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  writeBatch,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Check if Firestore is available
const isFirestoreAvailable = () => {
  if (!db) {
    console.warn('Firestore is not available. Some features may not work.');
    return false;
  }
  return true;
};

// Firestore Collections
export const COLLECTIONS = {
  COURSES: 'courses',
  MODULES: 'modules',
  LESSONS: 'lessons',
  ASSIGNMENTS: 'assignments',
  QUIZZES: 'quizzes',
  USERS: 'users',
  ENROLLMENTS: 'enrollments'
} as const;

// Generic Firestore Service
export class FirestoreService {
  // Get all documents from a collection
  static async getAll<T>(collectionName: string): Promise<T[]> {
    try {
      console.log(`🔍 Loading all documents from ${collectionName}...`);
      const snapshot = await getDocs(collection(db, collectionName));
      
      const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
      
      console.log(`✅ Loaded ${documents.length} documents from ${collectionName}`);
      return documents;
    } catch (error) {
      console.error(`❌ Error loading ${collectionName}:`, error);
      throw error;
    }
  }

  // Get document by ID
  static async getById<T>(collectionName: string, id: string): Promise<T | null> {
    try {
      console.log(`🔍 Loading document ${id} from ${collectionName}...`);
      const docRef = doc(db, collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const document = { id: docSnap.id, ...docSnap.data() } as T;
        console.log(`✅ Document found:`, document);
        return document;
      } else {
        console.log(`❌ Document ${id} not found in ${collectionName}`);
        return null;
      }
    } catch (error) {
      console.error(`❌ Error loading document ${id} from ${collectionName}:`, error);
      throw error;
    }
  }

  // Query documents with conditions
  static async query<T>(
    collectionName: string, 
    conditions: Array<{ field: string; operator: any; value: any }> = [],
    orderByField?: string,
    orderDirection: 'asc' | 'desc' = 'asc'
  ): Promise<T[]> {
    try {
      console.log(`🔍 Querying ${collectionName} with conditions:`, conditions);
      
      let q = collection(db, collectionName);
      let queryRef: any = q;

      // Add where conditions
      conditions.forEach(condition => {
        queryRef = query(queryRef, where(condition.field, condition.operator, condition.value));
      });

      // Add ordering
      if (orderByField) {
        queryRef = query(queryRef, orderBy(orderByField, orderDirection));
      }

      const snapshot = await getDocs(queryRef);
      const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
      
      console.log(`✅ Query returned ${documents.length} documents from ${collectionName}`);
      return documents;
    } catch (error) {
      console.error(`❌ Error querying ${collectionName}:`, error);
      throw error;
    }
  }

  // Create new document
  static async create<T>(collectionName: string, data: Omit<T, 'id'>): Promise<string> {
    try {
      console.log(`📝 Creating document in ${collectionName}:`, data);
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log(`✅ Document created with ID: ${docRef.id}`);
      return docRef.id;
    } catch (error) {
      console.error(`❌ Error creating document in ${collectionName}:`, error);
      throw error;
    }
  }

  // Update document
  static async update<T>(collectionName: string, id: string, data: Partial<T>): Promise<void> {
    try {
      console.log(`📝 Updating document ${id} in ${collectionName}:`, data);
      const docRef = doc(db, collectionName, id);
      await updateDoc(docRef, {
        ...data,
        updatedAt: serverTimestamp()
      });
      console.log(`✅ Document ${id} updated in ${collectionName}`);
    } catch (error) {
      console.error(`❌ Error updating document ${id} in ${collectionName}:`, error);
      throw error;
    }
  }

  // Delete document
  static async delete(collectionName: string, id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting document ${id} from ${collectionName}`);
      const docRef = doc(db, collectionName, id);
      await deleteDoc(docRef);
      console.log(`✅ Document ${id} deleted from ${collectionName}`);
    } catch (error) {
      console.error(`❌ Error deleting document ${id} from ${collectionName}:`, error);
      throw error;
    }
  }

  // Real-time listener
  static subscribe<T>(
    collectionName: string,
    callback: (documents: T[]) => void,
    conditions: Array<{ field: string; operator: any; value: any }> = []
  ): Unsubscribe {
    try {
      console.log(`👂 Setting up real-time listener for ${collectionName}`);
      
      let q = collection(db, collectionName);
      let queryRef: any = q;

      // Add where conditions
      conditions.forEach(condition => {
        queryRef = query(queryRef, where(condition.field, condition.operator, condition.value));
      });

      return onSnapshot(queryRef, (snapshot) => {
        const documents = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as T[];
        
        console.log(`🔄 Real-time update: ${documents.length} documents in ${collectionName}`);
        callback(documents);
      }, (error) => {
        console.error(`❌ Real-time listener error for ${collectionName}:`, error);
      });
    } catch (error) {
      console.error(`❌ Error setting up listener for ${collectionName}:`, error);
      throw error;
    }
  }

  // Batch operations
  static async batchWrite(operations: Array<{
    type: 'create' | 'update' | 'delete';
    collection: string;
    id?: string;
    data?: any;
  }>): Promise<void> {
    try {
      console.log(`📦 Executing batch write with ${operations.length} operations`);
      const batch = writeBatch(db);

      operations.forEach(operation => {
        switch (operation.type) {
          case 'create':
            const createRef = doc(collection(db, operation.collection));
            batch.set(createRef, {
              ...operation.data,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            });
            break;
          case 'update':
            if (operation.id) {
              const updateRef = doc(db, operation.collection, operation.id);
              batch.update(updateRef, {
                ...operation.data,
                updatedAt: serverTimestamp()
              });
            }
            break;
          case 'delete':
            if (operation.id) {
              const deleteRef = doc(db, operation.collection, operation.id);
              batch.delete(deleteRef);
            }
            break;
        }
      });

      await batch.commit();
      console.log(`✅ Batch write completed successfully`);
    } catch (error) {
      console.error(`❌ Error in batch write:`, error);
      throw error;
    }
  }
}

// Collection-specific services
export const coursesService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.COURSES),
  getById: (id: string) => FirestoreService.getById(COLLECTIONS.COURSES, id),
  getPublished: () => FirestoreService.query(COLLECTIONS.COURSES, [
    { field: 'isPublished', operator: '==', value: true }
  ]),
  create: (data: any) => FirestoreService.create(COLLECTIONS.COURSES, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.COURSES, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.COURSES, id),
  subscribe: (callback: (courses: any[]) => void) =>
    FirestoreService.subscribe(COLLECTIONS.COURSES, callback, [
      { field: 'isPublished', operator: '==', value: true }
    ]),
  subscribeAll: (callback: (courses: any[]) => void) =>
    FirestoreService.subscribe(COLLECTIONS.COURSES, callback)
};

export const modulesService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.MODULES),
  getByCourse: async (courseId: string) => {
    const modules = await FirestoreService.query(COLLECTIONS.MODULES, [
      { field: 'courseId', operator: '==', value: courseId }
    ]);
    // Sort by order field in memory to avoid index requirement
    return modules.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
  },
  create: (data: any) => FirestoreService.create(COLLECTIONS.MODULES, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.MODULES, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.MODULES, id)
};

export const lessonsService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.LESSONS),
  getByModule: (moduleId: string) => FirestoreService.query(COLLECTIONS.LESSONS, [
    { field: 'moduleId', operator: '==', value: moduleId }
  ], 'order'),
  create: (data: any) => FirestoreService.create(COLLECTIONS.LESSONS, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.LESSONS, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.LESSONS, id)
};

export const assignmentsService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.ASSIGNMENTS),
  getByModule: (moduleId: string) => FirestoreService.query(COLLECTIONS.ASSIGNMENTS, [
    { field: 'moduleId', operator: '==', value: moduleId }
  ]),
  create: (data: any) => FirestoreService.create(COLLECTIONS.ASSIGNMENTS, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.ASSIGNMENTS, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.ASSIGNMENTS, id)
};

export const quizzesService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.QUIZZES),
  getByModule: (moduleId: string) => FirestoreService.query(COLLECTIONS.QUIZZES, [
    { field: 'moduleId', operator: '==', value: moduleId }
  ]),
  create: (data: any) => FirestoreService.create(COLLECTIONS.QUIZZES, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.QUIZZES, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.QUIZZES, id)
};

export const usersService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.USERS),
  getById: (id: string) => FirestoreService.getById(COLLECTIONS.USERS, id),
  create: (data: any) => FirestoreService.create(COLLECTIONS.USERS, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.USERS, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.USERS, id)
};

export const enrollmentsService = {
  getAll: () => FirestoreService.getAll(COLLECTIONS.ENROLLMENTS),
  getByUser: (userId: string) => FirestoreService.query(COLLECTIONS.ENROLLMENTS, [
    { field: 'userId', operator: '==', value: userId }
  ]),
  getByCourse: (courseId: string) => FirestoreService.query(COLLECTIONS.ENROLLMENTS, [
    { field: 'courseId', operator: '==', value: courseId }
  ]),
  create: (data: any) => FirestoreService.create(COLLECTIONS.ENROLLMENTS, data),
  update: (id: string, data: any) => FirestoreService.update(COLLECTIONS.ENROLLMENTS, id, data),
  delete: (id: string) => FirestoreService.delete(COLLECTIONS.ENROLLMENTS, id)
};
