/**
 * FreeCodeLap Platform - Email Notification Modal
 * 
 * Mo<PERSON> for composing and sending emails to users
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Mail,
  Send,
  Users,
  Eye,
  FileText,
  Filter,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { UserRole } from '@/types/user';
import {
  emailNotificationService,
  EmailTemplate,
  EmailRecipientFilter,
  EmailSendResult
} from '@/services/emailNotificationService';
import { simpleEmailService } from '@/services/simpleEmailService';
import { useAuth } from '@/contexts/AuthContext';

interface EmailNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEmailSent: () => void;
  preselectedUserIds?: string[];
  initialFilter?: EmailRecipientFilter;
}

export const EmailNotificationModal: React.FC<EmailNotificationModalProps> = ({
  isOpen,
  onClose,
  onEmailSent,
  preselectedUserIds = [],
  initialFilter
}) => {
  const { toast } = useToast();
  const { userProfile } = useAuth();
  
  // Form state
  const [subject, setSubject] = useState('');
  const [htmlContent, setHtmlContent] = useState('');
  const [textContent, setTextContent] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [customVariables, setCustomVariables] = useState<Record<string, string>>({});
  
  // Recipient filters
  const [recipientFilter, setRecipientFilter] = useState<EmailRecipientFilter>({
    status: 'all',
    role: 'all',
    enrollmentStatus: 'all',
    customUserIds: preselectedUserIds
  });
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState<any>(null);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [recipientCount, setRecipientCount] = useState(0);
  const [currentTab, setCurrentTab] = useState<'compose' | 'template'>('compose');
  const [emailMethod, setEmailMethod] = useState<'client' | 'individual'>('client');

  useEffect(() => {
    if (isOpen) {
      loadTemplates();

      // Set initial filter if provided
      if (initialFilter) {
        console.log('📧 Setting initial filter:', initialFilter);
        setRecipientFilter(initialFilter);
      } else if (preselectedUserIds.length > 0) {
        console.log('📧 Setting preselected user IDs:', preselectedUserIds);
        setRecipientFilter(prev => ({
          ...prev,
          customUserIds: preselectedUserIds
        }));
      }
    }
  }, [isOpen, initialFilter, preselectedUserIds]);

  useEffect(() => {
    if (isOpen) {
      updateRecipientCount();
    }
  }, [isOpen, recipientFilter]);

  const loadTemplates = () => {
    const availableTemplates = emailNotificationService.getEmailTemplates();
    setTemplates(availableTemplates);
  };

  const updateRecipientCount = async () => {
    try {
      console.log('🔍 Updating recipient count with filter:', recipientFilter);
      const users = await emailNotificationService.getFilteredUsers(recipientFilter);
      console.log(`📊 Found ${users.length} users matching criteria`);
      setRecipientCount(users.length);
    } catch (error) {
      console.error('Error getting recipient count:', error);
      setRecipientCount(0);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setSubject(template.subject);
      setHtmlContent(template.htmlContent);
      setTextContent(template.textContent);
      
      // Initialize custom variables for this template
      const variables: Record<string, string> = {};
      template.variables.forEach(variable => {
        if (!['name', 'email', 'firstName', 'lastName', 'role', 'platformUrl'].includes(variable)) {
          variables[variable] = '';
        }
      });
      setCustomVariables(variables);
    }
  };

  const handlePreview = () => {
    const validation = emailNotificationService.validateEmailContent(subject, htmlContent || textContent);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.errors.join(', '),
        variant: "destructive",
      });
      return;
    }

    const preview = emailNotificationService.previewEmail(
      subject,
      htmlContent,
      textContent,
      customVariables
    );
    setPreviewContent(preview);
    setShowPreview(true);
  };

  const handleSendEmail = async () => {
    if (!userProfile) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    const validation = emailNotificationService.validateEmailContent(subject, htmlContent || textContent);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.errors.join(', '),
        variant: "destructive",
      });
      return;
    }

    if (recipientCount === 0) {
      toast({
        title: "No Recipients",
        description: "No users match the selected criteria",
        variant: "destructive",
      });
      return;
    }

    const confirmMessage = `Are you sure you want to send this email to ${recipientCount} user(s)?`;
    if (!confirm(confirmMessage)) return;

    try {
      setLoading(true);
      console.log('📧 Starting email send process...');
      console.log('📧 Recipient filter:', recipientFilter);
      console.log('📧 Subject:', subject);
      console.log('📧 Content length:', (htmlContent || textContent).length);
      console.log('📧 Email method:', emailMethod);

      // Get filtered users
      const users = await emailNotificationService.getFilteredUsers(recipientFilter);
      console.log(`📧 Found ${users.length} users to email`);

      if (users.length === 0) {
        toast({
          title: "No Recipients",
          description: "No users found matching the criteria",
          variant: "destructive",
        });
        return;
      }

      // Process template variables
      const processedSubject = subject.replace(/{{name}}/g, users[0]?.displayName || 'User')
                                     .replace(/{{email}}/g, users[0]?.email || '')
                                     .replace(/{{platformUrl}}/g, window.location.origin);

      const processedContent = (htmlContent || textContent)
                                .replace(/{{name}}/g, users[0]?.displayName || 'User')
                                .replace(/{{email}}/g, users[0]?.email || '')
                                .replace(/{{platformUrl}}/g, window.location.origin);

      let result: any;

      if (emailMethod === 'individual') {
        console.log('📧 Sending individual emails via email client');
        result = await simpleEmailService.sendIndividualEmails(
          users,
          processedSubject,
          processedContent,
          userProfile.uid,
          userProfile.displayName || 'Admin'
        );
      } else {
        console.log('📧 Sending bulk email via email client');
        result = await simpleEmailService.sendEmailsViaClient(
          users,
          processedSubject,
          processedContent,
          userProfile.uid,
          userProfile.displayName || 'Admin'
        );
      }

      console.log('📧 Email send result:', result);

      if (result.sentCount > 0) {
        toast({
          title: "Email Sent Successfully",
          description: `Sent to ${result.sentCount} users${result.failedCount > 0 ? `, ${result.failedCount} failed` : ''}`,
        });
      } else {
        toast({
          title: "Email Send Issue",
          description: `${result.sentCount} sent, ${result.failedCount} failed. ${result.errors.join(', ')}`,
          variant: "destructive",
        });
      }

      onEmailSent();
      onClose();
      resetForm();
    } catch (error: any) {
      console.error('❌ Email send error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to send email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSubject('');
    setHtmlContent('');
    setTextContent('');
    setSelectedTemplate('');
    setCustomVariables({});
    setRecipientFilter({
      status: 'all',
      role: 'all',
      enrollmentStatus: 'all',
      customUserIds: []
    });
    setCurrentTab('compose');
    setEmailMethod('client');
    setShowPreview(false);
    setPreviewContent(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <>
      {/* Main Email Modal */}
      <Dialog open={isOpen && !showPreview} onOpenChange={handleClose}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Send Email Notification
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-700 p-1 rounded-lg">
              <button
                type="button"
                onClick={() => setCurrentTab('compose')}
                className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentTab === 'compose'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-600'
                }`}
              >
                <Mail className="h-4 w-4 mr-2" />
                Compose
              </button>
              <button
                type="button"
                onClick={() => setCurrentTab('template')}
                className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentTab === 'template'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-600'
                }`}
              >
                <FileText className="h-4 w-4 mr-2" />
                Templates
              </button>
            </div>

            {/* Recipients Section */}
            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Recipients ({recipientCount} users)
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-gray-300">User Status</Label>
                  <Select
                    value={recipientFilter.status}
                    onValueChange={(value) => {
                      console.log('📧 Status filter changed to:', value);
                      setRecipientFilter(prev => ({ ...prev, status: value as any }));
                    }}
                  >
                    <SelectTrigger className="bg-gray-600 border-gray-500 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="active">Active Users</SelectItem>
                      <SelectItem value="inactive">Inactive Users</SelectItem>
                      <SelectItem value="suspended">Suspended Users</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-gray-300">User Role</Label>
                  <Select
                    value={recipientFilter.role}
                    onValueChange={(value) => {
                      console.log('📧 Role filter changed to:', value);
                      setRecipientFilter(prev => ({ ...prev, role: value as any }));
                    }}
                  >
                    <SelectTrigger className="bg-gray-600 border-gray-500 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      <SelectItem value="student">Students</SelectItem>
                      <SelectItem value="admin">Admins</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-gray-300">Enrollment Status</Label>
                  <Select
                    value={recipientFilter.enrollmentStatus}
                    onValueChange={(value) => {
                      console.log('📧 Enrollment filter changed to:', value);
                      setRecipientFilter(prev => ({ ...prev, enrollmentStatus: value as any }));
                    }}
                  >
                    <SelectTrigger className="bg-gray-600 border-gray-500 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="enrolled">Enrolled in Courses</SelectItem>
                      <SelectItem value="not_enrolled">Not Enrolled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {preselectedUserIds.length > 0 && (
                <div className="mt-4">
                  <Badge className="bg-blue-600 text-white">
                    {preselectedUserIds.length} specific user(s) selected
                  </Badge>
                </div>
              )}
            </div>

            {/* Email Method Selection */}
            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Email Method
              </h3>

              <div className="space-y-3">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="emailMethod"
                    value="client"
                    checked={emailMethod === 'client'}
                    onChange={(e) => setEmailMethod(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <p className="text-white font-medium">Open Email Client (Recommended)</p>
                    <p className="text-gray-400 text-sm">Opens your default email app with all recipients</p>
                  </div>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="emailMethod"
                    value="individual"
                    checked={emailMethod === 'individual'}
                    onChange={(e) => setEmailMethod(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <p className="text-white font-medium">Individual Emails</p>
                    <p className="text-gray-400 text-sm">Opens separate email windows for each recipient</p>
                  </div>
                </label>
              </div>
            </div>

            {/* Template Tab Content */}
            {currentTab === 'template' && (
              <div className="space-y-4">
                <div>
                  <Label className="text-gray-300">Select Template</Label>
                  <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue placeholder="Choose a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedTemplate && (
                  <div className="space-y-4">
                    <div>
                      <Label className="text-gray-300">Subject</Label>
                      <Input
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        className="bg-gray-700 border-gray-600 text-white"
                        placeholder="Email subject"
                      />
                    </div>

                    {/* Custom Variables */}
                    {Object.keys(customVariables).length > 0 && (
                      <div>
                        <Label className="text-gray-300">Template Variables</Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                          {Object.entries(customVariables).map(([key, value]) => (
                            <div key={key}>
                              <Label className="text-gray-400 text-sm">{key}</Label>
                              <Input
                                value={value}
                                onChange={(e) => setCustomVariables(prev => ({ ...prev, [key]: e.target.value }))}
                                className="bg-gray-600 border-gray-500 text-white"
                                placeholder={`Enter ${key}`}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Compose Tab Content */}
            {currentTab === 'compose' && (
              <div className="space-y-4">
                <div>
                  <Label className="text-gray-300">Subject *</Label>
                  <Input
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="Email subject"
                    required
                  />
                </div>

                <div>
                  <Label className="text-gray-300">HTML Content</Label>
                  <Textarea
                    value={htmlContent}
                    onChange={(e) => setHtmlContent(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white min-h-[200px]"
                    placeholder="HTML email content (optional - will use text content if empty)"
                  />
                </div>

                <div>
                  <Label className="text-gray-300">Text Content *</Label>
                  <Textarea
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white min-h-[150px]"
                    placeholder="Plain text email content"
                    required
                  />
                </div>

                <div className="bg-gray-700 p-3 rounded-lg">
                  <p className="text-gray-300 text-sm mb-2">Available Variables:</p>
                  <div className="flex flex-wrap gap-2">
                    {['{{name}}', '{{email}}', '{{firstName}}', '{{lastName}}', '{{role}}', '{{platformUrl}}'].map((variable) => (
                      <Badge key={variable} className="bg-gray-600 text-gray-300 text-xs">
                        {variable}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePreview}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  disabled={!subject || (!htmlContent && !textContent)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSendEmail}
                  disabled={loading || !subject || (!htmlContent && !textContent) || recipientCount === 0}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send to {recipientCount} users
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      <Dialog open={showPreview} onOpenChange={() => setShowPreview(false)}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Email Preview
            </DialogTitle>
          </DialogHeader>

          {previewContent && (
            <div className="space-y-4">
              <div>
                <Label className="text-gray-300">Subject:</Label>
                <p className="text-white bg-gray-700 p-2 rounded">{previewContent.subject}</p>
              </div>

              <div>
                <Label className="text-gray-300">HTML Content:</Label>
                <div
                  className="bg-white p-4 rounded border max-h-96 overflow-y-auto"
                  dangerouslySetInnerHTML={{ __html: previewContent.htmlContent }}
                />
              </div>

              <div>
                <Label className="text-gray-300">Text Content:</Label>
                <pre className="text-gray-300 bg-gray-700 p-4 rounded whitespace-pre-wrap max-h-48 overflow-y-auto">
                  {previewContent.textContent}
                </pre>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              onClick={() => setShowPreview(false)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Close Preview
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
