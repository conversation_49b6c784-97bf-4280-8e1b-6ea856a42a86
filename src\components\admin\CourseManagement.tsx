import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Course } from '@/types/course';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Star,
  Users,
  BookOpen,
  Video,
  Settings,
  Save,
  X,
  Loader2
} from 'lucide-react';

interface CourseFormData {
  title: string;
  description: string;
  shortDescription: string;
  thumbnail: string;
  videoUrl: string;
  category: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  price: number;
  originalPrice: number;
  duration: string;
  tags: string;
  requirements: string;
  learningOutcomes: string;
  targetAudience: string;
  featured: boolean;
  allowComments: boolean;
  allowDownloads: boolean;
  certificateEnabled: boolean;
}

interface CourseManagementProps {
  courses: Course[];
  onToggleStatus: (courseId: string, currentStatus: boolean) => void;
  onDeleteCourse: (courseId: string) => void;
  onEditCourse: (courseId: string) => void;
}

const CourseManagement: React.FC<CourseManagementProps> = ({
  courses,
  onToggleStatus,
  onDeleteCourse,
  onEditCourse
}) => {
  const { currentUser, isAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);

  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    shortDescription: '',
    thumbnail: '',
    videoUrl: '',
    category: '',
    level: 'Beginner',
    price: 0,
    originalPrice: 0,
    duration: '',
    tags: '',
    requirements: '',
    learningOutcomes: '',
    targetAudience: '',
    featured: false,
    allowComments: true,
    allowDownloads: true,
    certificateEnabled: true
  });

  const handleInputChange = (field: keyof CourseFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      shortDescription: '',
      thumbnail: '',
      videoUrl: '',
      category: '',
      level: 'Beginner',
      price: 0,
      originalPrice: 0,
      duration: '',
      tags: '',
      requirements: '',
      learningOutcomes: '',
      targetAudience: '',
      featured: false,
      allowComments: true,
      allowDownloads: true,
      certificateEnabled: true
    });
    setEditingCourse(null);
    setShowCreateForm(false);
  };

  const handleCreateCourse = () => {
    setShowCreateForm(true);
    resetForm();
  };

  const handleEditCourse = (course: Course) => {
    setEditingCourse(course);
    setFormData({
      title: course.title,
      description: course.description,
      shortDescription: course.shortDescription || '',
      thumbnail: course.thumbnail,
      videoUrl: course.videoUrl || '',
      category: course.category,
      level: course.level,
      price: course.price,
      originalPrice: course.originalPrice || 0,
      duration: course.duration,
      tags: course.tags.join(', '),
      requirements: course.requirements.join('\n'),
      learningOutcomes: course.learningOutcomes.join('\n'),
      targetAudience: course.targetAudience.join('\n'),
      featured: course.featured,
      allowComments: course.allowComments,
      allowDownloads: course.allowDownloads,
      certificateEnabled: course.certificateEnabled
    });
    setShowCreateForm(true);
  };

  const handleSaveCourse = async () => {
    try {
      setSaving(true);
      setError(null);

      // Validate required fields
      if (!formData.title || !formData.description || !formData.category) {
        setError('Please fill in all required fields');
        return;
      }

      // Create course object
      const courseData: Partial<Course> = {
        title: formData.title,
        description: formData.description,
        shortDescription: formData.shortDescription,
        thumbnail: formData.thumbnail,
        videoUrl: formData.videoUrl,
        category: formData.category,
        level: formData.level,
        price: formData.price,
        originalPrice: formData.originalPrice,
        duration: formData.duration,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        requirements: formData.requirements.split('\n').filter(Boolean),
        learningOutcomes: formData.learningOutcomes.split('\n').filter(Boolean),
        targetAudience: formData.targetAudience.split('\n').filter(Boolean),
        featured: formData.featured,
        allowComments: formData.allowComments,
        allowDownloads: formData.allowDownloads,
        certificateEnabled: formData.certificateEnabled,
        instructor: 'Ahmed Takal',
        instructorId: 'ahmed-takal',
        currency: 'USD',
        modules: [],
        enrollmentCount: 0,
        enrolledCount: 0,
        rating: 0,
        reviewCount: 0,
        isPublished: true,
        status: 'published',
        accessType: 'lifetime',
        updatedAt: new Date()
      };

      if (editingCourse) {
        // Update existing course
        // TODO: Implement update functionality
        setSuccess('Course updated successfully!');
      } else {
        // Create new course
        courseData.id = `course-${Date.now()}`;
        courseData.createdAt = new Date();
        // TODO: Implement create functionality
        setSuccess('Course created successfully!');
      }

      resetForm();
      // Course list will be updated by parent component
    } catch (error) {
      console.error('Error saving course:', error);
      setError('Failed to save course');
    } finally {
      setSaving(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold mb-2">Access Denied</h2>
            <p className="text-gray-400">You don't have permission to access the admin panel.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Course Management</h1>
            <p className="text-gray-400">Manage your courses, modules, and content</p>
          </div>
          <Button onClick={handleCreateCourse} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Create Course
          </Button>
        </div>

        {error && (
          <Alert className="bg-red-900/20 border-red-700 mb-6">
            <AlertDescription className="text-red-300">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-900/20 border-green-700 mb-6">
            <AlertDescription className="text-green-300">{success}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="courses" className="space-y-6">
          <TabsList className="bg-gray-800 border-gray-700">
            <TabsTrigger value="courses" className="data-[state=active]:bg-blue-600">
              <BookOpen className="h-4 w-4 mr-2" />
              Courses
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-600">
              <Users className="h-4 w-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-blue-600">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="courses">
            {showCreateForm && (
              <Card className="bg-gray-800 border-gray-700 mb-6">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white">
                      {editingCourse ? 'Edit Course' : 'Create New Course'}
                    </CardTitle>
                    <Button variant="ghost" onClick={resetForm}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Course Title *</label>
                      <Input
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="Enter course title"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Category *</label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-700 border-gray-600">
                          <SelectItem value="No-Code Development">No-Code Development</SelectItem>
                          <SelectItem value="AI Development">AI Development</SelectItem>
                          <SelectItem value="Web Development">Web Development</SelectItem>
                          <SelectItem value="Mobile Development">Mobile Development</SelectItem>
                          <SelectItem value="Data Science">Data Science</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Description *</label>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Enter course description"
                      className="bg-gray-700 border-gray-600 text-white"
                      rows={4}
                    />
                  </div>

                  <div className="grid md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Level</label>
                      <Select value={formData.level} onValueChange={(value: any) => handleInputChange('level', value)}>
                        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-700 border-gray-600">
                          <SelectItem value="Beginner">Beginner</SelectItem>
                          <SelectItem value="Intermediate">Intermediate</SelectItem>
                          <SelectItem value="Advanced">Advanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Price (USD)</label>
                      <Input
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', Number(e.target.value))}
                        placeholder="0"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Duration</label>
                      <Input
                        value={formData.duration}
                        onChange={(e) => handleInputChange('duration', e.target.value)}
                        placeholder="e.g., 8 weeks"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <Button onClick={handleSaveCourse} disabled={saving} className="bg-green-600 hover:bg-green-700">
                      {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      {editingCourse ? 'Update Course' : 'Create Course'}
                    </Button>
                    <Button variant="outline" onClick={resetForm}>
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Courses List */}
            <div className="grid gap-6">
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-400">Loading courses...</p>
                </div>
              ) : courses.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-400 mb-2">No courses found</h3>
                  <p className="text-gray-500">Create your first course to get started</p>
                </div>
              ) : (
                courses.map((course) => (
                  <Card key={course.id} className="bg-gray-800 border-gray-700">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-semibold text-white">{course.title}</h3>
                            <Badge variant={course.isPublished ? "default" : "secondary"}>
                              {course.isPublished ? 'Published' : 'Draft'}
                            </Badge>
                            {course.featured && (
                              <Badge className="bg-yellow-600">
                                <Star className="h-3 w-3 mr-1" />
                                Featured
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-400 mb-4">{course.description}</p>
                          <div className="flex items-center gap-6 text-sm text-gray-500">
                            <span>Category: {course.category}</span>
                            <span>Level: {course.level}</span>
                            <span>Price: ${course.price.toLocaleString()}</span>
                            <span>Students: {course.enrolledCount}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onToggleStatus(course.id, course.isPublished)}
                            title={course.isPublished ? "Unpublish" : "Publish"}
                          >
                            {course.isPublished ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditCourse(course.id)}
                            title="Edit Course"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                            onClick={() => onDeleteCourse(course.id)}
                            title="Delete Course"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Course Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400">Analytics dashboard coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Course Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400">Course settings coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CourseManagement;
