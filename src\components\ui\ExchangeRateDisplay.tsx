/**
 * Exchange Rate Display Component
 * Shows USD prices with local currency equivalent
 */

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { useUSDConversion, useSimpleCurrency } from '@/contexts/SimpleCurrencyContext';
import { Info, DollarSign } from 'lucide-react';

interface ExchangeRateDisplayProps {
  usdAmount: number;
  variant?: 'default' | 'compact' | 'detailed' | 'checkout';
  showUSDOnly?: boolean;
  className?: string;
}

export const ExchangeRateDisplay: React.FC<ExchangeRateDisplayProps> = ({
  usdAmount,
  variant = 'default',
  showUSDOnly = false,
  className = ''
}) => {
  const { userCurrency, formatCurrency, getCurrencyInfo } = useSimpleCurrency();
  const { convertedAmount, loading, error } = useUSDConversion(usdAmount);

  // Loading state
  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Skeleton className="h-6 w-20" />
        {!showUSDOnly && <Skeleton className="h-4 w-16" />}
      </div>
    );
  }

  // USD only or error state
  if (showUSDOnly || error || userCurrency === 'USD' || !convertedAmount) {
    return (
      <div className={`${className}`}>
        <span className="text-lg font-bold text-white">
          ${usdAmount.toFixed(2)} USD
        </span>
        {error && (
          <Badge variant="destructive" className="ml-2 text-xs">
            Rate Error
          </Badge>
        )}
      </div>
    );
  }

  const currencyInfo = getCurrencyInfo(userCurrency);
  const localFormatted = formatCurrency(convertedAmount, userCurrency);
  const exchangeRate = convertedAmount / usdAmount;

  // Compact variant for course cards
  if (variant === 'compact') {
    return (
      <div className={`space-y-1 ${className}`}>
        <div className="text-lg font-bold text-white">
          ${usdAmount.toFixed(2)} USD
        </div>
        <div className="text-sm text-gray-400">
          ≈ {localFormatted}
        </div>
      </div>
    );
  }

  // Detailed variant for course detail pages
  if (variant === 'detailed') {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-green-400" />
          <span className="text-2xl font-bold text-white">
            ${usdAmount.toFixed(2)} USD
          </span>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 space-y-2">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Info className="h-4 w-4" />
            <span>Local equivalent (for reference)</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{currencyInfo?.flag}</span>
              <span className="text-lg font-semibold text-white">
                {localFormatted}
              </span>
            </div>
            <Badge variant="outline" className="text-xs">
              1 USD = {exchangeRate.toFixed(2)} {userCurrency}
            </Badge>
          </div>
          
          <p className="text-xs text-gray-500">
            Exchange rate updated hourly. Payment will be processed in USD.
          </p>
        </div>
      </div>
    );
  }

  // Checkout variant for payment pages
  if (variant === 'checkout') {
    return (
      <div className={`bg-gray-800 rounded-lg p-4 space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <span className="text-gray-400">Course Price:</span>
          <span className="text-xl font-bold text-white">
            ${usdAmount.toFixed(2)} USD
          </span>
        </div>
        
        {userCurrency !== 'USD' && (
          <>
            <div className="border-t border-gray-700 pt-3 space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <Info className="h-4 w-4" />
                <span>Local equivalent (reference only)</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span>{currencyInfo?.flag}</span>
                  <span className="font-medium text-white">
                    {localFormatted}
                  </span>
                </div>
                <span className="text-sm text-gray-400">
                  Rate: {exchangeRate.toFixed(4)}
                </span>
              </div>
            </div>
            
            <div className="bg-blue-900/20 border border-blue-600 rounded p-3">
              <p className="text-xs text-blue-300">
                💳 You will be charged <strong>${usdAmount.toFixed(2)} USD</strong> regardless of your local currency.
                The {currencyInfo?.name} amount is shown for reference only.
              </p>
            </div>
          </>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={`space-y-1 ${className}`}>
      <div className="text-lg font-bold text-white">
        ${usdAmount.toFixed(2)} USD
      </div>
      
      <div className="flex items-center space-x-2 text-sm text-gray-400">
        <span>≈ {localFormatted}</span>
        <Badge variant="outline" className="text-xs">
          {currencyInfo?.flag} {userCurrency}
        </Badge>
      </div>
    </div>
  );
};

/**
 * Simple price component for basic use cases
 */
export const SimpleUSDPrice: React.FC<{
  usdAmount: number;
  className?: string;
}> = ({ usdAmount, className = '' }) => {
  return (
    <ExchangeRateDisplay
      usdAmount={usdAmount}
      variant="compact"
      className={className}
    />
  );
};

/**
 * Currency selector for user preferences
 */
export const CurrencySelector: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const { userCurrency, supportedCurrencies, setUserCurrency, loading } = useSimpleCurrency();

  if (loading) {
    return <Skeleton className="h-8 w-24" />;
  }

  return (
    <div className={`relative ${className}`}>
      <select
        value={userCurrency}
        onChange={(e) => setUserCurrency(e.target.value)}
        className="bg-gray-800 border border-gray-600 rounded px-3 py-1 text-white text-sm focus:outline-none focus:border-blue-500"
      >
        <option value="USD">🇺🇸 USD</option>
        {supportedCurrencies.map((currency) => (
          <option key={currency.code} value={currency.code}>
            {currency.flag} {currency.code}
          </option>
        ))}
      </select>
      
      <div className="absolute -bottom-6 left-0 text-xs text-gray-500">
        Display currency
      </div>
    </div>
  );
};

/**
 * Exchange rate info component
 */
export const ExchangeRateInfo: React.FC<{
  usdAmount: number;
  className?: string;
}> = ({ usdAmount, className = '' }) => {
  const { userCurrency, getCurrencyInfo } = useSimpleCurrency();
  const { convertedAmount, loading } = useUSDConversion(usdAmount);

  if (loading || userCurrency === 'USD' || !convertedAmount) {
    return null;
  }

  const currencyInfo = getCurrencyInfo(userCurrency);
  const exchangeRate = convertedAmount / usdAmount;

  return (
    <div className={`bg-gray-800 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2 text-sm text-gray-400 mb-2">
        <Info className="h-4 w-4" />
        <span>Exchange Rate Information</span>
      </div>
      
      <div className="space-y-1 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-400">Base Currency:</span>
          <span className="text-white">USD (US Dollar)</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Your Currency:</span>
          <span className="text-white">
            {currencyInfo?.flag} {currencyInfo?.name}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Exchange Rate:</span>
          <span className="text-white">
            1 USD = {exchangeRate.toFixed(4)} {userCurrency}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Last Updated:</span>
          <span className="text-white">
            {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>
      
      <p className="text-xs text-gray-500 mt-2">
        Rates are updated hourly. All payments are processed in USD.
      </p>
    </div>
  );
};
