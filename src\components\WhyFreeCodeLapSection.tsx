import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { 
  Brain, 
  Code, 
  Cpu, 
  Clock, 
  Shield, 
  Award,
  Users,
  BookOpen,
  Zap,
  Heart
} from 'lucide-react';

const WhyFreeCodeLapSection = () => {
  const benefits = [
    {
      icon: <Brain className="h-12 w-12 text-blue-500" />,
      title: "Beginner Friendly",
      description: "Start from zero and build your skills step by step with clear explanations and hands-on projects. No prior coding experience required.",
      gradient: "from-blue-500 to-blue-600"
    },
    {
      icon: <Code className="h-12 w-12 text-green-500" />,
      title: "Real Projects",
      description: "Build actual applications and add them to your portfolio. Learn by doing with practical, industry-relevant projects.",
      gradient: "from-green-500 to-green-600"
    },
    {
      icon: <Cpu className="h-12 w-12 text-purple-500" />,
      title: "No-Code + AI",
      description: "Learn modern development with FlutterFlow and AI-powered coding tools. Stay ahead with cutting-edge technology.",
      gradient: "from-purple-500 to-purple-600"
    },
    {
      icon: <Clock className="h-12 w-12 text-orange-500" />,
      title: "Lifetime Access",
      description: "Learn at your own pace with permanent access to all course materials, updates, and new content additions.",
      gradient: "from-orange-500 to-orange-600"
    },
    {
      icon: <Shield className="h-12 w-12 text-red-500" />,
      title: "Paystack Secure Payment",
      description: "Safe and secure payments with multiple payment options including M-Pesa, cards, and bank transfers.",
      gradient: "from-red-500 to-red-600"
    },
    {
      icon: <Award className="h-12 w-12 text-yellow-500" />,
      title: "Certificate",
      description: "Earn a certificate of completion to showcase your new skills and boost your career prospects.",
      gradient: "from-yellow-500 to-yellow-600"
    }
  ];

  return (
    <section className="py-20 bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Why Choose FreeCodeLap?
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Join thousands of students who have transformed their careers with our comprehensive, 
            practical approach to modern development
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gray-900 border-gray-700 overflow-hidden relative"
            >
              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${benefit.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
              
              <CardHeader className="text-center pb-4 relative z-10">
                <div className="flex justify-center mb-4">
                  <div className={`p-4 rounded-full bg-gradient-to-br ${benefit.gradient} bg-opacity-20 group-hover:scale-110 transition-transform duration-300`}>
                    {benefit.icon}
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-white group-hover:text-blue-400 transition-colors">
                  {benefit.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="text-center relative z-10">
                <p className="text-gray-300 leading-relaxed">
                  {benefit.description}
                </p>
              </CardContent>

              {/* Hover Effect Border */}
              <div className={`absolute inset-0 border-2 border-transparent group-hover:border-gradient-to-r group-hover:${benefit.gradient} rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
            </Card>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="text-4xl font-bold text-blue-400 mb-2">2</div>
            <div className="text-gray-300">Comprehensive Courses</div>
          </div>
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="text-4xl font-bold text-green-400 mb-2">83+</div>
            <div className="text-gray-300">Total Lessons</div>
          </div>
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="text-4xl font-bold text-purple-400 mb-2">421+</div>
            <div className="text-gray-300">Happy Students</div>
          </div>
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="text-4xl font-bold text-orange-400 mb-2">4.8/5</div>
            <div className="text-gray-300">Average Rating</div>
          </div>
        </div>

        {/* Testimonial Preview */}
        <div className="mt-20 text-center">
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-700 max-w-4xl mx-auto">
            <div className="flex justify-center mb-4">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-6 h-6 fill-current" viewBox="0 0 20 20">
                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                  </svg>
                ))}
              </div>
            </div>
            <blockquote className="text-xl text-gray-300 italic mb-4">
              "FreeCodeLap transformed my career completely. I went from knowing nothing about app development 
              to building my first mobile app in just 6 weeks. The FlutterFlow course is incredibly comprehensive!"
            </blockquote>
            <div className="text-white font-semibold">Sarah Wanjiku</div>
            <div className="text-gray-400">Mobile App Developer, Nairobi</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyFreeCodeLapSection;
