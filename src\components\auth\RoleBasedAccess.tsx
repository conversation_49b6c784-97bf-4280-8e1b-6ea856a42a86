import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/user';
import { RoleService } from '@/services/roleService';

interface RoleBasedAccessProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredRole?: UserRole;
  requireAdmin?: boolean;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * Component that conditionally renders content based on user role
 */
export const RoleBasedAccess: React.FC<RoleBasedAccessProps> = ({
  children,
  allowedRoles,
  requiredRole,
  requireAdmin = false,
  fallback = null,
  showFallback = false
}) => {
  const { userProfile, isAdmin } = useAuth();

  // If no user profile, don't show content
  if (!userProfile) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin()) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Check specific role requirement
  if (requiredRole && userProfile.role !== requiredRole) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Check allowed roles
  if (allowedRoles && !allowedRoles.includes(userProfile.role)) {
    return showFallback ? <>{fallback}</> : null;
  }

  // If all checks pass, render children
  return <>{children}</>;
};

/**
 * Component that only shows content to admin users
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  return (
    <RoleBasedAccess
      requireAdmin={true}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </RoleBasedAccess>
  );
};

/**
 * Component that only shows content to student users
 */
export const StudentOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  return (
    <RoleBasedAccess
      requiredRole="student"
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </RoleBasedAccess>
  );
};

/**
 * Hook to check user permissions
 */
export const usePermissions = () => {
  const { userProfile, isAdmin } = useAuth();

  return {
    canAccessAdminPanel: isAdmin(),
    canManageCourses: userProfile ? RoleService.canManageCourses(userProfile.role) : false,
    canViewAnalytics: userProfile ? RoleService.canViewAnalytics(userProfile.role) : false,
    canManageUsers: userProfile ? RoleService.canManageUsers(userProfile.role) : false,
    canAccessCourseContent: userProfile ? RoleService.canAccessCourseContent(userProfile.role) : false,
    isAdmin: isAdmin(),
    isStudent: userProfile?.role === 'student',
    userRole: userProfile?.role,
    roleDisplayName: userProfile ? RoleService.getRoleDisplayName(userProfile.role) : 'Unknown',
    rolePermissions: userProfile ? RoleService.getRolePermissions(userProfile.role) : []
  };
};

/**
 * Component to display user role badge
 */
export const RoleBadge: React.FC<{
  role?: UserRole;
  className?: string;
}> = ({ role, className = '' }) => {
  const { userProfile } = useAuth();
  const displayRole = role || userProfile?.role;

  if (!displayRole) return null;

  const getBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'student':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getBadgeColor(
        displayRole
      )} ${className}`}
    >
      {RoleService.getRoleDisplayName(displayRole)}
    </span>
  );
};

/**
 * Component to display access denied message
 */
export const AccessDenied: React.FC<{
  message?: string;
  showRole?: boolean;
}> = ({ 
  message = "You don't have permission to access this content.",
  showRole = true 
}) => {
  const { userProfile } = useAuth();

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="text-6xl mb-4">🔒</div>
      <h2 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h2>
      <p className="text-gray-600 mb-4">{message}</p>
      {showRole && userProfile && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Your role:</span>
          <RoleBadge />
        </div>
      )}
    </div>
  );
};

export default RoleBasedAccess;
