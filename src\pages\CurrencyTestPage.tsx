/**
 * Currency Test Page
 * Demonstrates the simplified USD-based currency system
 */

import React from 'react';
import { PaystackPayment } from '@/components/payment/PaystackPayment';
import { useSimpleCurrency } from '@/contexts/SimpleCurrencyContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DollarSign,
  Globe,
  CreditCard,
  Smartphone,
  TestTube,
  ArrowRight
} from 'lucide-react';

const CurrencyTestPage: React.FC = () => {
  const { userCurrency, supportedCurrencies, loading } = useSimpleCurrency();

  // Sample course data
  const sampleCourse = {
    id: 'course-123',
    title: 'React Fundamentals',
    price: 49.99, // USD
    thumbnail: 'https://via.placeholder.com/300x200?text=React+Course',
    instructor: '<PERSON>'
  };

  const sampleCourses = [
    { id: '1', title: 'React Fundamentals', price: 49.99 },
    { id: '2', title: 'Node.js Backend', price: 79.99 },
    { id: '3', title: 'Full Stack Development', price: 129.99 },
    { id: '4', title: 'Mobile App Development', price: 99.99 }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">Loading currency system...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-white flex items-center justify-center">
            <TestTube className="h-8 w-8 mr-3 text-blue-400" />
            Minimal Split Checkout Demo
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Clean, minimal split design that fits in one canvas without scrolling.
            Supports all 195 countries with auto-detection and clean pay button.
          </p>

          <div className="flex items-center justify-center space-x-4">
            <Badge variant="default" className="bg-blue-600">
              <DollarSign className="h-3 w-3 mr-1" />
              Minimal Design
            </Badge>
            <Badge variant="outline">
              <Globe className="h-3 w-3 mr-1" />
              195 Countries
            </Badge>
            <Badge variant="secondary">
              No Scrolling
            </Badge>
          </div>
        </div>

        {/* System Overview */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              How It Works
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-400" />
                <h4 className="font-medium text-white mb-1">Minimal Design</h4>
                <p className="text-gray-400">Less detail, clean layout</p>
              </div>
              <div className="text-center">
                <Globe className="h-8 w-8 mx-auto mb-2 text-blue-400" />
                <h4 className="font-medium text-white mb-1">All Countries</h4>
                <p className="text-gray-400">195 countries worldwide</p>
              </div>
              <div className="text-center">
                <CreditCard className="h-8 w-8 mx-auto mb-2 text-purple-400" />
                <h4 className="font-medium text-white mb-1">Clean Pay Button</h4>
                <p className="text-gray-400">Clear button after selection</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Course Cards - USD Only */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Course Catalog (USD Pricing)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {sampleCourses.map((course, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4 space-y-3">
                  <div className="h-32 bg-gray-600 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-sm">Course Thumbnail</span>
                  </div>
                  <h3 className="font-medium text-white">{course.title}</h3>
                  <div className="text-xl font-bold text-green-400">
                    ${course.price.toFixed(2)} USD
                  </div>
                  <Button size="sm" className="w-full">
                    Buy This Course
                  </Button>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <p className="text-gray-400 text-sm">
                💡 All courses are priced in USD. Local currency comparison shown on checkout page.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Minimal Split Checkout Demo */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-white text-center">
            Minimal Split Checkout Demo
          </h2>
          <p className="text-gray-400 text-center max-w-2xl mx-auto">
            Clean, minimal split design that fits in one canvas without scrolling.
            All 195 countries with auto-detection and clean pay button.
          </p>

          <div className="bg-gray-800 rounded-lg overflow-hidden p-6">
            <PaystackPayment
              userId="demo-user-123"
              courseId={sampleCourse.id}
              courseTitle={sampleCourse.title}
              courseInstructor={sampleCourse.instructor}
              amount={sampleCourse.price}
              customerEmail="<EMAIL>"
              customerName="Demo User"
              onSuccess={(enrollmentId) => {
                alert(`Demo: Payment successful!\n\nEnrollment ID: ${enrollmentId}`);
              }}
              onError={(error) => {
                alert(`Demo: Payment failed!\n\nError: ${error}`);
              }}
              onCancel={() => {
                alert('Demo: Payment cancelled by user');
              }}
            />
          </div>
        </div>

        {/* Instructions */}
        <Card className="bg-blue-900/20 border-blue-600">
          <CardHeader>
            <CardTitle className="text-blue-400">System Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2 text-blue-300">
              <div className="flex items-center space-x-2">
                <ArrowRight className="h-4 w-4" />
                <span>Minimal split design - fits in one canvas without scrolling</span>
              </div>
              <div className="flex items-center space-x-2">
                <ArrowRight className="h-4 w-4" />
                <span>All 195 countries in the world with auto-detection</span>
              </div>
              <div className="flex items-center space-x-2">
                <ArrowRight className="h-4 w-4" />
                <span>Clean pay button after selecting card or M-Pesa</span>
              </div>
              <div className="flex items-center space-x-2">
                <ArrowRight className="h-4 w-4" />
                <span>Less detailed, removed unnecessary information</span>
              </div>
              <div className="flex items-center space-x-2">
                <ArrowRight className="h-4 w-4" />
                <span>Desktop split screen with mobile responsive design</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-green-900/20 border border-green-600 rounded-lg">
              <p className="text-green-300 text-sm">
                ✅ <strong>System Status:</strong> Minimal split checkout ready! Clean design that fits in one canvas
                without scrolling, with all 195 countries and clean pay button.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CurrencyTestPage;
