/**
 * Debug utility for payment flow issues
 */

import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { enrollmentService } from '@/services/enrollmentService';

export const debugPaymentFlow = async (userId: string, courseId: string) => {
  console.log('🔍 DEBUG: Payment Flow Issue');
  console.log('============================');
  console.log('User ID:', userId);
  console.log('Course ID:', courseId);
  console.log('');

  try {
    // Step 1: Check user profile
    console.log('👤 Step 1: Checking user profile...');
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('✅ User found:', {
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role
      });
    } else {
      console.log('❌ User profile not found');
      return;
    }

    // Step 2: Check course exists
    console.log('');
    console.log('📚 Step 2: Checking course...');
    const courseDoc = await getDoc(doc(db, 'courses', courseId));
    if (courseDoc.exists()) {
      const courseData = courseDoc.data();
      console.log('✅ Course found:', {
        title: courseData.title,
        price: courseData.price,
        published: courseData.published
      });
    } else {
      console.log('❌ Course not found');
      return;
    }

    // Step 3: Check enrollment status
    console.log('');
    console.log('📋 Step 3: Checking enrollment status...');
    const isEnrolled = await enrollmentService.isUserEnrolled(userId, courseId);
    console.log('📊 Enrollment status:', isEnrolled);

    // Step 4: Check all enrollments for this course
    console.log('');
    console.log('📋 Step 4: Checking all enrollments for course...');
    const enrollmentsRef = collection(db, 'enrollments');
    const courseEnrollmentsQuery = query(
      enrollmentsRef,
      where('courseId', '==', courseId)
    );
    
    const courseEnrollments = await getDocs(courseEnrollmentsQuery);
    console.log('📊 Total enrollments for course:', courseEnrollments.docs.length);
    
    courseEnrollments.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`📋 Enrollment ${index + 1}:`, {
        docId: doc.id,
        userId: data.userId,
        paymentInfo: data.paymentInfo ? 'Yes' : 'No',
        status: data.status,
        enrolledAt: data.enrolledAt?.toDate?.()?.toLocaleDateString() || 'Unknown'
      });
    });

    // Step 5: Check localStorage
    console.log('');
    console.log('💾 Step 5: Checking localStorage...');
    const progressKey = `progress_${userId}_${courseId}`;
    const progressData = localStorage.getItem(progressKey);
    
    if (progressData) {
      try {
        const progress = JSON.parse(progressData);
        console.log('✅ LocalStorage data found:', {
          enrolledAt: progress.enrolledAt,
          userId: progress.userId,
          courseId: progress.courseId
        });
      } catch (error) {
        console.log('❌ Invalid localStorage data:', error);
      }
    } else {
      console.log('❌ No localStorage data found');
    }

    // Step 6: Check payments
    console.log('');
    console.log('💳 Step 6: Checking payments...');
    const paymentsRef = collection(db, 'payments');
    const userPaymentsQuery = query(
      paymentsRef,
      where('userId', '==', userId),
      where('courseId', '==', courseId)
    );
    
    const userPayments = await getDocs(userPaymentsQuery);
    console.log('📊 Payments found:', userPayments.docs.length);
    
    userPayments.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`💳 Payment ${index + 1}:`, {
        id: doc.id,
        status: data.status,
        amount: data.amount,
        reference: data.paymentDetails?.reference || data.reference,
        createdAt: data.createdAt?.toDate?.()?.toLocaleDateString() || 'Unknown'
      });
    });

    // Summary
    console.log('');
    console.log('🎯 SUMMARY:');
    console.log('===========');
    
    if (isEnrolled) {
      console.log('✅ User is enrolled - should have access');
    } else {
      console.log('❌ User is NOT enrolled');
      
      if (userPayments.docs.length > 0) {
        console.log('💡 ISSUE: Payment found but no enrollment');
        console.log('🔧 SOLUTION: Payment succeeded but enrollment failed');
      } else {
        console.log('💡 ISSUE: No payment or enrollment found');
        console.log('🔧 SOLUTION: User needs to complete payment');
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
};

/**
 * Simulate a successful payment enrollment
 */
export const simulatePaymentEnrollment = async (userId: string, courseId: string) => {
  console.log('🧪 Simulating payment enrollment...');
  
  try {
    const mockPaymentData = {
      transaction_id: 'test_' + Date.now(),
      reference: 'ref_' + Date.now(),
      amount: 3500,
      currency: 'KES',
      payment_method: 'test'
    };

    const enrollmentId = await enrollmentService.enrollUserWithPayment(
      userId,
      courseId,
      mockPaymentData
    );

    console.log('✅ Test enrollment created:', enrollmentId);

    // Also create localStorage entry
    const progressKey = `progress_${userId}_${courseId}`;
    const newProgress = {
      id: progressKey,
      userId: userId,
      courseId: courseId,
      enrolledAt: new Date().toISOString(),
      lastAccessedAt: new Date().toISOString(),
      completionPercentage: 0,
      completedLessons: [],
      completedAssignments: [],
      completedQuizzes: [],
    };
    localStorage.setItem(progressKey, JSON.stringify(newProgress));
    console.log('✅ Test localStorage created');

    console.log('🎉 Test enrollment complete! Try accessing the course now.');
    
  } catch (error) {
    console.error('❌ Test enrollment failed:', error);
  }
};

/**
 * Clear all enrollment data for testing
 */
export const clearEnrollmentData = async (userId: string, courseId: string) => {
  console.log('🧹 Clearing enrollment data...');
  
  try {
    // Clear localStorage
    const progressKey = `progress_${userId}_${courseId}`;
    localStorage.removeItem(progressKey);
    console.log('✅ LocalStorage cleared');

    // Note: We don't delete Firestore data as that should be permanent
    console.log('ℹ️ Firestore data preserved (as it should be)');
    console.log('🔄 Refresh the page to see changes');
    
  } catch (error) {
    console.error('❌ Clear failed:', error);
  }
};

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).debugPaymentFlow = debugPaymentFlow;
  (window as any).simulatePaymentEnrollment = simulatePaymentEnrollment;
  (window as any).clearEnrollmentData = clearEnrollmentData;
  
  console.log('🔧 Payment debug functions available:');
  console.log('• debugPaymentFlow(userId, courseId)');
  console.log('• simulatePaymentEnrollment(userId, courseId)');
  console.log('• clearEnrollmentData(userId, courseId)');
}
