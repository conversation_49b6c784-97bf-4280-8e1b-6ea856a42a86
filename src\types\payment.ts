export interface Payment {
  id: string;
  user_id: string;
  course_id: string;
  amount: number;
  currency: string;
  payment_method: 'card' | 'mpesa' | 'bank_transfer';
  status: 'pending' | 'successful' | 'failed' | 'cancelled';
  transaction_id: string;
  reference: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    course_title?: string;
    user_email?: string;
    user_name?: string;
    phone_number?: string;
  };
}

export interface PaymentIntent {
  course_id: string;
  course_title: string;
  amount: number;
  currency: string;
  payment_method: 'card' | 'mpesa' | 'bank_transfer';
  customer_info: {
    email: string;
    phone_number: string;
    name: string;
  };
}

export interface PaymentResult {
  success: boolean;
  message: string;
  payment_id?: string;
  transaction_id?: string;
  reference?: string;
  redirect_url?: string;
}

export interface PaymentMethodOption {
  id: 'card' | 'mpesa' | 'bank_transfer';
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  currencies: string[];
}

export interface CoursePrice {
  course_id: string;
  price_kes: number;
  price_usd?: number;
  currency_options: string[];
  discount_percentage?: number;
  original_price?: number;
}

export interface PaymentConfig {
  flutterwave: {
    public_key: string;
    secret_key: string;
    environment: 'sandbox' | 'production';
  };
  supported_currencies: string[];
  supported_payment_methods: PaymentMethodOption[];
  default_currency: string;
}

export interface PaymentWebhook {
  event: string;
  data: {
    id: string;
    tx_ref: string;
    flw_ref: string;
    device_fingerprint: string;
    amount: number;
    currency: string;
    charged_amount: number;
    app_fee: number;
    merchant_fee: number;
    processor_response: string;
    auth_model: string;
    ip: string;
    narration: string;
    status: string;
    payment_type: string;
    created_at: string;
    account_id: number;
    customer: {
      id: number;
      name: string;
      phone_number: string;
      email: string;
      created_at: string;
    };
    meta: {
      course_id?: string;
      course_title?: string;
      user_id?: string;
    };
  };
}
