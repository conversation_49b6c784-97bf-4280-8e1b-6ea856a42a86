import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Edit3, 
  Save, 
  X,
  Award,
  BookOpen,
  Clock,
  Star,
  Camera
} from 'lucide-react';

export default function Profile() {
  const { currentUser, userProfile } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    displayName: userProfile?.displayName || currentUser?.displayName || '',
    email: currentUser?.email || '',
    phone: userProfile?.phone || '',
    location: userProfile?.location || '',
    bio: userProfile?.bio || '',
    dateOfBirth: userProfile?.dateOfBirth || '',
    occupation: userProfile?.occupation || '',
    experience: userProfile?.experience || 'beginner'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSave = () => {
    // TODO: Implement profile update logic
    console.log('Saving profile:', formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      displayName: userProfile?.displayName || currentUser?.displayName || '',
      email: currentUser?.email || '',
      phone: userProfile?.phone || '',
      location: userProfile?.location || '',
      bio: userProfile?.bio || '',
      dateOfBirth: userProfile?.dateOfBirth || '',
      occupation: userProfile?.occupation || '',
      experience: userProfile?.experience || 'beginner'
    });
    setIsEditing(false);
  };

  const stats = [
    { label: 'Courses Enrolled', value: '3', icon: BookOpen, color: 'text-blue-400' },
    { label: 'Hours Learned', value: '24', icon: Clock, color: 'text-green-400' },
    { label: 'Certificates', value: '1', icon: Award, color: 'text-purple-400' },
    { label: 'Average Rating', value: '4.8', icon: Star, color: 'text-yellow-400' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <Navigation />
      
      <div className="container mx-auto px-4 py-24">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">My Profile</h1>
          <p className="text-xl text-gray-300">Manage your account information and preferences</p>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-800 border-gray-700 shadow-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl font-bold text-white flex items-center">
                    <User className="w-6 h-6 mr-2 text-blue-400" />
                    Profile Information
                  </CardTitle>
                  {!isEditing ? (
                    <Button
                      onClick={() => setIsEditing(true)}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Edit3 className="w-4 h-4 mr-2" />
                      Edit Profile
                    </Button>
                  ) : (
                    <div className="flex space-x-2">
                      <Button
                        onClick={handleSave}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        Save
                      </Button>
                      <Button
                        onClick={handleCancel}
                        variant="outline"
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Avatar Section */}
                <div className="flex items-center space-x-6 pb-6 border-b border-gray-700">
                  <div className="relative">
                    {userProfile?.profilePictureUrl || currentUser?.photoURL ? (
                      <img
                        src={userProfile?.profilePictureUrl || currentUser?.photoURL || ''}
                        alt={formData.displayName || 'Profile'}
                        className="w-24 h-24 rounded-full object-cover border-2 border-blue-500"
                        onError={(e) => {
                          // Fallback to initials if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.nextElementSibling as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div
                      className={`w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center ${
                        userProfile?.profilePictureUrl || currentUser?.photoURL ? 'hidden' : 'flex'
                      }`}
                    >
                      <span className="text-white text-3xl font-bold">
                        {(formData.displayName || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                    {isEditing && (
                      <button
                        className="absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 hover:bg-blue-700 transition-colors"
                        title="Change profile picture"
                        aria-label="Change profile picture"
                      >
                        <Camera className="w-4 h-4 text-white" />
                      </button>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">{formData.displayName || 'User'}</h3>
                    <p className="text-gray-400">{formData.email}</p>
                    <Badge className="mt-2 bg-blue-900/50 text-blue-200 border-blue-700">
                      {formData.experience.charAt(0).toUpperCase() + formData.experience.slice(1)} Level
                    </Badge>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="displayName" className="text-gray-300">Full Name</Label>
                    <Input
                      id="displayName"
                      name="displayName"
                      value={formData.displayName}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-gray-300">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      disabled={true}
                      className="bg-gray-700 border-gray-600 text-white opacity-60"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phone" className="text-gray-300">Phone Number</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                      placeholder="+254 XXX XXX XXX"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="location" className="text-gray-300">Location</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                      placeholder="City, Country"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="occupation" className="text-gray-300">Occupation</Label>
                    <Input
                      id="occupation"
                      name="occupation"
                      value={formData.occupation}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                      placeholder="Your profession"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="experience" className="text-gray-300">Experience Level</Label>
                    <select
                      id="experience"
                      name="experience"
                      value={formData.experience}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="w-full bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 disabled:opacity-60"
                      aria-label="Experience Level"
                      title="Select your experience level"
                    >
                      <option value="beginner">Beginner</option>
                      <option value="intermediate">Intermediate</option>
                      <option value="advanced">Advanced</option>
                      <option value="expert">Expert</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="bio" className="text-gray-300">Bio</Label>
                  <Textarea
                    id="bio"
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                    placeholder="Tell us about yourself..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats Sidebar */}
          <div className="space-y-6">
            {/* Learning Stats */}
            <Card className="bg-gray-800 border-gray-700 shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Learning Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <stat.icon className={`w-5 h-5 ${stat.color}`} />
                      <span className="text-gray-300">{stat.label}</span>
                    </div>
                    <span className="text-white font-bold text-lg">{stat.value}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-gray-800 border-gray-700 shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white justify-start"
                  onClick={() => window.location.href = '/my-courses'}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  View My Courses
                </Button>
                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white justify-start"
                  onClick={() => window.location.href = '/billing'}
                >
                  <Award className="w-4 h-4 mr-2" />
                  Billing & Payments
                </Button>
                <Button 
                  className="w-full bg-green-600 hover:bg-green-700 text-white justify-start"
                  onClick={() => window.location.href = '/settings'}
                >
                  <User className="w-4 h-4 mr-2" />
                  Account Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
