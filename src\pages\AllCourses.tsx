import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { courseService } from '@/services/courseService';
import { enrollmentsService } from '@/services/firestoreService';
import { Course } from '@/types/course';
import { coursesService } from '@/services/firestoreService';
import { useAuth } from '@/contexts/AuthContext';
import {
  Search,
  Filter,
  Clock,
  Users,
  Star,
  PlayCircle,
  BookOpen,
  TrendingUp,
  Award,
  ChevronRight,
  Grid,
  List
} from 'lucide-react';

export default function AllCourses() {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'newest' | 'popular' | 'rating'>('newest');

  useEffect(() => {
    // Load published courses with real-time enrollment and review data
    const loadCoursesWithRealTimeData = async () => {
      try {
        const coursesData = await courseService.getAllCourses();

        if (coursesData && coursesData.length > 0) {
          // Get real-time enrollment and review data for each course
          const coursesWithRealTimeData = await Promise.all(
            coursesData.map(async (course) => {
              try {
                // Use enrollment count from course data by default
                let enrollmentCount = course.enrollmentCount || 0;

                // Only try to get real-time enrollment data if user is authenticated
                if (currentUser) {
                  try {
                    const enrollments = await enrollmentsService.getByCourse(course.id);
                    const activeEnrollments = enrollments.filter((e: any) => e.status === 'active');
                    enrollmentCount = activeEnrollments.length || course.enrollmentCount || 0;
                  } catch (enrollmentError) {
                    // Use default enrollment count from course data
                    enrollmentCount = course.enrollmentCount || 0;
                  }
                }

                // Use existing review data from course (will be updated by real review system later)
                const reviewCount = course.reviewCount || 0;
                const averageRating = course.rating || 0;

                return {
                  ...course,
                  enrollmentCount,
                  enrolledCount: enrollmentCount,
                  reviewCount,
                  rating: Math.round(averageRating * 10) / 10
                };
              } catch (error) {
                console.log(`⚠️ Error getting real-time data for course ${course.id}:`, error);
                return course; // Return original course if real-time data fails
              }
            })
          );

          console.log('📚 AllCourses: Courses with real-time data:', coursesWithRealTimeData.length);
          console.log('📚 AllCourses: Course IDs:', coursesWithRealTimeData.map(c => ({ id: c.id, title: c.title, students: c.enrollmentCount, reviews: c.reviewCount })));
          setCourses(coursesWithRealTimeData);
        } else {
          console.log('📚 AllCourses: No courses found');
          setCourses([]);
        }
      } catch (error) {
        console.error('📚 AllCourses: Error loading courses:', error);
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    loadCoursesWithRealTimeData();

    // Set loading timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('⏰ AllCourses: Loading timeout - stopping loading state');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    // Cleanup timeout on unmount
    return () => {
      console.log('🧹 AllCourses: Cleaning up timeout');
      clearTimeout(loadingTimeout);
    };
  }, []); // Note: AllCourses doesn't need currentUser dependency since it doesn't use auth-specific logic

  useEffect(() => {
    filterCourses();
  }, [courses, searchTerm, selectedLevel, sortBy]);

  // Real-time listener handles course loading automatically

  const filterCourses = () => {
    let filtered = [...courses];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.instructor.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Level filter
    if (selectedLevel !== 'all') {
      filtered = filtered.filter(course => course.level === selectedLevel);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.enrollmentCount - a.enrollmentCount;
        case 'rating':
          return b.rating - a.rating;
        case 'newest':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    setFilteredCourses(filtered);
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const CourseCard = ({ course, isListView = false }: { course: Course; isListView?: boolean }) => (
    <Card 
      className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 ${
        isListView ? 'flex flex-row' : ''
      }`}
      onClick={() => navigate(`/course/${course.id}`)}
    >
      <div className={`relative overflow-hidden ${isListView ? 'w-64 flex-shrink-0' : 'aspect-video'}`}>
        <img
          src={course.thumbnail || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500'}
          alt={course.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
          <PlayCircle className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="absolute top-3 left-3">
          <Badge className={getLevelColor(course.level)}>
            {course.level.charAt(0).toUpperCase() + course.level.slice(1)}
          </Badge>
        </div>
        {course.price === 0 && (
          <div className="absolute top-3 right-3">
            <Badge className="bg-green-500 text-white">Free</Badge>
          </div>
        )}
      </div>

      <div className={`p-6 ${isListView ? 'flex-1' : ''}`}>
        <CardHeader className="p-0 mb-4">
          <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
            {course.title}
          </CardTitle>
          {/* Use short description if available, fallback to description */}
          <p className="text-gray-600 text-sm line-clamp-2">
            {course.shortDescription || course.description}
          </p>
        </CardHeader>

        <CardContent className="p-0">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center text-sm text-gray-500">
              <Users className="w-4 h-4 mr-1" />
              <span>{course.instructor}</span>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="w-4 h-4 mr-1" />
              <span>{formatDuration(course.duration)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-400 fill-yellow-400 mr-1" />
              <span className="text-sm font-medium">{course.rating}</span>
              <span className="text-sm text-gray-500 ml-1">({course.reviewCount})</span>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span>{course.enrollmentCount} students</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-blue-600">
                  {course.price === 0 ? 'Free' : `KES ${course.price.toLocaleString()}`}
                </div>
                {course.originalPrice && course.originalPrice > course.price && (
                  <div className="text-sm text-gray-500 line-through">
                    KES {course.originalPrice.toLocaleString()}
                  </div>
                )}
              </div>
              {course.price > 0 && (
                <div className="text-xs text-gray-500">
                  One-time payment
                </div>
              )}
            </div>
            <Button size="sm" className="btn-animated btn-pink transform hover:scale-105">
              View Course
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </CardContent>
      </div>
    </Card>
  );

  if (loading) {
    return (
      <div className="min-h-screen section-dark">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-slate-300">Loading courses...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen section-dark">
      <Navigation />
      
      <div className="pt-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16 animate-fade-in-up">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-5xl font-bold mb-6">All Courses</h1>
              <p className="text-xl mb-8 max-w-3xl mx-auto">
                Discover our complete collection of FlutterFlow courses designed to take you from beginner to expert.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                  <BookOpen className="w-8 h-8 mx-auto mb-3" />
                  <div className="text-2xl font-bold">{courses.length}+</div>
                  <div className="text-blue-100">Courses Available</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                  <Users className="w-8 h-8 mx-auto mb-3" />
                  <div className="text-2xl font-bold">1000+</div>
                  <div className="text-blue-100">Students Learning</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                  <Award className="w-8 h-8 mx-auto mb-3" />
                  <div className="text-2xl font-bold">4.8/5</div>
                  <div className="text-blue-100">Average Rating</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="container mx-auto px-4 py-8">
          <div className="card-elevated rounded-lg shadow-2xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search courses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center gap-4">
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="px-3 py-2 bg-blue-dark-800 border border-blue-dark-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 bg-blue-dark-800 border border-blue-dark-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="newest">Newest First</option>
                  <option value="popular">Most Popular</option>
                  <option value="rating">Highest Rated</option>
                </select>

                <div className="flex border border-blue-dark-600 rounded-md">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'text-blue-200'}`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'text-blue-200'}`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Results */}
          <div className="mb-6">
            <p className="text-blue-200">
              Showing {filteredCourses.length} of {courses.length} courses
              {searchTerm && ` for "${searchTerm}"`}
            </p>
          </div>

          {/* Courses Grid/List */}
          {filteredCourses.length === 0 ? (
            <div className="text-center py-16">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No courses found</h3>
              <p className="text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
              <Button
              onClick={() => {
                setSearchTerm('');
                setSelectedLevel('all');
              }}
              className="btn-animated btn-blue"
            >
              Clear Filters
            </Button>
            </div>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
                : 'space-y-6'
            }>
              {filteredCourses.map((course) => (
                <CourseCard 
                  key={course.id} 
                  course={course} 
                  isListView={viewMode === 'list'} 
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
