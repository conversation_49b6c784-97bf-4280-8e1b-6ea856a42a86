/**
 * Secure Checkout Page for FreeCodeLap
 * Handles course purchases with Paystack integration
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';
import { enrollmentService } from '@/services/enrollmentService';
import { Course } from '@/types/course';
import { PaystackPayment } from '@/components/payment/PaystackPayment';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  BookOpen, 
  Clock, 
  Users, 
  Star,
  Shield
} from 'lucide-react';

export default function CheckoutPage() {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [enrollmentId, setEnrollmentId] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    if (!courseId) {
      setError('Course ID is missing');
      setLoading(false);
      return;
    }

    loadCourse();
  }, [courseId, currentUser, navigate]);

  const loadCourse = async () => {
    try {
      setLoading(true);
      const courseData = await courseService.getCourse(courseId!);
      
      if (!courseData) {
        setError('Course not found');
        return;
      }

      // Check if user is already enrolled
      const isEnrolled = await enrollmentService.isUserEnrolled(currentUser!.uid, courseId!);
      if (isEnrolled) {
        navigate(`/course/${courseId}/content`);
        return;
      }

      setCourse(courseData);
    } catch (error: any) {
      console.error('❌ Error loading course:', error);
      setError(error.message || 'Failed to load course');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (enrollmentId: string) => {
    setEnrollmentId(enrollmentId);
    setSuccess(true);
    
    // Redirect to course content after a short delay
    setTimeout(() => {
      navigate(`/course/${courseId}/content`);
    }, 3000);
  };

  const handlePaymentError = (error: string) => {
    setError(error);
  };

  const handlePaymentCancel = () => {
    // User cancelled payment, stay on checkout page
    console.log('Payment cancelled by user');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
            <p className="text-gray-400">Loading course details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Alert className="border-red-600 bg-red-900/20 max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
          <div className="text-center mt-6">
            <Button onClick={() => navigate('/courses')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Card className="bg-gray-800 border-gray-700 max-w-md mx-auto">
            <CardContent className="text-center p-8">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">Payment Successful!</h2>
              <p className="text-gray-400 mb-4">
                You have been successfully enrolled in {course?.title}
              </p>
              <p className="text-gray-500 text-sm">
                Redirecting to course content...
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!course) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button 
            onClick={() => navigate(`/course/${courseId}`)} 
            variant="ghost" 
            className="text-gray-400 hover:text-white mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Course Details
          </Button>
          
          <h1 className="text-3xl font-bold text-white mb-2">Complete Your Purchase</h1>
          <p className="text-gray-400">Secure checkout powered by Paystack</p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Course Summary */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Course Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <img 
                  src={course.thumbnail} 
                  alt={course.title}
                  className="w-20 h-20 object-cover rounded-lg"
                />
                <div className="flex-1">
                  <h3 className="text-white font-semibold">{course.title}</h3>
                  <p className="text-gray-400 text-sm">by {course.instructor}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center text-gray-400">
                  <Clock className="h-4 w-4 mr-2" />
                  {course.duration}
                </div>
                <div className="flex items-center text-gray-400">
                  <Users className="h-4 w-4 mr-2" />
                  {course.enrolledCount || 0} students
                </div>
                <div className="flex items-center text-gray-400">
                  <BookOpen className="h-4 w-4 mr-2" />
                  {course.level}
                </div>
                <div className="flex items-center text-gray-400">
                  <Star className="h-4 w-4 mr-2" />
                  {course.rating || 4.8} rating
                </div>
              </div>

              <div className="border-t border-gray-700 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Course Price:</span>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">${course.price} USD</div>
                    <div className="text-gray-400 text-sm">≈ KES {Math.round(course.price * 130).toLocaleString()}</div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-3">
                <div className="flex items-center text-blue-400 text-sm">
                  <Shield className="h-4 w-4 mr-2" />
                  30-day money-back guarantee
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Section */}
          <div>
            <PaystackPayment
              userId={currentUser!.uid}
              courseId={course.id}
              courseTitle={course.title}
              courseInstructor={course.instructor}
              amount={course.price}
              customerEmail={currentUser!.email!}
              customerName={currentUser!.displayName || undefined}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              onCancel={handlePaymentCancel}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
