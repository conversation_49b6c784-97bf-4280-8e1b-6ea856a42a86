
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Award, Users, Code, Github, BookOpen, Star, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';

const AboutInstructorSection = () => {
  const achievements = [
    { icon: <Code className="w-6 h-6" />, label: "5+ Years Experience", value: "FlutterFlow Expert" },
    { icon: <Users className="w-6 h-6" />, label: "500+ Students", value: "Taught Globally" },
    { icon: <Award className="w-6 h-6" />, label: "50+ Apps", value: "Built & Published" },
    { icon: <BookOpen className="w-6 h-6" />, label: "AI Specialist", value: "Advanced Integrations" }
  ];

  const expertise = [
    "FlutterFlow Advanced Features",
    "Firebase & Backend Integration",
    "AI & ChatGPT Implementation",
    "RevenueCat & Payment Systems",
    "Custom Widget Development",
    "App Store Optimization",
    "Mobile App Monetization",
    "Real-time Applications"
  ];



  return (
    <section id="about-instructor" className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center space-x-2 bg-blue-900/50 text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-6 border border-blue-700">
            <Award className="w-4 h-4" />
            <span>Meet Your Expert Instructor</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Learn from <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Ahmed Takal</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            FlutterFlow Expert & Mobile App Development Specialist with 5+ years of experience
            building real-world applications and teaching students globally
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-16">
            {/* Left Column - Instructor Image */}
            <div className="relative animate-slide-in-left">
              <div className="relative mb-8">
                {/* Main Image */}
                <div className="relative w-80 h-80 mx-auto lg:mx-0">
                  <img
                    src="https://ahmedtakal.s3.us-east-005.backblazeb2.com/myImages/ahmed+taqal.jpeg"
                    alt="Ahmed Mohamed Noor - FlutterFlow Expert"
                    className="w-full h-full rounded-2xl shadow-2xl object-cover transform hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-600/10 to-transparent rounded-2xl"></div>
                </div>

                {/* Floating Badges */}
                <div className="absolute -top-4 -left-4 bg-gray-800 border border-gray-600 text-white px-4 py-2 rounded-full shadow-lg font-bold text-sm">
                  🇰🇪 Based in Kenya
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-xl">
                  <Award className="w-6 h-6" />
                </div>

                {/* Experience Badge */}
                <div className="absolute top-4 right-4 bg-gray-800/90 backdrop-blur-sm text-white px-3 py-1 rounded-full shadow-lg font-semibold text-sm border border-gray-600">
                  5+ Years Experience
                </div>
              </div>
            </div>

            {/* Right Column - Instructor Info */}
            <div className="animate-slide-in-right">
              <div className="space-y-6">
                {/* Name and Title */}
                <div>
                  <h3 className="text-4xl font-bold text-white mb-3">
                    Hi, I'm <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Ahmed Takal</span>
                  </h3>
                  <p className="text-xl text-blue-400 font-semibold mb-2">Senior FlutterFlow Developer & Instructor</p>
                  <p className="text-lg text-gray-300">Founder of FreeCodeLap</p>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-3">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-current" />
                    ))}
                  </div>
                  <span className="text-white font-semibold">4.9/5</span>
                  <span className="text-gray-400">(2,500+ students)</span>
                </div>

                {/* Description */}
                <div className="space-y-4">
                  <p className="text-lg text-gray-300 leading-relaxed">
                    I'm a passionate FlutterFlow developer with over <strong className="text-white">5 years of experience</strong> in mobile app development.
                    I specialize in creating powerful, scalable applications using FlutterFlow, Firebase, AI integrations, and advanced payment systems.
                  </p>
                  <p className="text-lg text-gray-300 leading-relaxed">
                    Having built and published over <strong className="text-white">50 successful apps</strong>, I understand the challenges developers face and the solutions that work in real-world scenarios.
                    My courses are designed to give you practical, hands-on experience that you can immediately apply to your own projects.
                  </p>
                </div>

                {/* Expertise Areas */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4 hover:bg-blue-900/40 transition-colors duration-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <Code className="w-5 h-5 text-blue-400" />
                      <span className="font-semibold text-white">FlutterFlow Expert</span>
                    </div>
                    <p className="text-sm text-gray-300">Advanced app development & custom functions</p>
                  </div>
                  <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4 hover:bg-purple-900/40 transition-colors duration-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <Zap className="w-5 h-5 text-purple-400" />
                      <span className="font-semibold text-white">AI Integration</span>
                    </div>
                    <p className="text-sm text-gray-300">ChatGPT, OpenAI & machine learning</p>
                  </div>
                  <div className="bg-green-900/30 border border-green-700 rounded-lg p-4 hover:bg-green-900/40 transition-colors duration-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <BookOpen className="w-5 h-5 text-green-400" />
                      <span className="font-semibold text-white">Teaching</span>
                    </div>
                    <p className="text-sm text-gray-300">2,500+ students taught globally</p>
                  </div>
                  <div className="bg-orange-900/30 border border-orange-700 rounded-lg p-4 hover:bg-orange-900/40 transition-colors duration-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-5 h-5 text-orange-400" />
                      <span className="font-semibold text-white">Mentoring</span>
                    </div>
                    <p className="text-sm text-gray-300">1-on-1 guidance & career support</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-4">
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                    <BookOpen className="w-4 h-4 mr-2" />
                    View My Courses
                  </Button>
                  <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-3 rounded-lg font-semibold">
                    <Users className="w-4 h-4 mr-2" />
                    Join Community
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-gray-800 border border-gray-700 shadow-xl rounded-2xl overflow-hidden">
              <CardContent className="p-8">
                <h4 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <Award className="w-6 h-6 text-blue-400 mr-2" />
                  Key Achievements
                </h4>
                <div className="grid grid-cols-2 gap-6">
                  {achievements.map((achievement, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-gradient-to-br from-blue-900/50 to-purple-900/50 text-blue-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 transform hover:scale-110 transition-transform duration-300 border border-blue-700">
                        {achievement.icon}
                      </div>
                      <div className="text-2xl font-bold text-white">{achievement.value}</div>
                      <div className="text-sm text-gray-400">{achievement.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border border-gray-700 shadow-xl rounded-2xl overflow-hidden">
              <CardContent className="p-8">
                <h4 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <Code className="w-6 h-6 text-purple-400 mr-2" />
                  Areas of Expertise
                </h4>
                <div className="space-y-4">
                  {expertise.map((skill, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
                      <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
                      <span className="text-gray-200 font-medium">{skill}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutInstructorSection;
