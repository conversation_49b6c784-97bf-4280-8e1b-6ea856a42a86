# 🛠️ Paystack CSP Troubleshooting Guide

## 🚨 Common CSP Error
```
Refused to frame 'https://checkout.paystack.com/' because it violates the following Content Security Policy directive: "frame-src https://js.paystack.co https://checkout.paystack.co https://accounts.google.com".
```

## ✅ Solution Applied

### 1. Updated Content Security Policy
The CSP in `index.html` has been updated to include all necessary Paystack domains:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self'; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.paystack.co https://fonts.googleapis.com https://apis.google.com https://www.gstatic.com https://gstatic.com; 
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://paystack.com; 
  font-src 'self' https://fonts.gstatic.com; 
  img-src 'self' data: https: https://www.google.com https://accounts.google.com https://paystack.com https://checkout.paystack.co; 
  connect-src 'self' https://api.paystack.co https://standard.paystack.co https://checkout.paystack.co https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://accounts.google.com; 
  frame-src 'self' https://js.paystack.co https://checkout.paystack.co https://standard.paystack.co https://paystack.com https://accounts.google.com; 
  child-src 'self' https://js.paystack.co https://checkout.paystack.co https://standard.paystack.co https://paystack.com;
">
```

### 2. Enhanced Paystack Loading
Updated `PaystackCheckout.tsx` with:
- Better error handling
- Timeout protection
- Proper script cleanup
- CSP-compliant loading

### 3. Required Domains for Paystack
Make sure these domains are allowed in your CSP:

**Script Sources:**
- `https://js.paystack.co` - Main Paystack script

**Frame Sources:**
- `https://checkout.paystack.co` - Payment iframe
- `https://standard.paystack.co` - Alternative checkout
- `https://paystack.com` - Main domain

**Connect Sources:**
- `https://api.paystack.co` - API calls
- `https://standard.paystack.co` - Payment processing
- `https://checkout.paystack.co` - Checkout API

**Image Sources:**
- `https://paystack.com` - Logos and images
- `https://checkout.paystack.co` - Checkout images

## 🔧 Testing the Fix

### 1. Clear Browser Cache
```bash
# Chrome DevTools
F12 → Application → Storage → Clear storage

# Or hard refresh
Ctrl+Shift+R (Windows/Linux)
Cmd+Shift+R (Mac)
```

### 2. Check Console for Errors
Open DevTools (F12) and look for:
- ✅ No CSP violations
- ✅ Paystack script loaded successfully
- ✅ Payment iframe opens without errors

### 3. Test Payment Flow
1. Navigate to course checkout
2. Click "Pay with Card" or "Pay with M-Pesa"
3. Verify payment popup opens
4. Complete test transaction

## 🚨 If Issues Persist

### Alternative CSP Configuration
If you're still having issues, try this more permissive CSP temporarily:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self'; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; 
  style-src 'self' 'unsafe-inline' https:; 
  font-src 'self' https:; 
  img-src 'self' data: https:; 
  connect-src 'self' https:; 
  frame-src 'self' https:; 
  child-src 'self' https:;
">
```

⚠️ **Warning**: This is less secure and should only be used for testing.

### Check Network Tab
1. Open DevTools → Network tab
2. Try to make a payment
3. Look for failed requests to Paystack domains
4. Check if any requests are blocked

### Verify Environment Variables
Make sure your Paystack keys are properly set:

```bash
# Check .env file
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key
# or for production
VITE_PAYSTACK_PUBLIC_KEY=pk_live_your_live_key
```

## 🔒 Security Best Practices

### 1. Use Test Keys in Development
```env
# Development
VITE_PAYSTACK_PUBLIC_KEY=pk_test_89ae2fc67b744d6b6fe3be4e2354bd7c287b8515

# Production
VITE_PAYSTACK_PUBLIC_KEY=pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3
```

### 2. Validate SSL Certificate
Ensure your site has a valid SSL certificate:
- HTTPS is required for payment processing
- Use Let's Encrypt for free SSL
- Check certificate validity

### 3. Monitor Payment Logs
Check Paystack dashboard for:
- Failed transactions
- CSP-related errors
- Integration issues

## 📞 Support

If you continue experiencing issues:

1. **Check Paystack Status**: https://status.paystack.com
2. **Paystack Documentation**: https://paystack.com/docs
3. **Contact Paystack Support**: <EMAIL>

## ✅ Verification Checklist

- [ ] CSP updated with all Paystack domains
- [ ] Browser cache cleared
- [ ] No CSP violations in console
- [ ] Paystack script loads successfully
- [ ] Payment iframe opens without errors
- [ ] Test transaction completes successfully
- [ ] SSL certificate is valid
- [ ] Environment variables are correct
