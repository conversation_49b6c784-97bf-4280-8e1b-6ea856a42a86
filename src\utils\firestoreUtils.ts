/**
 * Utility functions for Firestore data handling
 */

/**
 * Removes undefined values from an object to prevent Firestore errors
 * Firestore doesn't allow undefined values, so we need to clean the data
 */
export function cleanFirestoreData<T extends Record<string, any>>(data: T): Partial<T> {
  const cleaned: Partial<T> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (value !== undefined) {
      // Handle nested objects
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        const cleanedNested = cleanFirestoreData(value);
        if (Object.keys(cleanedNested).length > 0) {
          cleaned[key as keyof T] = cleanedNested as T[keyof T];
        }
      } else {
        cleaned[key as keyof T] = value;
      }
    }
  }
  
  return cleaned;
}

/**
 * Validates that required fields are present in the data
 */
export function validateRequiredFields<T extends Record<string, any>>(
  data: T, 
  requiredFields: (keyof T)[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      missingFields.push(String(field));
    }
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * Converts empty strings to null for optional fields
 * This is useful when you want to distinguish between "not set" (null) and "empty" ("")
 */
export function convertEmptyStringsToNull<T extends Record<string, any>>(data: T): T {
  const converted = { ...data };
  
  for (const [key, value] of Object.entries(converted)) {
    if (value === '') {
      converted[key as keyof T] = null as T[keyof T];
    }
  }
  
  return converted;
}

/**
 * Sanitizes a string to be used as a Firestore document ID
 */
export function sanitizeDocumentId(input: string): string {
  return input
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
}

/**
 * Prepares course data for Firestore by cleaning and validating
 */
export function prepareCourseForFirestore(courseData: any) {
  // Required fields for a course
  const requiredFields = ['title', 'description', 'category', 'instructor', 'price'];
  
  // Validate required fields
  const validation = validateRequiredFields(courseData, requiredFields);
  if (!validation.isValid) {
    throw new Error(`Missing required fields: ${validation.missingFields.join(', ')}`);
  }
  
  // Clean the data
  const cleanedData = cleanFirestoreData(courseData);
  
  // Ensure arrays are not undefined
  if (!cleanedData.tags) cleanedData.tags = [];
  if (!cleanedData.requirements) cleanedData.requirements = [];
  if (!cleanedData.learningOutcomes) cleanedData.learningOutcomes = [];
  if (!cleanedData.targetAudience) cleanedData.targetAudience = [];
  if (!cleanedData.modules) cleanedData.modules = [];
  
  // Ensure numeric fields have default values
  if (cleanedData.price === undefined) cleanedData.price = 0;
  if (cleanedData.enrollmentCount === undefined) cleanedData.enrollmentCount = 0;
  if (cleanedData.enrolledCount === undefined) cleanedData.enrolledCount = 0;
  if (cleanedData.rating === undefined) cleanedData.rating = 0;
  if (cleanedData.reviewCount === undefined) cleanedData.reviewCount = 0;
  
  // Ensure boolean fields have default values
  if (cleanedData.isPublished === undefined) cleanedData.isPublished = false;
  if (cleanedData.featured === undefined) cleanedData.featured = false;
  if (cleanedData.allowComments === undefined) cleanedData.allowComments = true;
  if (cleanedData.allowDownloads === undefined) cleanedData.allowDownloads = true;
  if (cleanedData.certificateEnabled === undefined) cleanedData.certificateEnabled = true;
  
  // Set default access type
  if (!cleanedData.accessType) cleanedData.accessType = 'lifetime';
  
  // Set default currency
  if (!cleanedData.currency) cleanedData.currency = 'KES';
  
  // Set default status
  if (!cleanedData.status) {
    cleanedData.status = cleanedData.isPublished ? 'published' : 'draft';
  }
  
  return cleanedData;
}
