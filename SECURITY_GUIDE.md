# 🛡️ FreeCodeLap Security Guide

## 🚨 IMMEDIATE ACTION REQUIRED

### Step 1: Revoke Compromised API Keys
**URGENT**: The following keys were exposed in your code and must be revoked immediately:

1. **Login to Paystack Dashboard**: https://dashboard.paystack.com
2. **Go to Settings → API Keys & Webhooks**
3. **Revoke these exposed keys**:
   - `pk_live_86ca6418f579d33705360b4a50912f4fc5d41da3`
   - `************************************************`
4. **Generate new live keys**
5. **Update your production environment variables**

### Step 2: Update Environment Variables
Your codebase now uses secure environment variables. Update your keys:

```bash
# In your .env file (for development - use TEST keys)
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key_here
VITE_PAYSTACK_SECRET_KEY=sk_test_your_test_key_here

# In your .env.production file (for production - use NEW LIVE keys)
VITE_PAYSTACK_PUBLIC_KEY=pk_live_your_new_live_key_here
VITE_PAYSTACK_SECRET_KEY=sk_live_your_new_live_key_here
```

## 🔒 Environment Variable Best Practices

### Development vs Production
- **Development**: Always use TEST keys (`pk_test_`, `sk_test_`)
- **Production**: Only use LIVE keys (`pk_live_`, `sk_live_`)

### File Structure
```
.env                 # Development environment (TEST keys)
.env.production      # Production environment (LIVE keys)
.env.example         # Template file (no real keys)
```

### Security Rules
1. **NEVER commit .env files** to version control
2. **Use different keys** for development and production
3. **Rotate keys regularly** (every 3-6 months)
4. **Monitor for unauthorized usage**
5. **Use least privilege principle**

## 🔧 How to Use Environment Variables

### In React/Vite (Frontend)
```javascript
// ✅ Correct way
const apiKey = import.meta.env.VITE_FIREBASE_API_KEY;

// ❌ Wrong way (hardcoded)
const apiKey = "AIzaSyABB72plhO6QQEvbCGoiJnuowDbtjjJ2OM";
```

### In Node.js (Backend)
```javascript
// ✅ Correct way
const secretKey = process.env.PAYSTACK_SECRET_KEY;

// ❌ Wrong way (hardcoded)
const secretKey = "************************************************";
```

### Environment Variable Naming
- **Frontend (Vite)**: Must start with `VITE_`
- **Backend (Node.js)**: No prefix required
- **Use UPPERCASE** with underscores
- **Be descriptive**: `VITE_PAYSTACK_PUBLIC_KEY` not `VITE_KEY`

## 🚀 Deployment Security

### Production Checklist
- [ ] New API keys generated and updated
- [ ] HTTPS enabled (SSL certificate)
- [ ] Environment variables set on hosting platform
- [ ] .env files added to .gitignore
- [ ] No hardcoded secrets in code
- [ ] Webhook signatures verified
- [ ] Error logging configured
- [ ] Rate limiting implemented

### Hosting Platforms
Each platform has different ways to set environment variables:

**Vercel**:
```bash
vercel env add VITE_PAYSTACK_PUBLIC_KEY
```

**Netlify**:
Site Settings → Environment Variables

**Railway/Render**:
Dashboard → Environment Variables

## 🔍 Security Monitoring

### What to Monitor
- Unusual payment patterns
- Failed authentication attempts
- API rate limit hits
- Webhook delivery failures
- Error logs and exceptions

### Tools to Use
- Paystack Dashboard analytics
- Firebase Console monitoring
- Application error tracking (Sentry)
- Server logs and alerts

## 📋 Security Checklist

### Development
- [ ] Using TEST API keys only
- [ ] .env file not committed to git
- [ ] Environment variables properly loaded
- [ ] Error handling for missing variables
- [ ] Local HTTPS for payment testing

### Production
- [ ] Using LIVE API keys (new ones)
- [ ] HTTPS enabled with valid SSL
- [ ] Environment variables set on hosting
- [ ] Webhook signature verification
- [ ] Error monitoring and alerting
- [ ] Regular security audits
- [ ] API key rotation schedule

## 🆘 Emergency Response

If you suspect a security breach:

1. **Immediately revoke** all API keys
2. **Generate new keys** and update production
3. **Check transaction logs** for unauthorized activity
4. **Review access logs** for suspicious activity
5. **Contact Paystack support** if needed
6. **Document the incident** for future prevention

## 📞 Support Contacts

- **Paystack Support**: <EMAIL>
- **Firebase Support**: Firebase Console → Support
- **Emergency**: Immediately revoke keys first, ask questions later

---

**Remember**: Security is not a one-time setup, it's an ongoing process. Regular audits and updates are essential for maintaining a secure application.
