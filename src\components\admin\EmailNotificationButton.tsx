/**
 * FreeCodeLap Platform - Email Notification Button
 * 
 * Quick access button for sending emails to users
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { 
  Mail, 
  ChevronDown, 
  Users, 
  UserCheck, 
  UserX, 
  Shield,
  GraduationCap,
  Send
} from 'lucide-react';
import { EmailNotificationModal } from './EmailNotificationModal';
import { EmailRecipientFilter } from '@/services/emailNotificationService';

interface EmailNotificationButtonProps {
  selectedUserIds?: string[];
  onEmailSent?: () => void;
  className?: string;
}

export const EmailNotificationButton: React.FC<EmailNotificationButtonProps> = ({
  selectedUserIds = [],
  onEmailSent,
  className = ''
}) => {
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [recipientFilter, setRecipientFilter] = useState<EmailRecipientFilter>({
    status: 'all',
    role: 'all',
    enrollmentStatus: 'all'
  });

  const handleQuickEmail = (filter: EmailRecipientFilter) => {
    console.log('📧 Quick email filter selected:', filter);
    setRecipientFilter(filter);
    setShowEmailModal(true);
  };

  const handleCustomEmail = () => {
    setRecipientFilter({
      status: 'all',
      role: 'all',
      enrollmentStatus: 'all',
      customUserIds: selectedUserIds
    });
    setShowEmailModal(true);
  };

  const handleEmailSent = () => {
    setShowEmailModal(false);
    onEmailSent?.();
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`border-gray-600 text-gray-300 hover:bg-gray-700 ${className}`}
          >
            <Mail className="h-4 w-4 mr-2" />
            Send Email
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent className="bg-gray-800 border-gray-700 text-white">
          {selectedUserIds.length > 0 && (
            <>
              <DropdownMenuItem 
                onClick={handleCustomEmail}
                className="hover:bg-gray-700 cursor-pointer"
              >
                <Send className="h-4 w-4 mr-2" />
                Email Selected Users ({selectedUserIds.length})
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-700" />
            </>
          )}
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'all', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <Users className="h-4 w-4 mr-2" />
            All Users
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'active', role: 'all', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <UserCheck className="h-4 w-4 mr-2" />
            Active Users
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'inactive', role: 'all', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <UserX className="h-4 w-4 mr-2" />
            Inactive Users
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'suspended', role: 'all', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <UserX className="h-4 w-4 mr-2" />
            Suspended Users
          </DropdownMenuItem>
          
          <DropdownMenuSeparator className="bg-gray-700" />
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'student', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <GraduationCap className="h-4 w-4 mr-2" />
            All Students
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'admin', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <Shield className="h-4 w-4 mr-2" />
            All Admins
          </DropdownMenuItem>
          
          <DropdownMenuSeparator className="bg-gray-700" />
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'all', enrollmentStatus: 'enrolled' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <GraduationCap className="h-4 w-4 mr-2" />
            Enrolled Students
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'all', enrollmentStatus: 'not_enrolled' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <Users className="h-4 w-4 mr-2" />
            Non-Enrolled Users
          </DropdownMenuItem>
          
          <DropdownMenuSeparator className="bg-gray-700" />
          
          <DropdownMenuItem 
            onClick={() => handleQuickEmail({ status: 'all', role: 'all', enrollmentStatus: 'all' })}
            className="hover:bg-gray-700 cursor-pointer"
          >
            <Mail className="h-4 w-4 mr-2" />
            Custom Email...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <EmailNotificationModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        onEmailSent={handleEmailSent}
        preselectedUserIds={recipientFilter.customUserIds}
        initialFilter={recipientFilter}
      />
    </>
  );
};
