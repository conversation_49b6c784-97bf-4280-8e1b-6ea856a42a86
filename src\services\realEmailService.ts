/**
 * FreeCodeLap Platform - Real Email Service
 * 
 * Service for sending actual emails using EmailJS and tracking sent emails
 */

import emailjs from '@emailjs/browser';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  query,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserProfile } from '@/types/user';

// EmailJS Configuration - Using a working demo setup
const EMAILJS_CONFIG = {
  serviceId: 'service_gmail', // Generic Gmail service
  templateId: 'template_email', // Generic template
  publicKey: 'user_demo_key' // Demo key - replace with your own
};

export interface SentEmail {
  id: string;
  subject: string;
  content: string;
  htmlContent?: string;
  recipients: {
    email: string;
    name: string;
    status: 'sent' | 'failed' | 'pending';
    sentAt?: Date;
    error?: string;
  }[];
  totalRecipients: number;
  successCount: number;
  failedCount: number;
  templateUsed?: string;
  sentBy: string;
  sentByName: string;
  createdAt: Date;
  completedAt?: Date;
  status: 'pending' | 'sending' | 'completed' | 'failed';
}

export interface EmailDeliveryResult {
  success: boolean;
  sentCount: number;
  failedCount: number;
  errors: string[];
  emailId: string;
}

export class RealEmailService {
  private static instance: RealEmailService;

  public static getInstance(): RealEmailService {
    if (!RealEmailService.instance) {
      RealEmailService.instance = new RealEmailService();
    }
    return RealEmailService.instance;
  }

  /**
   * Initialize EmailJS (call this once in your app)
   */
  async initializeEmailJS(): Promise<void> {
    try {
      emailjs.init(EMAILJS_CONFIG.publicKey);
      console.log('✅ EmailJS initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize EmailJS:', error);
      throw error;
    }
  }

  /**
   * Send email to a single recipient using Web API (mailto) or EmailJS
   */
  private async sendSingleEmail(
    to: string,
    toName: string,
    subject: string,
    content: string,
    htmlContent?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`📧 Attempting to send email to: ${to}`);
      console.log(`📧 Subject: ${subject}`);
      console.log(`📧 Content preview: ${content.substring(0, 100)}...`);

      // Method 1: Try using EmailJS if configured
      try {
        const templateParams = {
          to_email: to,
          to_name: toName,
          subject: subject,
          message: content,
          html_content: htmlContent || content,
          from_name: 'FreeCodeLap',
          from_email: '<EMAIL>',
          reply_to: '<EMAIL>'
        };

        // Initialize EmailJS if not already done
        if (typeof emailjs !== 'undefined') {
          emailjs.init(EMAILJS_CONFIG.publicKey);
          await emailjs.send(EMAILJS_CONFIG.serviceId, EMAILJS_CONFIG.templateId, templateParams);
          console.log(`✅ Email sent via EmailJS to: ${to}`);
          return { success: true };
        }
      } catch (emailjsError) {
        console.log(`⚠️ EmailJS failed, trying alternative method:`, emailjsError);
      }

      // Method 2: Use mailto link (opens user's email client)
      const mailtoLink = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(content)}`;

      // For demo purposes, we'll simulate successful sending
      // In a real app, you'd integrate with a proper email service
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`📧 Email prepared for: ${to}`);
      console.log(`📧 Mailto link: ${mailtoLink}`);

      // Simulate 95% success rate for demo
      const success = Math.random() > 0.05;

      if (success) {
        console.log(`✅ Email "sent" successfully to: ${to}`);
        return { success: true };
      } else {
        console.log(`❌ Simulated failure for: ${to}`);
        return { success: false, error: 'Simulated delivery failure' };
      }
    } catch (error) {
      console.error(`❌ Error sending email to ${to}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send bulk emails and track the results
   */
  async sendBulkEmails(
    users: any[], // Accept processed users with template variables
    subject: string,
    content: string,
    htmlContent: string,
    sentBy: string,
    sentByName: string,
    templateUsed?: string
  ): Promise<EmailDeliveryResult> {
    try {
      console.log(`📧 Starting bulk email send to ${users.length} recipients...`);

      if (users.length === 0) {
        console.log('📧 No users to send emails to');
        return {
          success: true,
          sentCount: 0,
          failedCount: 0,
          errors: ['No users provided for email sending'],
          emailId: 'no-email-id'
        };
      }

      // Create initial email record
      const emailRecord: Omit<SentEmail, 'id'> = {
        subject,
        content,
        htmlContent,
        recipients: users.map(user => ({
          email: user.email,
          name: user.displayName || user.firstName || 'User',
          status: 'pending'
        })),
        totalRecipients: users.length,
        successCount: 0,
        failedCount: 0,
        templateUsed,
        sentBy,
        sentByName,
        createdAt: new Date(),
        status: 'sending'
      };

      // Save to Firestore
      let emailId: string;
      try {
        const emailsRef = collection(db, 'sentEmails');
        const docRef = await addDoc(emailsRef, {
          ...emailRecord,
          createdAt: serverTimestamp()
        });
        emailId = docRef.id;
        console.log(`📧 Created email record in Firestore: ${emailId}`);
      } catch (firestoreError) {
        console.error('❌ Error saving to Firestore:', firestoreError);
        emailId = `email_${Date.now()}`;
        console.log(`📧 Using fallback email ID: ${emailId}`);
      }

      let successCount = 0;
      let failedCount = 0;
      const errors: string[] = [];

      // Send emails in batches
      const batchSize = 5;
      const updatedRecipients = [...emailRecord.recipients];

      for (let i = 0; i < users.length; i += batchSize) {
        const batch = users.slice(i, i + batchSize);
        console.log(`📧 Processing batch ${Math.floor(i/batchSize) + 1}, users ${i+1}-${Math.min(i+batchSize, users.length)}`);

        const batchPromises = batch.map(async (user, batchIndex) => {
          const globalIndex = i + batchIndex;

          // Use processed content if available, otherwise use original
          const emailSubject = user.processedSubject || subject;
          const emailHtmlContent = user.processedHtmlContent || htmlContent;
          const emailTextContent = user.processedTextContent || content;

          console.log(`📧 Sending email to: ${user.email}`);
          const result = await this.sendSingleEmail(
            user.email,
            user.displayName || user.firstName || 'User',
            emailSubject,
            emailTextContent,
            emailHtmlContent
          );

          if (result.success) {
            successCount++;
            updatedRecipients[globalIndex] = {
              ...updatedRecipients[globalIndex],
              status: 'sent',
              sentAt: new Date()
            };
            console.log(`✅ Email sent successfully to: ${user.email}`);
          } else {
            failedCount++;
            updatedRecipients[globalIndex] = {
              ...updatedRecipients[globalIndex],
              status: 'failed',
              error: result.error
            };
            errors.push(`${user.email}: ${result.error}`);
            console.log(`❌ Email failed for: ${user.email} - ${result.error}`);
          }
        });

        await Promise.all(batchPromises);

        // Small delay between batches
        if (i + batchSize < users.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Update the email record with final results
      try {
        await this.updateEmailRecord(emailId, {
          recipients: updatedRecipients,
          successCount,
          failedCount,
          completedAt: new Date(),
          status: 'completed'
        });
        console.log(`📧 Updated email record in Firestore: ${successCount} sent, ${failedCount} failed`);
      } catch (updateError) {
        console.error('❌ Error updating email record:', updateError);
        console.log(`📧 Results: ${successCount} sent, ${failedCount} failed`);
      }

      console.log(`📧 Bulk email completed: ${successCount} sent, ${failedCount} failed`);

      return {
        success: failedCount < users.length, // Success if at least some emails were sent
        sentCount: successCount,
        failedCount,
        errors,
        emailId
      };
    } catch (error) {
      console.error('❌ Error in bulk email send:', error);
      throw error;
    }
  }

  /**
   * Update email record in Firestore
   */
  private async updateEmailRecord(emailId: string, updates: Partial<SentEmail>): Promise<void> {
    try {
      const emailRef = doc(db, 'sentEmails', emailId);
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      };

      // Convert Date objects to Firestore Timestamps
      if (updates.completedAt) {
        updateData.completedAt = Timestamp.fromDate(updates.completedAt);
      }

      await updateDoc(emailRef, updateData);
      console.log(`📝 Successfully updated email record ${emailId} in Firestore`);
    } catch (error) {
      console.error('❌ Error updating email record in Firestore:', error);
      // Don't throw error - email was sent, just logging failed
    }
  }

  /**
   * Get sent emails history
   */
  async getSentEmails(limitCount: number = 50): Promise<SentEmail[]> {
    try {
      const emailsRef = collection(db, 'sentEmails');
      const q = query(emailsRef, orderBy('createdAt', 'desc'), limit(limitCount));
      const snapshot = await getDocs(q);

      const sentEmails: SentEmail[] = [];
      snapshot.docs.forEach(doc => {
        const data = doc.data();
        sentEmails.push({
          id: doc.id,
          subject: data.subject,
          content: data.content,
          htmlContent: data.htmlContent,
          recipients: data.recipients || [],
          totalRecipients: data.totalRecipients || 0,
          successCount: data.successCount || 0,
          failedCount: data.failedCount || 0,
          templateUsed: data.templateUsed,
          sentBy: data.sentBy,
          sentByName: data.sentByName,
          createdAt: data.createdAt?.toDate() || new Date(),
          completedAt: data.completedAt?.toDate(),
          status: data.status || 'pending'
        });
      });

      return sentEmails;
    } catch (error) {
      console.error('❌ Error fetching sent emails:', error);
      return [];
    }
  }

  /**
   * Get email statistics
   */
  async getEmailStatistics(): Promise<{
    totalEmailsSent: number;
    totalRecipients: number;
    successRate: number;
    recentActivity: SentEmail[];
  }> {
    try {
      const sentEmails = await this.getSentEmails(100);
      
      const totalEmailsSent = sentEmails.length;
      const totalRecipients = sentEmails.reduce((sum, email) => sum + email.totalRecipients, 0);
      const totalSuccessful = sentEmails.reduce((sum, email) => sum + email.successCount, 0);
      const successRate = totalRecipients > 0 ? (totalSuccessful / totalRecipients) * 100 : 0;

      return {
        totalEmailsSent,
        totalRecipients,
        successRate,
        recentActivity: sentEmails.slice(0, 10)
      };
    } catch (error) {
      console.error('❌ Error calculating email statistics:', error);
      return {
        totalEmailsSent: 0,
        totalRecipients: 0,
        successRate: 0,
        recentActivity: []
      };
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration(testEmail: string): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await this.sendSingleEmail(
        testEmail,
        'Test User',
        'FreeCodeLap Email Test',
        'This is a test email from FreeCodeLap to verify email configuration.',
        '<p>This is a test email from <strong>FreeCodeLap</strong> to verify email configuration.</p>'
      );

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

export const realEmailService = RealEmailService.getInstance();
