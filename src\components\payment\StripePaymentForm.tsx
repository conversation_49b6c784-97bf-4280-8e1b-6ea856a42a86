import React, { useState } from 'react';
import { PaystackButton } from 'react-paystack';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Lock, Smartphone } from 'lucide-react';
import { paymentService } from '@/services/paymentService';
import { PaymentData } from '@/services/paymentService';

interface PaystackPaymentFormProps {
  paymentData: PaymentData;
  onSuccess: (result: any) => void;
  onError: (error: string) => void;
}

const PaystackPaymentForm: React.FC<PaystackPaymentFormProps> = ({
  paymentData,
  onSuccess,
  onError,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create Paystack configuration
  const paystackConfig = paymentService.createPaystackConfig(paymentData);

  const handlePaystackSuccess = async (response: any) => {
    setIsProcessing(true);
    setError(null);

    try {
      // Verify the transaction with Paystack
      const verificationResult = await paymentService.verifyPaystackTransaction(response.reference);

      if (verificationResult.status === 'success') {
        onSuccess({
          transaction_id: verificationResult.transaction_id,
          reference: response.reference,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_method: paymentData.payment_method,
          paystack_response: response,
        });
      } else {
        setError(verificationResult.message);
        onError(verificationResult.message);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Payment verification failed. Please try again.';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaystackClose = () => {
    setError('Payment was cancelled');
    onError('Payment was cancelled');
  };

  const getPaymentMethodIcon = () => {
    switch (paymentData.payment_method) {
      case 'mpesa':
        return <Smartphone className="w-5 h-5 mr-2 text-green-400" />;
      case 'card':
      default:
        return <CreditCard className="w-5 h-5 mr-2 text-blue-400" />;
    }
  };

  const getPaymentMethodTitle = () => {
    switch (paymentData.payment_method) {
      case 'mpesa':
        return 'M-Pesa Payment';
      case 'card':
      default:
        return 'Card Payment';
    }
  };

  return (
    <Card className="card-elevated">
      <CardHeader>
        <CardTitle className="flex items-center text-white">
          {getPaymentMethodIcon()}
          {getPaymentMethodTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Payment Information */}
        <div className="space-y-4">
          <div className="bg-blue-dark-800/50 p-4 rounded-lg border border-blue-dark-600">
            <h4 className="text-white font-medium mb-2">Payment Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-200">Amount:</span>
                <span className="text-white font-medium">
                  {paymentService.formatCurrency(paymentData.amount, paymentData.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-200">Course:</span>
                <span className="text-white font-medium">{paymentData.course_title}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-200">Payment Method:</span>
                <span className="text-white font-medium capitalize">
                  {paymentData.payment_method === 'mpesa' ? 'M-Pesa' : 'Card'}
                </span>
              </div>
            </div>
          </div>

          {/* Payment Method Info */}
          {paymentData.payment_method === 'mpesa' && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Smartphone className="w-5 h-5 text-green-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-green-400 font-medium mb-1">M-Pesa Payment</p>
                  <p className="text-green-300">
                    You'll be redirected to complete payment with M-Pesa
                  </p>
                </div>
              </div>
            </div>
          )}

          {paymentData.payment_method === 'card' && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CreditCard className="w-5 h-5 text-blue-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-blue-400 font-medium mb-1">Card Payment</p>
                  <p className="text-blue-300">
                    Secure payment with Visa, Mastercard, and other cards
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Security Notice */}
        <div className="flex items-center space-x-2 text-sm text-blue-300">
          <Lock className="w-4 h-4" />
          <span>Your payment information is secure and encrypted</span>
        </div>

        {/* Paystack Payment Button */}
        <PaystackButton
          {...paystackConfig}
          onSuccess={handlePaystackSuccess}
          onClose={handlePaystackClose}
          className="w-full btn-animated btn-blue py-3 text-white rounded-lg font-medium disabled:opacity-50"
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Verifying Payment...
            </>
          ) : (
            <>
              {getPaymentMethodIcon()}
              Pay {paymentService.formatCurrency(paymentData.amount, paymentData.currency)}
            </>
          )}
        </PaystackButton>
      </CardContent>
    </Card>
  );
};

export default PaystackPaymentForm;
