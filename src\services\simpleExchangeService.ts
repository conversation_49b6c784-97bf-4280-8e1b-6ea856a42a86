/**
 * Simple Exchange Rate Service for FreeCodeLap
 * Handles USD to local currency conversion for display purposes only
 */

import { collection, doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface ExchangeRate {
  currency: string;
  rate: number;
  symbol: string;
  name: string;
  lastUpdated: Date;
}

export interface SupportedCurrency {
  code: string;
  name: string;
  symbol: string;
  flag: string;
  countries: string[];
}

class SimpleExchangeService {
  private rateCache: { [currency: string]: ExchangeRate } = {};
  private cacheExpiry = 3600000; // 1 hour
  private lastUpdate: number = 0;

  // Supported currencies for display
  private supportedCurrencies: SupportedCurrency[] = [
    // Major currencies
    {
      code: 'USD',
      name: 'US Dollar',
      symbol: '$',
      flag: '🇺🇸',
      countries: ['US']
    },
    {
      code: 'EUR',
      name: 'Euro',
      symbol: '€',
      flag: '🇪🇺',
      countries: ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'IE', 'PT', 'FI', 'LU']
    },
    {
      code: 'GBP',
      name: 'British Pound',
      symbol: '£',
      flag: '🇬🇧',
      countries: ['GB']
    },
    {
      code: 'JPY',
      name: 'Japanese Yen',
      symbol: '¥',
      flag: '🇯🇵',
      countries: ['JP']
    },
    {
      code: 'CAD',
      name: 'Canadian Dollar',
      symbol: 'C$',
      flag: '🇨🇦',
      countries: ['CA']
    },
    {
      code: 'AUD',
      name: 'Australian Dollar',
      symbol: 'A$',
      flag: '🇦🇺',
      countries: ['AU']
    },
    {
      code: 'CHF',
      name: 'Swiss Franc',
      symbol: 'CHF',
      flag: '🇨🇭',
      countries: ['CH']
    },

    // African currencies
    {
      code: 'KES',
      name: 'Kenyan Shilling',
      symbol: 'KSh',
      flag: '🇰🇪',
      countries: ['KE']
    },
    {
      code: 'NGN',
      name: 'Nigerian Naira',
      symbol: '₦',
      flag: '🇳🇬',
      countries: ['NG']
    },
    {
      code: 'GHS',
      name: 'Ghanaian Cedi',
      symbol: 'GH₵',
      flag: '🇬🇭',
      countries: ['GH']
    },
    {
      code: 'ZAR',
      name: 'South African Rand',
      symbol: 'R',
      flag: '🇿🇦',
      countries: ['ZA']
    },
    {
      code: 'EGP',
      name: 'Egyptian Pound',
      symbol: 'E£',
      flag: '🇪🇬',
      countries: ['EG']
    },
    {
      code: 'MAD',
      name: 'Moroccan Dirham',
      symbol: 'MAD',
      flag: '🇲🇦',
      countries: ['MA']
    },
    {
      code: 'TND',
      name: 'Tunisian Dinar',
      symbol: 'TND',
      flag: '🇹🇳',
      countries: ['TN']
    },
    {
      code: 'UGX',
      name: 'Ugandan Shilling',
      symbol: 'USh',
      flag: '🇺🇬',
      countries: ['UG']
    },
    {
      code: 'TZS',
      name: 'Tanzanian Shilling',
      symbol: 'TSh',
      flag: '🇹🇿',
      countries: ['TZ']
    },
    {
      code: 'RWF',
      name: 'Rwandan Franc',
      symbol: 'RWF',
      flag: '🇷🇼',
      countries: ['RW']
    },

    // Asian currencies
    {
      code: 'INR',
      name: 'Indian Rupee',
      symbol: '₹',
      flag: '🇮🇳',
      countries: ['IN']
    },
    {
      code: 'CNY',
      name: 'Chinese Yuan',
      symbol: '¥',
      flag: '🇨🇳',
      countries: ['CN']
    },
    {
      code: 'KRW',
      name: 'South Korean Won',
      symbol: '₩',
      flag: '🇰🇷',
      countries: ['KR']
    },
    {
      code: 'SGD',
      name: 'Singapore Dollar',
      symbol: 'S$',
      flag: '🇸🇬',
      countries: ['SG']
    },
    {
      code: 'MYR',
      name: 'Malaysian Ringgit',
      symbol: 'RM',
      flag: '🇲🇾',
      countries: ['MY']
    },
    {
      code: 'THB',
      name: 'Thai Baht',
      symbol: '฿',
      flag: '🇹🇭',
      countries: ['TH']
    },
    {
      code: 'PHP',
      name: 'Philippine Peso',
      symbol: '₱',
      flag: '🇵🇭',
      countries: ['PH']
    },
    {
      code: 'IDR',
      name: 'Indonesian Rupiah',
      symbol: 'Rp',
      flag: '🇮🇩',
      countries: ['ID']
    },
    {
      code: 'VND',
      name: 'Vietnamese Dong',
      symbol: '₫',
      flag: '🇻🇳',
      countries: ['VN']
    },
    {
      code: 'BDT',
      name: 'Bangladeshi Taka',
      symbol: '৳',
      flag: '🇧🇩',
      countries: ['BD']
    },
    {
      code: 'PKR',
      name: 'Pakistani Rupee',
      symbol: 'Rs',
      flag: '🇵🇰',
      countries: ['PK']
    },

    // Other currencies
    {
      code: 'BRL',
      name: 'Brazilian Real',
      symbol: 'R$',
      flag: '🇧🇷',
      countries: ['BR']
    },
    {
      code: 'MXN',
      name: 'Mexican Peso',
      symbol: '$',
      flag: '🇲🇽',
      countries: ['MX']
    },
    {
      code: 'ARS',
      name: 'Argentine Peso',
      symbol: '$',
      flag: '🇦🇷',
      countries: ['AR']
    },
    {
      code: 'CLP',
      name: 'Chilean Peso',
      symbol: '$',
      flag: '🇨🇱',
      countries: ['CL']
    },
    {
      code: 'COP',
      name: 'Colombian Peso',
      symbol: '$',
      flag: '🇨🇴',
      countries: ['CO']
    },
    {
      code: 'PEN',
      name: 'Peruvian Sol',
      symbol: 'S/',
      flag: '🇵🇪',
      countries: ['PE']
    },
    {
      code: 'AED',
      name: 'UAE Dirham',
      symbol: 'AED',
      flag: '🇦🇪',
      countries: ['AE']
    },
    {
      code: 'SAR',
      name: 'Saudi Riyal',
      symbol: 'SR',
      flag: '🇸🇦',
      countries: ['SA']
    },
    {
      code: 'ILS',
      name: 'Israeli Shekel',
      symbol: '₪',
      flag: '🇮🇱',
      countries: ['IL']
    },
    {
      code: 'TRY',
      name: 'Turkish Lira',
      symbol: '₺',
      flag: '🇹🇷',
      countries: ['TR']
    },
    {
      code: 'NZD',
      name: 'New Zealand Dollar',
      symbol: 'NZ$',
      flag: '🇳🇿',
      countries: ['NZ']
    },
    {
      code: 'SEK',
      name: 'Swedish Krona',
      symbol: 'kr',
      flag: '🇸🇪',
      countries: ['SE']
    },
    {
      code: 'NOK',
      name: 'Norwegian Krone',
      symbol: 'kr',
      flag: '🇳🇴',
      countries: ['NO']
    },
    {
      code: 'DKK',
      name: 'Danish Krone',
      symbol: 'kr',
      flag: '🇩🇰',
      countries: ['DK']
    }
  ];

  /**
   * Detect user's local currency based on location
   */
  async detectUserCurrency(): Promise<string> {
    try {
      // Try IP-based detection
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      const countryCode = data.country_code;
      
      // Find currency for country
      for (const currency of this.supportedCurrencies) {
        if (currency.countries.includes(countryCode)) {
          return currency.code;
        }
      }
      
      // Fallback to browser locale
      return this.detectFromBrowserLocale();
    } catch (error) {
      console.error('Error detecting currency:', error);
      return this.detectFromBrowserLocale();
    }
  }

  /**
   * Detect currency from browser locale
   */
  private detectFromBrowserLocale(): string {
    try {
      const locale = navigator.language || 'en-US';
      const localeMap: { [key: string]: string } = {
        'en-KE': 'KES',
        'sw-KE': 'KES',
        'en-NG': 'NGN',
        'ha-NG': 'NGN',
        'en-GH': 'GHS',
        'tw-GH': 'GHS',
        'en-ZA': 'ZAR',
        'af-ZA': 'ZAR',
        'en-GB': 'GBP',
        'en-CA': 'CAD',
        'fr-CA': 'CAD',
        'en-AU': 'AUD',
        'en-IN': 'INR',
        'hi-IN': 'INR'
      };
      
      return localeMap[locale] || 'USD';
    } catch (error) {
      return 'USD';
    }
  }

  /**
   * Get exchange rate for currency (USD to target currency)
   */
  async getExchangeRate(currency: string): Promise<number> {
    if (currency === 'USD') return 1;

    // Check cache
    const cached = this.rateCache[currency];
    const now = Date.now();
    
    if (cached && (now - this.lastUpdate) < this.cacheExpiry) {
      return cached.rate;
    }

    // Update rates if needed
    await this.updateExchangeRates();
    
    return this.rateCache[currency]?.rate || 1;
  }

  /**
   * Convert USD amount to target currency
   */
  async convertFromUSD(usdAmount: number, targetCurrency: string): Promise<number> {
    if (targetCurrency === 'USD') return usdAmount;
    
    const rate = await this.getExchangeRate(targetCurrency);
    const converted = usdAmount * rate;
    
    // Round appropriately for currency
    return this.roundForCurrency(converted, targetCurrency);
  }

  /**
   * Format amount with currency symbol
   */
  formatCurrency(amount: number, currency: string): string {
    const currencyInfo = this.supportedCurrencies.find(c => c.code === currency);
    const symbol = currencyInfo?.symbol || currency;
    
    // Format based on currency
    if (['KES', 'NGN'].includes(currency)) {
      // No decimals for these currencies
      return `${symbol}${Math.round(amount).toLocaleString()}`;
    } else {
      // 2 decimals for others
      return `${symbol}${amount.toFixed(2)}`;
    }
  }

  /**
   * Get currency info
   */
  getCurrencyInfo(currency: string): SupportedCurrency | null {
    return this.supportedCurrencies.find(c => c.code === currency) || null;
  }

  /**
   * Get all supported currencies
   */
  getSupportedCurrencies(): SupportedCurrency[] {
    return this.supportedCurrencies;
  }

  /**
   * Update exchange rates from API
   */
  private async updateExchangeRates(): Promise<void> {
    try {
      console.log('🔄 Updating exchange rates...');
      
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (!response.ok) {
        throw new Error('Failed to fetch exchange rates');
      }
      
      const data = await response.json();
      
      // Update cache
      for (const currency of this.supportedCurrencies) {
        if (data.rates[currency.code]) {
          this.rateCache[currency.code] = {
            currency: currency.code,
            rate: data.rates[currency.code],
            symbol: currency.symbol,
            name: currency.name,
            lastUpdated: new Date()
          };
        }
      }
      
      this.lastUpdate = Date.now();
      
      // Save to Firestore for persistence
      await this.saveRatesToFirestore();
      
      console.log('✅ Exchange rates updated successfully');
    } catch (error) {
      console.error('❌ Error updating exchange rates:', error);
      this.useFallbackRates();
    }
  }

  /**
   * Use fallback exchange rates
   */
  private useFallbackRates(): void {
    console.log('⚠️ Using fallback exchange rates');

    const fallbackRates = {
      // Major currencies
      'EUR': 0.85,
      'GBP': 0.73,
      'JPY': 110.0,
      'CAD': 1.35,
      'AUD': 1.50,
      'CHF': 0.92,

      // African currencies
      'KES': 130.0,
      'NGN': 460.0,
      'GHS': 12.0,
      'ZAR': 18.5,
      'EGP': 31.0,
      'MAD': 10.0,
      'TND': 3.1,
      'UGX': 3700.0,
      'TZS': 2300.0,
      'RWF': 1000.0,

      // Asian currencies
      'INR': 83.0,
      'CNY': 7.2,
      'KRW': 1300.0,
      'SGD': 1.35,
      'MYR': 4.7,
      'THB': 35.0,
      'PHP': 56.0,
      'IDR': 15000.0,
      'VND': 24000.0,
      'BDT': 110.0,
      'PKR': 280.0,

      // Other currencies
      'BRL': 5.0,
      'MXN': 17.0,
      'ARS': 350.0,
      'CLP': 900.0,
      'COP': 4000.0,
      'PEN': 3.7,
      'AED': 3.67,
      'SAR': 3.75,
      'ILS': 3.7,
      'TRY': 27.0,
      'NZD': 1.65,
      'SEK': 10.5,
      'NOK': 10.8,
      'DKK': 6.9
    };
    
    for (const [code, rate] of Object.entries(fallbackRates)) {
      const currencyInfo = this.supportedCurrencies.find(c => c.code === code);
      if (currencyInfo) {
        this.rateCache[code] = {
          currency: code,
          rate,
          symbol: currencyInfo.symbol,
          name: currencyInfo.name,
          lastUpdated: new Date()
        };
      }
    }
    
    this.lastUpdate = Date.now();
  }

  /**
   * Save rates to Firestore
   */
  private async saveRatesToFirestore(): Promise<void> {
    try {
      await setDoc(doc(db, 'system', 'exchangeRates'), {
        rates: this.rateCache,
        lastUpdated: new Date(),
        baseCurrency: 'USD'
      });
    } catch (error) {
      console.error('Error saving rates to Firestore:', error);
    }
  }

  /**
   * Load rates from Firestore
   */
  async loadRatesFromFirestore(): Promise<void> {
    try {
      const ratesDoc = await getDoc(doc(db, 'system', 'exchangeRates'));
      if (ratesDoc.exists()) {
        const data = ratesDoc.data();
        const lastUpdated = data.lastUpdated?.toDate();
        
        // Check if data is recent (within 1 hour)
        if (lastUpdated && (Date.now() - lastUpdated.getTime()) < this.cacheExpiry) {
          this.rateCache = data.rates;
          this.lastUpdate = lastUpdated.getTime();
          console.log('✅ Loaded exchange rates from Firestore');
          return;
        }
      }
    } catch (error) {
      console.error('Error loading rates from Firestore:', error);
    }
    
    // If no valid cached data, update from API
    await this.updateExchangeRates();
  }

  /**
   * Round amount appropriately for currency
   */
  private roundForCurrency(amount: number, currency: string): number {
    if (['KES', 'NGN'].includes(currency)) {
      // Round to nearest whole number
      return Math.round(amount);
    } else {
      // Round to 2 decimal places
      return Math.round(amount * 100) / 100;
    }
  }

  /**
   * Initialize service
   */
  async initialize(): Promise<void> {
    await this.loadRatesFromFirestore();
    
    // Schedule periodic updates (every hour)
    setInterval(() => {
      this.updateExchangeRates();
    }, this.cacheExpiry);
  }
}

export const simpleExchangeService = new SimpleExchangeService();
