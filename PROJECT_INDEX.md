# FreeCodeLap Platform - Project Index

This document provides an overview of the FreeCodeLap platform, a FlutterFlow learning platform built with React, TypeScript, and Firebase.

## Project Overview

- **Framework**: React with Vite
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Firebase (Authentication, Firestore)
- **Payment Processing**: Paystack
- **UI Components**: Radix UI, Custom Components

## Project Structure

```
├── backend-examples/
├── public/
├── src/
│   ├── components/
│   ├── contexts/
│   ├── hooks/
│   ├── lib/
│   ├── pages/
│   ├── services/
│   ├── types/
│   └── utils/
├── index.html
├── package.json
└── ...
```

## Main Entry Points

### index.html
The main HTML file that serves as the entry point for the application.

### src/main.tsx
The main TypeScript file that renders the React application.

### src/App.tsx
The main App component that sets up routing and providers.

## Components

### UI Components (`src/components/ui/`)
Reusable UI components built with Radix UI:
- accordion.tsx
- alert.tsx
- alert-dialog.tsx
- avatar.tsx
- badge.tsx
- button.tsx
- calendar.tsx
- card.tsx
- carousel.tsx
- checkbox.tsx
- dialog.tsx
- dropdown-menu.tsx
- form.tsx
- input.tsx
- label.tsx
- loading.tsx
- progress.tsx
- select.tsx
- separator.tsx
- sonner.tsx
- switch.tsx
- tabs.tsx
- textarea.tsx
- toast.tsx
- toaster.tsx
- tooltip.tsx
- use-toast.ts

### Layout Components (`src/components/`)
Main layout and section components:
- AboutInstructorSection.tsx - Instructor information section
- AppFallback.tsx - Fallback component for app errors
- ContactSection.tsx - Contact form section
- CourseCatalogSection.tsx - Course catalog display
- EmailCaptureSection.tsx - Email collection form
- ErrorBoundary.tsx - Error boundary component
- FAQSection.tsx - Frequently asked questions section
- FeaturedCoursesSection.tsx - Featured courses display
- FinalCTASection.tsx - Final call-to-action section
- FirebaseFallback.tsx - Fallback component for Firebase errors
- Footer.tsx - Site footer
- HeroSection.tsx - Main hero section
- HowItWorksSection.tsx - How-it-works explanation section
- LiveClassesContactSection.tsx - Live classes contact section
- Navigation.tsx - Main navigation component
- TestimonialsSection.tsx - Testimonials display
- WhatYoullLearnSection.tsx - Learning outcomes section
- WhyFreeCodeLapSection.tsx - Platform value proposition

### Admin Components (`src/components/admin/`)
Components for admin functionality:
- ComprehensiveCourseForm.tsx - Full course creation/editing form
- CourseManagement.tsx - Course management interface
- CreateCourseForm.tsx - Simple course creation form
- SimpleCourseForm.tsx - Simplified course form
- UserManagement.tsx - User management interface

### Authentication Components (`src/components/auth/`)
Authentication-related components:
- AuthModal.tsx - Authentication modal dialog
- ProtectedRoute.tsx - Route protection component
- RoleBasedAccess.tsx - Role-based access control
- UserMenu.tsx - User menu dropdown

### Course Components (`src/components/course/`)
Course-specific components:
- SimpleVideoPlayer.tsx - Basic video player

### Course Components (`src/components/courses/`)
Course listing components:
- CourseCard.tsx - Individual course card component

### Debug Components (`src/components/debug/`)
Development/debugging components:
- PaystackTest.tsx - Paystack integration testing
- QuickVideoTest.tsx - Quick video testing
- VideoTest.tsx - Video testing component
- VideoTroubleshooter.tsx - Video troubleshooting tool

### Legal Components (`src/components/legal/`)
Legal-related components:
- CookieConsent.tsx - Cookie consent banner

### Payment Components (`src/components/payment/`)
Payment processing components:
- CardInput.tsx - Card input form
- CourseCheckout.tsx - Course checkout process
- LiveModeStatus.tsx - Live mode status indicator
- PaymentMethodSelector.tsx - Payment method selector
- PaymentMethodTabs.tsx - Payment method tabs
- PaymentSuccess.tsx - Payment success confirmation
- PaystackButton.tsx - Paystack payment button
- PaystackCheckout.tsx - Paystack checkout process
- PaystackPayment.tsx - Paystack payment component
- SecurePaymentForm.tsx - Secure payment form
- StripePaymentForm.tsx - Stripe payment form
- StripeStyleCheckout.tsx - Stripe-style checkout

### Video Components (`src/components/video/`)
Video player components:
- (Video players will be implemented later)

## Context Providers (`src/contexts/`)
State management contexts:
- AuthContext.tsx - Authentication state and functions
- CourseContext.tsx - Course data and functions
- PaymentContext.tsx - Payment state and functions

## Hooks (`src/hooks/`)
Custom React hooks:
- use-mobile.tsx - Mobile device detection
- use-toast.ts - Toast notification hook
- useCourseEnrollment.ts - Course enrollment functionality

## Library (`src/lib/`)
Core library functions:
- firebase.ts - Firebase configuration and initialization
- firestore.ts - Firestore database functions
- paystack.ts - Paystack payment integration
- utils.ts - Utility functions

## Pages (`src/pages/`)
Application pages:
- AdminPanel.tsx - Admin dashboard
- AllCourses.tsx - All courses listing
- Billing.tsx - Billing information page
- ComprehensiveCourseForm.tsx - Comprehensive course form page
- CookiePolicy.tsx - Cookie policy page
- CourseCheckoutPage.tsx - Course checkout page
- CourseDetail.tsx - Course detail page
- Dashboard.tsx - User dashboard
- ForgotPassword.tsx - Forgot password page
- Index.tsx - Home page
- LiveClasses.tsx - Live classes page
- Login.tsx - Login page
- MyCourses.tsx - User's courses page
- NewCourseContent.tsx - New course content page
- NotFound.tsx - 404 page
- PrivacyPolicy.tsx - Privacy policy page
- Profile.tsx - User profile page
- RefundPolicy.tsx - Refund policy page
- Register.tsx - Registration page
- Settings.tsx - User settings page
- TermsOfService.tsx - Terms of service page

## Services (`src/services/`)
Business logic services:
- courseDataService.ts - Course data operations
- courseService.ts - Course-related functions
- enrollmentService.ts - Course enrollment functions
- firestoreService.ts - Firestore database operations
- paymentService.ts - Payment processing functions
- roleService.ts - User role management

## Types (`src/types/`)
TypeScript type definitions:
- course.ts - Course-related types (Course, Module, Lesson, Assignment, Quiz, etc.)
- payment.ts - Payment-related types (Payment, PaymentIntent, PaymentResult, etc.)
- schema.ts - Data schema types
- user.ts - User-related types (UserProfile, CourseProgress, Certificate, PaymentRecord, etc.)

## Utilities (`src/utils/`)
Utility functions and guides:
- backblazeB2Guide.md - Backblaze B2 storage guide
- courseMigration.ts - Course migration utilities
- createTestCourse.ts - Test course creation
- firestoreUtils.ts - Firestore utility functions
- videoPlayerSolution.md - Video player solution documentation

## Configuration Files

### package.json
Project dependencies and scripts:
- Dependencies: React, Firebase, Radix UI, Tailwind CSS, Paystack
- Scripts: dev, build, lint, preview

### .env.example
Environment variables configuration:
- Firebase configuration (API key, Auth domain, Project ID, etc.)
- Paystack configuration (Public and Secret keys)
- Business information (Name, Email, Phone)
- Environment settings (development/production)
- App configuration (Name, URL)

### vite.config.ts
Vite configuration for the project:
- Server configuration (host: "::", port: 8080)
- React plugin with SWC
- Component tagger for development mode
- Path alias configuration (@ for src directory)

### tailwind.config.ts
Tailwind CSS configuration:
- Dark mode support
- Content paths for TypeScript files
- Custom font family (Poppins)
- Extended color palette with HSL values
- Custom border radius values
- Accordion animations
- Tailwind CSS animate plugin

### postcss.config.js
PostCSS configuration:
- Tailwind CSS plugin
- Autoprefixer plugin

### components.json
shadcn/ui configuration:
- Style: default
- RSC (React Server Components): disabled
- TypeScript: enabled
- Tailwind configuration references
- Path aliases for components, utils, UI, lib, and hooks

### tsconfig.json
TypeScript configuration:
- References to tsconfig.app.json and tsconfig.node.json
- Base URL configuration for path aliases
- Path alias configuration (@ for src directory)
- Disabled strict mode for easier development

### tsconfig.app.json
TypeScript configuration for application code:
- Target: ES2020
- Module resolution: bundler
- JSX support for React
- Path alias configuration (@ for src directory)
- Includes src directory

### tsconfig.node.json
TypeScript configuration for Node.js environment (Vite config):
- Target: ES2022
- Module resolution: bundler
- Strict mode enabled
- Includes vite.config.ts

### firestore.rules
Firebase Firestore security rules.

## Key Features

1. **Authentication System**
   - User registration and login
   - Password reset functionality
   - Role-based access control (user, admin)
   - Google Sign-In integration
   - Email/password authentication
   - Real-time authentication debugging (AuthDebug panel)

2. **Course Management**
   - Comprehensive course creation and editing system
   - Course content delivery with modules, lessons, assignments, and quizzes
   - Video player integration with progress tracking
   - Progress tracking and completion certificates
   - Course preview functionality
   - Course categorization and tagging
   - Course ratings and reviews
   - Enrollment management
   - Access control (lifetime, subscription, limited)

3. **Payment Processing**
   - Paystack integration
   - Secure payment forms
   - Payment status management

4. **Admin Panel**
   - Course management
   - User management
   - Content creation tools
   - Assignment and quiz management
   - Certificate generation

5. **Responsive Design**
   - Mobile-friendly interface
   - Adaptive layouts

6. **Security**
   - Firebase authentication
   - Protected routes
   - Secure payment processing
   - Content Security Policy
   - SSL requirement enforcement

## Development Features

1. **Debugging Tools**
   - Video player testing components
   - Payment integration testing
   - Course structure validation
   - Authentication debugging (AuthDebug panel)
   - Comprehensive console logging

2. **Documentation**
   - Setup guides for Firebase, Paystack, etc.
   - Troubleshooting guides
   - Security documentation
   - Testing procedures

## Development Features

1. **Debugging Tools**
   - Video player testing components
   - Payment integration testing
   - Course structure validation

2. **Documentation**
   - Setup guides for Firebase, Paystack, etc.
   - Troubleshooting guides
   - Security documentation

## Routes

The application uses React Router with the following routes:

- `/` - Home page
- `/login` - Login page
- `/register` - Registration page
- `/forgot-password` - Password reset page
- `/dashboard` - User dashboard
- `/course/:courseId` - Course detail page
- `/course/:courseId/preview` - Course preview
- `/course/:courseId/content` - Course content
- `/course/:courseId/learn` - Learning interface
- `/course/:courseId/lesson/:lessonId` - Specific lesson
- `/course/:courseId/play` - Course player
- `/admin` - Admin panel
- `/admin/panel` - Admin panel (alternative route)
- `/admin/course-form` - Course creation form
- `/admin/courses/edit/:courseId` - Course editing
- `/admin/courses` - Course management
- `/live-classes` - Live classes page
- `/courses` - All courses listing
- `/checkout/:courseId` - Course checkout
- `/profile` - User profile
- `/my-courses` - User's courses
- `/billing` - Billing information
- `/settings` - User settings
- `/privacy-policy` - Privacy policy
- `/terms-of-service` - Terms of service
- `/cookie-policy` - Cookie policy
- `/refund-policy` - Refund policy
- `*` - 404 page (catch-all)

## Payment Integration

The platform integrates with Paystack for payment processing:

### Features
- Secure payment forms with multiple payment channels (card, bank, USSD, QR, mobile money, bank transfer)
- Payment status tracking and verification
- Automatic enrollment creation upon successful payment
- Payment reference generation for transaction tracking
- Currency support (default KES)
- Payment webhook handling
- Discount code support
- Purchase history checking

### Implementation Details
- Paystack public key configuration via environment variables
- Custom Paystack configuration with metadata
- Payment success and error handling
- Integration with Firebase for payment and enrollment records
- Formatted amount display for different currencies
- Testing components for development (PaystackTest.tsx)

### Key Functions
- `createPaystackConfig()` - Creates Paystack configuration for course enrollment
- `handlePaymentSuccess()` - Processes successful payments and creates enrollments
- `handlePaymentClose()` - Handles payment cancellation
- `verifyPayment()` - Verifies payment status with Paystack API
- `handlePaymentWebhook()` - Processes Paystack webhooks
- `getPaymentStatus()` - Retrieves payment status
- `formatAmount()` - Formats amounts for display
- `calculateCoursePrice()` - Calculates course price with discounts
- `hasUserPurchasedCourse()` - Checks if user has purchased a course

### Payment Types
The system uses TypeScript interfaces for payment data:
- `Payment` - Core payment information (id, user_id, course_id, amount, currency, status, etc.)
- `PaymentIntent` - Payment initiation data
- `PaymentResult` - Payment processing results
- `PaymentMethodOption` - Available payment methods
- `CoursePrice` - Course pricing information
- `PaymentConfig` - Payment configuration settings
- `PaymentWebhook` - Webhook data structure

## Video Content

The platform supports video content through:
- Custom video players
- Multiple video sources (YouTube, Vimeo, direct links)
- Video troubleshooting tools
- Network testing capabilities

## Security Features

- Firebase authentication
- Protected routes with role-based access
- Content Security Policy in index.html
- Secure payment processing
- SSL requirement enforcement
