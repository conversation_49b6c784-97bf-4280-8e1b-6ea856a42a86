import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getFeaturedCourses } from '@/lib/firestore';
import { Course } from '@/types/schema';
// Removed old paystack lib import - using direct formatting now
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';
import { enrollmentsService } from '@/services/firestoreService';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import {
  BookOpen,
  Clock,
  Users,
  Star,
  PlayCircle,
  Award,
  CheckCircle,
  Code,
  Brain,
  Smartphone,
  Cpu,
  ArrowRight
} from 'lucide-react';

// Create sample courses if none exist
const createSampleCourses = async () => {
  const sampleCourses = [
    {
      id: 'flutterflow-basics',
      title: 'FlutterFlow No-Code Development',
      description: 'Master the art of building beautiful mobile and web applications without writing a single line of code.',
      instructor: '<PERSON> Takal',
      level: 'beginner' as const,
      price: 49.99,
      duration: 480,
      thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
      learningOutcomes: [
        'Build professional mobile apps with FlutterFlow',
        'Master advanced UI/UX design principles',
        'Integrate APIs and backend services'
      ],
      tags: ['FlutterFlow', 'No-Code', 'Mobile Development'],
      category: 'Development',
      isPublished: true,
      featured: true,
      enrollmentCount: 1200,
      rating: 4.8,
      reviewCount: 150
    },
    {
      id: 'ai-coding-mastery',
      title: 'AI-Powered Coding with Cursor & VS Code',
      description: 'Learn to code 10x faster using AI tools like Cursor, GitHub Copilot, and ChatGPT.',
      instructor: 'Ahmed Takal',
      level: 'intermediate' as const,
      price: 59.99,
      duration: 360,
      thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
      learningOutcomes: [
        'Master AI-assisted coding techniques',
        'Use Cursor and VS Code effectively',
        'Build projects 10x faster'
      ],
      tags: ['AI', 'Coding', 'Productivity'],
      category: 'Programming',
      isPublished: true,
      featured: true,
      enrollmentCount: 850,
      rating: 4.9,
      reviewCount: 95
    },
    {
      id: 'react-fundamentals',
      title: 'React Fundamentals for Beginners',
      description: 'Learn React from scratch and build modern web applications.',
      instructor: 'Ahmed Takal',
      level: 'beginner' as const,
      price: 39.99,
      duration: 300,
      thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
      learningOutcomes: [
        'Understand React components and JSX',
        'Manage state and props effectively',
        'Build interactive web applications'
      ],
      tags: ['React', 'JavaScript', 'Web Development'],
      category: 'Web Development',
      isPublished: true,
      featured: true,
      enrollmentCount: 2100,
      rating: 4.7,
      reviewCount: 280
    }
  ];

  try {
    for (const course of sampleCourses) {
      const courseRef = doc(db, 'courses', course.id);
      await setDoc(courseRef, course);
      console.log(`✅ Created sample course: ${course.title}`);
    }
    console.log('🎉 Sample courses created successfully!');
  } catch (error) {
    console.error('❌ Error creating sample courses:', error);
  }
};

const FeaturedCoursesSection = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  const handleEnrollClick = (courseId: string) => {
    if (!currentUser) {
      navigate('/login', {
        state: {
          from: { pathname: `/course/${courseId}` },
          message: 'Please sign in to enroll in this course'
        }
      });
      return;
    }

    // Navigate to course detail page for enrollment
    navigate(`/course/${courseId}`);
  };

  useEffect(() => {
    console.log('🌟 FeaturedCoursesSection: Setting up course loading...');
    console.log('🌟 FeaturedCoursesSection: Current user:', currentUser ? currentUser.email : 'Not logged in');

    // Load ALL courses from Firestore and show them
    const loadCoursesWithRealTimeData = async () => {
      try {
        // Try to get all courses first (this should work for both authenticated and non-authenticated users)
        const allCourses = await courseService.getAllCourses();

        if (allCourses && allCourses.length > 0) {

          // Get real-time enrollment and review data for each course (skip if fails)
          const coursesWithRealTimeData = await Promise.all(
            allCourses.map(async (course) => {
              try {
                // Use enrollment count from course data, don't require authentication
                let enrollmentCount = course.enrollmentCount || 0;

                // Only try to get real-time enrollment data if user is authenticated
                if (currentUser) {
                  try {
                    const enrollments = await enrollmentsService.getByCourse(course.id);
                    const activeEnrollments = enrollments.filter((e: any) => e.status === 'active');
                    enrollmentCount = activeEnrollments.length || course.enrollmentCount || 0;
                  } catch (enrollmentError) {
                    // Use default enrollment count from course data
                    enrollmentCount = course.enrollmentCount || 0;
                  }
                }

                return {
                  ...course,
                  enrollmentCount,
                  enrolledCount: enrollmentCount,
                  reviewCount: course.reviewCount || 0,
                  rating: Math.round((course.rating || 0) * 10) / 10
                };
              } catch (error) {
                return {
                  ...course,
                  enrollmentCount: course.enrollmentCount || 0,
                  enrolledCount: course.enrollmentCount || 0,
                  reviewCount: course.reviewCount || 0,
                  rating: Math.round((course.rating || 0) * 10) / 10
                };
              }
            })
          );

          setCourses(coursesWithRealTimeData);
          setLoading(false);
          return;
        }

        // If still no courses found, show fallback courses immediately
        const fallbackCourses = [
            {
              id: 'fallback-1',
              title: 'FlutterFlow No-Code Development',
              description: 'Master mobile app development without coding',
              instructor: 'Ahmed Takal',
              level: 'beginner' as const,
              price: 49.99,
              duration: 480,
              thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
              learningOutcomes: ['Build mobile apps', 'Master UI/UX', 'Integrate APIs'],
              tags: ['FlutterFlow', 'No-Code'],
              category: 'Development',
              isPublished: true,
              featured: true,
              enrollmentCount: 1200,
              rating: 4.8,
              reviewCount: 150
            },
            {
              id: 'fallback-2',
              title: 'AI-Powered Coding with Cursor & VS Code',
              description: 'Learn to code 10x faster using AI tools like Cursor, GitHub Copilot, and ChatGPT.',
              instructor: 'Ahmed Takal',
              level: 'intermediate' as const,
              price: 59.99,
              duration: 360,
              thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
              learningOutcomes: ['Master AI-assisted coding', 'Use Cursor effectively', 'Build projects faster'],
              tags: ['AI', 'Coding'],
              category: 'Programming',
              isPublished: true,
              featured: true,
              enrollmentCount: 850,
              rating: 4.9,
              reviewCount: 95
            }
        ];
        setCourses(fallbackCourses);
      } catch (error) {
        console.error('🌟 FeaturedCoursesSection: Error loading courses:', error);

        // Show fallback courses even if there's an error
        const fallbackCourses = [
          {
            id: 'fallback-course-1',
            title: 'FlutterFlow No-Code Development',
            description: 'Master the art of building beautiful mobile and web applications without writing a single line of code.',
            instructor: 'Ahmed Takal',
            level: 'beginner' as const,
            price: 49.99,
            duration: 480,
            thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
            learningOutcomes: ['Build professional mobile apps', 'Master UI/UX design', 'Integrate APIs'],
            tags: ['FlutterFlow', 'No-Code'],
            category: 'Development',
            isPublished: true,
            featured: true,
            enrollmentCount: 1200,
            rating: 4.8,
            reviewCount: 150
          },
          {
            id: 'fallback-course-2',
            title: 'AI-Powered Coding with Cursor & VS Code',
            description: 'Learn to code 10x faster using AI tools like Cursor, GitHub Copilot, and ChatGPT.',
            instructor: 'Ahmed Takal',
            level: 'intermediate' as const,
            price: 59.99,
            duration: 360,
            thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
            learningOutcomes: ['Master AI-assisted coding', 'Use Cursor effectively', 'Build projects faster'],
            tags: ['AI', 'Coding'],
            category: 'Programming',
            isPublished: true,
            featured: true,
            enrollmentCount: 850,
            rating: 4.9,
            reviewCount: 95
          }
        ];

        console.log('🌟 FeaturedCoursesSection: Showing fallback courses due to error');
        setCourses(fallbackCourses);
      } finally {
        setLoading(false);
      }
    };

    loadCoursesWithRealTimeData();

    // Set loading timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('⏰ FeaturedCoursesSection: Loading timeout - showing fallback courses');
        setLoading(false);

        // Show fallback courses if loading takes too long
        const timeoutFallbackCourses = [
          {
            id: 'timeout-fallback-1',
            title: 'FlutterFlow No-Code Development',
            description: 'Master mobile app development without coding',
            instructor: 'Ahmed Takal',
            level: 'beginner' as const,
            price: 49.99,
            duration: 480,
            thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
            learningOutcomes: ['Build mobile apps', 'Master UI/UX', 'Integrate APIs'],
            tags: ['FlutterFlow', 'No-Code'],
            category: 'Development',
            isPublished: true,
            featured: true,
            enrollmentCount: 1200,
            rating: 4.8,
            reviewCount: 150
          },
          {
            id: 'timeout-fallback-2',
            title: 'AI-Powered Coding with Cursor & VS Code',
            description: 'Learn to code 10x faster using AI tools',
            instructor: 'Ahmed Takal',
            level: 'intermediate' as const,
            price: 59.99,
            duration: 360,
            thumbnail: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
            learningOutcomes: ['Master AI-assisted coding', 'Use Cursor effectively', 'Build projects faster'],
            tags: ['AI', 'Coding'],
            category: 'Programming',
            isPublished: true,
            featured: true,
            enrollmentCount: 850,
            rating: 4.9,
            reviewCount: 95
          }
        ];
        setCourses(timeoutFallbackCourses);
      }
    }, 8000); // 8 second timeout

    // Cleanup timeout on unmount
    return () => {
      console.log('🧹 FeaturedCoursesSection: Cleaning up timeout');
      clearTimeout(loadingTimeout);
    };
  }, [currentUser]); // Re-run when authentication state changes

  const skillLevelColors = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  };

  const getCourseIcon = (courseId: string) => {
    if (courseId.includes('flutterflow')) {
      return <Smartphone className="h-8 w-8 text-blue-500" />;
    } else if (courseId.includes('vibe-coding')) {
      return <Brain className="h-8 w-8 text-purple-500" />;
    }
    return <Code className="h-8 w-8 text-gray-500" />;
  };

  if (loading) {
    return (
      <section id="courses-loading-section" className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Featured Courses
            </h2>
            <div className="flex justify-center space-x-4">
              {[1, 2].map((i) => (
                <div key={i} className="w-96 h-64 bg-gray-800 rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="courses-section" className="py-20 bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            All Courses
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Master the future of development with our comprehensive courses in No-Code and AI-powered coding
          </p>
          <p className="text-sm text-gray-400 mt-2">
            Showing {courses.length} course{courses.length !== 1 ? 's' : ''} available
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {courses.map((course) => (
            <Card
              key={course.id}
              className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gray-800 border-gray-700 overflow-hidden cursor-pointer"
              onClick={() => navigate(`/course/${course.id}`)}
            >
              {/* Course Thumbnail */}
              <div className="relative overflow-hidden">
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                
                {/* Course Icon */}
                <div className="absolute top-4 left-4">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                    {getCourseIcon(course.id)}
                  </div>
                </div>

                {/* Featured Badge */}
                <Badge className="absolute top-4 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                  Featured
                </Badge>

                {/* Skill Level */}
                <div className="absolute bottom-4 left-4">
                  <Badge className={skillLevelColors[course.level?.toLowerCase() as keyof typeof skillLevelColors] || skillLevelColors.beginner}>
                    {course.level ? course.level.charAt(0).toUpperCase() + course.level.slice(1) : 'Beginner'}
                  </Badge>
                </div>

                {/* Play Button */}
                <div className="absolute bottom-4 right-4">
                  <Button size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                    <PlayCircle className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                </div>
              </div>

              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-2">
                  <CardTitle className="text-xl font-bold text-white group-hover:text-blue-400 transition-colors line-clamp-2">
                    {course.title}
                  </CardTitle>
                  {course.rating && course.rating > 0 && (
                    <div className="flex items-center gap-1 text-sm text-gray-400">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{course.rating.toFixed(1)} ({course.reviewCount || 0})</span>
                    </div>
                  )}
                </div>
                {/* Use short description if available, fallback to description */}
                <p className="text-gray-300 line-clamp-3 leading-relaxed">
                  {course.shortDescription || course.description}
                </p>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Course Stats */}
                <div className="flex items-center gap-6 text-sm text-gray-400 mb-4">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{course.duration || 'Self-paced'}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span>
                      {course.curriculum && course.curriculum.length > 0
                        ? `${course.curriculum.length} modules`
                        : course.modules && course.modules.length > 0
                        ? `${course.modules.length} modules`
                        : '0 modules'
                      }
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{course.enrollmentCount || course.enrolledCount || 0} students</span>
                  </div>
                </div>

                {/* Course Tags */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {(course.tags || []).slice(0, 4).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Price and CTA */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-3">
                      <div className="text-3xl font-bold text-blue-400">
                        ${course.price} USD
                      </div>
                      {course.originalPrice && course.originalPrice > course.price && (
                        <div className="text-lg text-gray-500 line-through">
                          ${course.originalPrice} USD
                        </div>
                      )}
                    </div>
                    {course.price > 0 && (
                      <div className="text-xs text-gray-400">
                        One-time payment
                      </div>
                    )}
                  </div>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEnrollClick(course.id);
                    }}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    Enroll Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button asChild size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
            <Link to="/courses">
              View All Courses
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCoursesSection;
