/**
 * FreeCodeLap Platform - Firestore Service Functions
 * 
 * This file contains all the Firestore database operations for the platform.
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
  DocumentSnapshot,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db } from './firebase';
import { 
  User, 
  Course, 
  Module, 
  Lesson, 
  Enrollment, 
  Progress, 
  Announcement, 
  Certificate, 
  Payment, 
  Review 
} from '../types/schema';

// ============================================================================
// USER OPERATIONS
// ============================================================================

export const createUser = async (userData: Omit<User, 'createdAt'>): Promise<void> => {
  const userRef = doc(db, 'users', userData.uid);
  await updateDoc(userRef, {
    ...userData,
    createdAt: Timestamp.now()
  });
};

export const getUser = async (userId: string): Promise<User | null> => {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);
  return userSnap.exists() ? { id: userSnap.id, ...userSnap.data() } as User : null;
};

export const updateUser = async (userId: string, updates: Partial<User>): Promise<void> => {
  const userRef = doc(db, 'users', userId);
  await updateDoc(userRef, updates);
};

export const getAllUsers = async (): Promise<User[]> => {
  const usersRef = collection(db, 'users');
  const snapshot = await getDocs(usersRef);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User));
};

// ============================================================================
// COURSE OPERATIONS
// ============================================================================

export const createCourse = async (courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt' | 'enrollments'>): Promise<string> => {
  const coursesRef = collection(db, 'courses');
  const docRef = await addDoc(coursesRef, {
    ...courseData,
    enrollments: 0,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });
  return docRef.id;
};

export const getCourse = async (courseId: string): Promise<Course | null> => {
  const courseRef = doc(db, 'courses', courseId);
  const courseSnap = await getDoc(courseRef);
  return courseSnap.exists() ? { id: courseSnap.id, ...courseSnap.data() } as Course : null;
};

export const updateCourse = async (courseId: string, updates: Partial<Course>): Promise<void> => {
  const courseRef = doc(db, 'courses', courseId);
  await updateDoc(courseRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const deleteCourse = async (courseId: string): Promise<void> => {
  const courseRef = doc(db, 'courses', courseId);
  await deleteDoc(courseRef);
};

export const getAllCourses = async (): Promise<Course[]> => {
  const coursesRef = collection(db, 'courses');
  const q = query(coursesRef, orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Course));
};

export const getPublishedCourses = async (): Promise<Course[]> => {
  const coursesRef = collection(db, 'courses');
  // Get all courses first, then filter for published ones
  const snapshot = await getDocs(query(coursesRef, orderBy('createdAt', 'desc')));

  const allCourses = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Course));

  // Filter for published courses - check both isPublished and published fields
  const publishedCourses = allCourses.filter(course =>
    course.isPublished === true || (course as any).published === true
  );

  // Return published courses, or all if none are marked as published
  return publishedCourses.length > 0 ? publishedCourses : allCourses;
};

export const getFeaturedCourses = async (): Promise<Course[]> => {
  const coursesRef = collection(db, 'courses');
  const q = query(
    coursesRef, 
    where('published', '==', true), 
    where('featured', '==', true),
    orderBy('createdAt', 'desc')
  );
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Course));
};

// ============================================================================
// MODULE OPERATIONS
// ============================================================================

export const createModule = async (courseId: string, moduleData: Omit<Module, 'id' | 'courseId' | 'createdAt' | 'updatedAt' | 'lessons'>): Promise<string> => {
  const modulesRef = collection(db, 'courses', courseId, 'modules');
  const docRef = await addDoc(modulesRef, {
    ...moduleData,
    courseId,
    lessons: 0,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });
  return docRef.id;
};

export const getModule = async (courseId: string, moduleId: string): Promise<Module | null> => {
  const moduleRef = doc(db, 'courses', courseId, 'modules', moduleId);
  const moduleSnap = await getDoc(moduleRef);
  return moduleSnap.exists() ? { id: moduleSnap.id, ...moduleSnap.data() } as Module : null;
};

export const getCourseModules = async (courseId: string): Promise<Module[]> => {
  const modulesRef = collection(db, 'courses', courseId, 'modules');
  const q = query(modulesRef, orderBy('order', 'asc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Module));
};

export const updateModule = async (courseId: string, moduleId: string, updates: Partial<Module>): Promise<void> => {
  const moduleRef = doc(db, 'courses', courseId, 'modules', moduleId);
  await updateDoc(moduleRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const deleteModule = async (courseId: string, moduleId: string): Promise<void> => {
  const moduleRef = doc(db, 'courses', courseId, 'modules', moduleId);
  await deleteDoc(moduleRef);
};

// ============================================================================
// LESSON OPERATIONS
// ============================================================================

export const createLesson = async (
  courseId: string, 
  moduleId: string, 
  lessonData: Omit<Lesson, 'id' | 'courseId' | 'moduleId' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  const lessonsRef = collection(db, 'courses', courseId, 'modules', moduleId, 'lessons');
  const docRef = await addDoc(lessonsRef, {
    ...lessonData,
    courseId,
    moduleId,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });
  
  // Update module lesson count
  const moduleRef = doc(db, 'courses', courseId, 'modules', moduleId);
  await updateDoc(moduleRef, {
    lessons: increment(1)
  });
  
  return docRef.id;
};

export const getLesson = async (courseId: string, moduleId: string, lessonId: string): Promise<Lesson | null> => {
  const lessonRef = doc(db, 'courses', courseId, 'modules', moduleId, 'lessons', lessonId);
  const lessonSnap = await getDoc(lessonRef);
  return lessonSnap.exists() ? { id: lessonSnap.id, ...lessonSnap.data() } as Lesson : null;
};

export const getModuleLessons = async (courseId: string, moduleId: string): Promise<Lesson[]> => {
  const lessonsRef = collection(db, 'courses', courseId, 'modules', moduleId, 'lessons');
  const q = query(lessonsRef, orderBy('order', 'asc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Lesson));
};

export const updateLesson = async (
  courseId: string, 
  moduleId: string, 
  lessonId: string, 
  updates: Partial<Lesson>
): Promise<void> => {
  const lessonRef = doc(db, 'courses', courseId, 'modules', moduleId, 'lessons', lessonId);
  await updateDoc(lessonRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const deleteLesson = async (courseId: string, moduleId: string, lessonId: string): Promise<void> => {
  const lessonRef = doc(db, 'courses', courseId, 'modules', moduleId, 'lessons', lessonId);
  await deleteDoc(lessonRef);
  
  // Update module lesson count
  const moduleRef = doc(db, 'courses', courseId, 'modules', moduleId);
  await updateDoc(moduleRef, {
    lessons: increment(-1)
  });
};

// ============================================================================
// ENROLLMENT OPERATIONS
// ============================================================================

export const createEnrollment = async (enrollmentData: Omit<Enrollment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  const enrollmentsRef = collection(db, 'enrollments');

  const docRef = await addDoc(enrollmentsRef, {
    ...enrollmentData,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });

  // Update course enrollment count
  const courseRef = doc(db, 'courses', enrollmentData.courseId);
  await updateDoc(courseRef, {
    enrollments: increment(1)
  });

  return docRef.id;
};

export const getEnrollment = async (enrollmentId: string): Promise<Enrollment | null> => {
  const enrollmentRef = doc(db, 'enrollments', enrollmentId);
  const enrollmentSnap = await getDoc(enrollmentRef);
  return enrollmentSnap.exists() ? { id: enrollmentSnap.id, ...enrollmentSnap.data() } as Enrollment : null;
};

export const getUserEnrollment = async (userId: string, courseId: string): Promise<Enrollment | null> => {
  const enrollmentsRef = collection(db, 'enrollments');
  const q = query(
    enrollmentsRef,
    where('userId', '==', userId),
    where('courseId', '==', courseId),
    where('status', '==', 'active'),
    limit(1)
  );
  const snapshot = await getDocs(q);
  return snapshot.empty ? null : { id: snapshot.docs[0].id, ...snapshot.docs[0].data() } as Enrollment;
};

export const getUserEnrollments = async (userId: string): Promise<Enrollment[]> => {
  const enrollmentsRef = collection(db, 'enrollments');
  const q = query(enrollmentsRef, where('userId', '==', userId), orderBy('enrolledAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Enrollment));
};

export const getCourseEnrollments = async (courseId: string): Promise<Enrollment[]> => {
  const enrollmentsRef = collection(db, 'enrollments');
  const q = query(enrollmentsRef, where('courseId', '==', courseId), orderBy('enrolledAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Enrollment));
};

export const updateEnrollment = async (enrollmentId: string, updates: Partial<Enrollment>): Promise<void> => {
  const enrollmentRef = doc(db, 'enrollments', enrollmentId);
  await updateDoc(enrollmentRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

// ============================================================================
// PROGRESS OPERATIONS
// ============================================================================

export const updateProgress = async (progressData: Omit<Progress, 'id'>): Promise<void> => {
  const progressId = `${progressData.userId}_${progressData.lessonId}`;
  const progressRef = doc(db, 'progress', progressId);
  
  await updateDoc(progressRef, {
    ...progressData,
    id: progressId
  });
};

export const getUserProgress = async (userId: string, courseId: string): Promise<Progress[]> => {
  const progressRef = collection(db, 'progress');
  const q = query(
    progressRef, 
    where('userId', '==', userId), 
    where('courseId', '==', courseId)
  );
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Progress));
};

export const getLessonProgress = async (userId: string, lessonId: string): Promise<Progress | null> => {
  const progressId = `${userId}_${lessonId}`;
  const progressRef = doc(db, 'progress', progressId);
  const progressSnap = await getDoc(progressRef);
  return progressSnap.exists() ? { id: progressSnap.id, ...progressSnap.data() } as Progress : null;
};

// ============================================================================
// PAYMENT OPERATIONS
// ============================================================================

export const createPayment = async (paymentData: Omit<Payment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  const paymentsRef = collection(db, 'payments');
  const docRef = await addDoc(paymentsRef, {
    ...paymentData,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });
  return docRef.id;
};

export const getPayment = async (paymentId: string): Promise<Payment | null> => {
  const paymentRef = doc(db, 'payments', paymentId);
  const paymentSnap = await getDoc(paymentRef);
  return paymentSnap.exists() ? { id: paymentSnap.id, ...paymentSnap.data() } as Payment : null;
};

export const updatePayment = async (paymentId: string, updates: Partial<Payment>): Promise<void> => {
  const paymentRef = doc(db, 'payments', paymentId);
  await updateDoc(paymentRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const getUserPayments = async (userId: string): Promise<Payment[]> => {
  const paymentsRef = collection(db, 'payments');
  const q = query(paymentsRef, where('userId', '==', userId), orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Payment));
};

// ============================================================================
// CERTIFICATE OPERATIONS
// ============================================================================

export const createCertificate = async (certificateData: Omit<Certificate, 'id' | 'issuedAt'>): Promise<void> => {
  const certificateId = `${certificateData.userId}_${certificateData.courseId}`;
  const certificateRef = doc(db, 'certificates', certificateId);
  await updateDoc(certificateRef, {
    ...certificateData,
    id: certificateId,
    issuedAt: Timestamp.now()
  });

  // Update enrollment to mark certificate as issued
  const enrollmentId = `${certificateData.userId}_${certificateData.courseId}`;
  const enrollmentRef = doc(db, 'enrollments', enrollmentId);
  await updateDoc(enrollmentRef, {
    certificateIssued: true,
    certificateUrl: certificateData.certificateUrl,
    certificateIssuedAt: Timestamp.now()
  });
};

export const getUserCertificates = async (userId: string): Promise<Certificate[]> => {
  const certificatesRef = collection(db, 'certificates');
  const q = query(certificatesRef, where('userId', '==', userId), orderBy('issuedAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Certificate));
};

export const verifyCertificate = async (verificationCode: string): Promise<Certificate | null> => {
  const certificatesRef = collection(db, 'certificates');
  const q = query(certificatesRef, where('verificationCode', '==', verificationCode));
  const snapshot = await getDocs(q);
  return snapshot.empty ? null : { id: snapshot.docs[0].id, ...snapshot.docs[0].data() } as Certificate;
};

// ============================================================================
// ANNOUNCEMENT OPERATIONS
// ============================================================================

export const createAnnouncement = async (announcementData: Omit<Announcement, 'id' | 'createdAt' | 'sentToUsers'>): Promise<string> => {
  const announcementsRef = collection(db, 'announcements');
  const docRef = await addDoc(announcementsRef, {
    ...announcementData,
    createdAt: Timestamp.now(),
    sentToUsers: 0
  });
  return docRef.id;
};

export const getAnnouncements = async (courseId?: string): Promise<Announcement[]> => {
  const announcementsRef = collection(db, 'announcements');
  let q;

  if (courseId) {
    // Get announcements for specific course
    q = query(
      announcementsRef,
      where('courseId', '==', courseId),
      orderBy('createdAt', 'desc')
    );
  } else {
    // Get global announcements
    q = query(
      announcementsRef,
      where('courseId', '==', null),
      orderBy('createdAt', 'desc')
    );
  }

  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Announcement));
};

// ============================================================================
// REVIEW OPERATIONS
// ============================================================================

export const createReview = async (reviewData: Omit<Review, 'id' | 'createdAt' | 'updatedAt' | 'helpful'>): Promise<void> => {
  const reviewId = `${reviewData.userId}_${reviewData.courseId}`;
  const reviewRef = doc(db, 'reviews', reviewId);
  await updateDoc(reviewRef, {
    ...reviewData,
    id: reviewId,
    helpful: 0,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  });

  // Update course rating
  await updateCourseRating(reviewData.courseId);
};

export const getCourseReviews = async (courseId: string): Promise<Review[]> => {
  const reviewsRef = collection(db, 'reviews');
  const q = query(reviewsRef, where('courseId', '==', courseId), orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review));
};

export const updateReview = async (userId: string, courseId: string, updates: Partial<Review>): Promise<void> => {
  const reviewId = `${userId}_${courseId}`;
  const reviewRef = doc(db, 'reviews', reviewId);
  await updateDoc(reviewRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });

  // Update course rating if the rating changed
  if (updates.rating) {
    await updateCourseRating(courseId);
  }
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

export const isUserEnrolled = async (userId: string, courseId: string): Promise<boolean> => {
  const enrollment = await getEnrollment(userId, courseId);
  return enrollment !== null && enrollment.status === 'active';
};

export const getUserCourseProgress = async (userId: string, courseId: string): Promise<{ completed: number; total: number; percentage: number }> => {
  const progressList = await getUserProgress(userId, courseId);
  const completed = progressList.filter(p => p.completed).length;
  const total = progressList.length;
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

  return { completed, total, percentage };
};

export const updateCourseRating = async (courseId: string): Promise<void> => {
  const reviews = await getCourseReviews(courseId);

  if (reviews.length === 0) {
    return;
  }

  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / reviews.length;

  const courseRef = doc(db, 'courses', courseId);
  await updateDoc(courseRef, {
    rating: averageRating,
    reviews: reviews.length
  });
};
