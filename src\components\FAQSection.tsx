import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const FAQSection = () => {
  const faqs = [
    {
      question: "Do I need any programming experience to start?",
      answer: "Not at all! FreeCodeLap courses are designed for complete beginners. Our FlutterFlow No-Code course requires zero programming experience, and our Vibe Coding course teaches you to use AI tools that make coding accessible to everyone. If you can use a computer, you can learn with us."
    },
    {
      question: "What's the difference between the two main courses?",
      answer: "FlutterFlow No-Code Development teaches you to build mobile and web apps without writing code using FlutterFlow's visual interface. Vibe Coding focuses on AI-powered development using tools like VS Code, Cursor, and GitHub Copilot to boost productivity and code faster with AI assistance."
    },
    {
      question: "How long does it take to complete a course?",
      answer: "FlutterFlow No-Code takes 8 weeks (45 lessons), and Vibe Coding takes 6 weeks (38 lessons). Most students study 1-2 hours daily, but you have lifetime access so you can learn at your own pace. Some finish faster, others take more time to practice."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We use Paystack for secure payments, accepting M-Pesa, Visa/Mastercard, bank transfers, and mobile money. All payments are processed securely in Kenyan Shillings (KES). You get instant access to your course after successful payment."
    },
    {
      question: "Do you provide certificates upon completion?",
      answer: "Yes! Students who complete all lessons and projects receive a verified certificate of completion. Certificates are manually issued by our team and can be added to your LinkedIn profile to showcase your new skills to employers."
    },
    {
      question: "What's included with lifetime access?",
      answer: "Lifetime access includes all course videos, project files, resources, future updates, and new content additions. You can re-watch lessons anytime, download materials, and access new modules we add to keep courses current with latest features."
    },
    {
      question: "Can I build real apps that make money after the course?",
      answer: "Absolutely! Our courses focus on practical, real-world applications. FlutterFlow students build complete apps ready for app stores, while Vibe Coding students learn to develop faster and more efficiently. Many graduates start freelancing or building their own apps within months."
    },
    {
      question: "What devices/software do I need?",
      answer: "You need a computer (Windows, Mac, or Linux) with internet connection. FlutterFlow works in your web browser - no installation required. For Vibe Coding, we'll guide you through setting up VS Code and AI tools. A smartphone is helpful for testing apps."
    },
    {
      question: "Is there a refund policy?",
      answer: "Yes, we offer a 14-day money-back guarantee. If you're not satisfied within 14 days of purchase, contact us for a full refund. We want every student to be completely happy with their learning experience at FreeCodeLap."
    },
    {
      question: "Do you offer live classes or just recorded courses?",
      answer: "We offer both! Recorded courses give you instant access and lifetime availability. Live classes provide real-time interaction and personalized feedback. Contact us on WhatsApp to learn about current live class schedules and availability."
    },
    {
      question: "How do I get support if I'm stuck?",
      answer: "Course students get access to our community support, email assistance, and detailed course materials. Live class students get real-time help during sessions. We're committed to helping every student succeed in their learning journey."
    },
    {
      question: "Can I get help with my specific project during the course?",
      answer: "Definitely! Live class students get direct project feedback during sessions. Recorded course students can share their projects in our community or schedule optional 1-on-1 sessions. I encourage building real projects and provide guidance to help you create something meaningful and profitable."
    }
  ];

  const handleContactWhatsApp = () => {
    window.open('https://wa.me/************?text=Hi Ahmed, I have a question about your FlutterFlow courses', '_blank');
  };

  const handleContactEmail = () => {
    window.open('mailto:<EMAIL>', '_blank');
  };

  return (
    <section id="faq" className="py-20 section-dark">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl font-bold text-white mb-4 gradient-text">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-blue-200 max-w-2xl mx-auto">
            Got questions? Here are answers to the most common questions about our FlutterFlow courses
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="card-elevated rounded-lg border-0 px-6 animate-bounce-in animate-delay-200"
              >
                <AccordionTrigger className="text-left font-semibold text-white hover:no-underline py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-blue-200 leading-relaxed pb-6">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="mt-12 bg-gradient-to-r from-blue-600 to-pink-600 rounded-2xl p-8 text-white text-center animate-slide-in-bottom">
            <h3 className="text-2xl font-bold mb-4">Still Have Questions?</h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Don't see your question answered here? I'm happy to help! Contact me directly and I'll get back to you within 24 hours.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                type="button"
                className="btn-animated btn-blue transform hover:scale-105"
                onClick={handleContactWhatsApp}
              >
                WhatsApp: +254 712 345 678
              </button>
              <button
                type="button"
                className="btn-animated btn-ghost transform hover:scale-105"
                onClick={handleContactEmail}
              >
                Email: <EMAIL>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
