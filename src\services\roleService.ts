import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserRole } from '@/types/user';

export class RoleService {
  /**
   * Update user role in Firestore
   */
  static async updateUserRole(userId: string, newRole: UserRole): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        role: newRole,
        updatedAt: new Date()
      });
      console.log(`✅ User role updated: ${userId} -> ${newRole}`);
    } catch (error) {
      console.error('❌ Error updating user role:', error);
      throw error;
    }
  }

  /**
   * Get user role from Firestore
   */
  static async getUserRole(userId: string): Promise<UserRole | null> {
    try {
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);
      
      if (userSnap.exists()) {
        return userSnap.data().role as UserRole;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting user role:', error);
      throw error;
    }
  }

  /**
   * Check if user has admin privileges
   */
  static isAdminEmail(email: string): boolean {
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    return adminEmails.includes(email.toLowerCase()) || 
           email.toLowerCase().includes('admin');
  }

  /**
   * Get default role for new user based on email
   */
  static getDefaultRole(email: string): UserRole {
    return this.isAdminEmail(email) ? 'admin' : 'student';
  }

  /**
   * Validate role permissions for specific actions
   */
  static canAccessAdminPanel(role: UserRole): boolean {
    return role === 'admin';
  }

  static canManageCourses(role: UserRole): boolean {
    return role === 'admin';
  }

  static canViewAnalytics(role: UserRole): boolean {
    return role === 'admin';
  }

  static canManageUsers(role: UserRole): boolean {
    return role === 'admin';
  }

  static canAccessCourseContent(role: UserRole): boolean {
    return role === 'admin' || role === 'student';
  }

  /**
   * Get role display name
   */
  static getRoleDisplayName(role: UserRole): string {
    switch (role) {
      case 'admin':
        return 'Administrator';
      case 'student':
        return 'Student';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get role permissions description
   */
  static getRolePermissions(role: UserRole): string[] {
    switch (role) {
      case 'admin':
        return [
          'Access admin panel',
          'Manage courses',
          'View analytics',
          'Manage users',
          'Access all content'
        ];
      case 'student':
        return [
          'Enroll in courses',
          'Access purchased content',
          'Track progress',
          'Download certificates'
        ];
      default:
        return [];
    }
  }
}

export default RoleService;
