
import React from 'react';
import { Navigation } from '../components/Navigation';
import HeroSection from '../components/HeroSection';
import FeaturedCoursesSection from '../components/FeaturedCoursesSection';

import WhyFreeCodeLapSection from '../components/WhyFreeCodeLapSection';
import HowItWorksSection from '../components/HowItWorksSection';
import LiveClassesContactSection from '../components/LiveClassesContactSection';
import AboutInstructorSection from '../components/AboutInstructorSection';
import TestimonialsSection from '../components/TestimonialsSection';
import FAQSection from '../components/FAQSection';
import FinalCTASection from '../components/FinalCTASection';
import EmailCaptureSection from '../components/EmailCaptureSection';
import Footer from '../components/Footer';
import { ErrorBoundary } from '../components/ErrorBoundary';



const Index = () => {
  return (
    <div className="min-h-screen section-dark">
      <Navigation />

      {/* Main Content */}
      <main className="pt-16">
        {/* Hero Section */}
        <HeroSection />





        {/* Featured Courses Section */}
        <ErrorBoundary>
          <FeaturedCoursesSection />
        </ErrorBoundary>

        {/* About Instructor Section */}
        <AboutInstructorSection />

        {/* Why FreeCodeLap Section */}
        <WhyFreeCodeLapSection />

        {/* How It Works Section */}
        <HowItWorksSection />

        {/* Live Classes Contact Section */}
        <LiveClassesContactSection />

        {/* Testimonials Section */}
        <TestimonialsSection />

        {/* FAQ Section */}
        <FAQSection />

        {/* Final CTA Section */}
        <FinalCTASection />

        {/* Email Capture Section */}
        <EmailCaptureSection />

        {/* Footer */}
        <Footer />
      </main>
    </div>
  );
};

export default Index;
