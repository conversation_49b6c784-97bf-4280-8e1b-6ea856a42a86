import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, Smartphone, Building2, Check } from 'lucide-react';

interface PaymentMethod {
  id: 'card' | 'mpesa' | 'bank_transfer';
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  popular?: boolean;
}

interface PaymentMethodSelectorProps {
  selectedMethod: string | null;
  onMethodSelect: (method: string) => void;
  amount: number;
  currency: string;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodSelect,
  amount,
  currency,
}) => {
  const getPaymentMethods = (): PaymentMethod[] => {
    const methods: PaymentMethod[] = [
      {
        id: 'card',
        name: 'Credit/Debit Card',
        description: 'Pay with Visa, Mastercard, or other cards',
        icon: <CreditCard className="w-6 h-6" />,
        enabled: true,
      },
    ];

    // Add M-Pesa for KES
    if (currency === 'KES') {
      methods.push({
        id: 'mpesa',
        name: '<PERSON>-Pes<PERSON>',
        description: 'Pay with your M-Pesa mobile money',
        icon: <Smartphone className="w-6 h-6" />,
        enabled: true,
        popular: true,
      });
    }

    // Add bank transfer for supported currencies
    if (['NGN', 'KES', 'GHS', 'ZAR'].includes(currency)) {
      methods.push({
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Direct bank transfer payment',
        icon: <Building2 className="w-6 h-6" />,
        enabled: true,
      });
    }

    return methods;
  };

  const paymentMethods = getPaymentMethods();

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <Card className="card-elevated">
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Payment Summary</h3>
            <div className="text-3xl font-bold text-blue-400 mb-2">
              {formatCurrency(amount, currency)}
            </div>
            <p className="text-blue-200 text-sm">One-time payment for course access</p>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white mb-4">Choose Payment Method</h3>
        
        {paymentMethods.map((method) => (
          <Card
            key={method.id}
            className={`card-elevated cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              selectedMethod === method.id
                ? 'ring-2 ring-blue-500 bg-blue-dark-800/70'
                : 'hover:shadow-xl'
            } ${!method.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={() => method.enabled && onMethodSelect(method.id)}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-full ${
                    selectedMethod === method.id
                      ? 'bg-blue-500 text-white'
                      : 'bg-blue-dark-700 text-blue-400'
                  }`}>
                    {method.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-white">{method.name}</h4>
                      {method.popular && (
                        <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full">
                          Popular
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-blue-200 mt-1">{method.description}</p>
                  </div>
                </div>

                {selectedMethod === method.id && (
                  <div className="bg-blue-500 text-white rounded-full p-1">
                    <Check className="w-4 h-4" />
                  </div>
                )}
              </div>

              {/* Method-specific info */}
              {method.id === 'mpesa' && (
                <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <p className="text-sm text-green-400">
                    💡 Secure M-Pesa payment via Paystack
                  </p>
                </div>
              )}

              {method.id === 'card' && (
                <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <p className="text-sm text-blue-400">
                    🔒 Secure payment processing via Paystack
                  </p>
                </div>
              )}

              {method.id === 'bank_transfer' && (
                <div className="mt-4 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                  <p className="text-sm text-purple-400">
                    🏦 Direct bank transfer via Paystack
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Security Notice */}
      <Card className="card-elevated">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="bg-green-500/20 p-2 rounded-full">
              <Check className="w-4 h-4 text-green-400" />
            </div>
            <div>
              <p className="text-sm text-white font-medium">Secure Payment</p>
              <p className="text-xs text-blue-200">
                Your payment information is encrypted and secure
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentMethodSelector;
