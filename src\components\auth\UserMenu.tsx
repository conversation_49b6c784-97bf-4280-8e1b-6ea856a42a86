import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { AdminOnly, RoleBadge } from '@/components/auth/RoleBasedAccess';
import {
  User,
  BookOpen,
  Settings,
  Shield,
  GraduationCap,
  LogOut,
  ChevronDown
} from 'lucide-react';

export function UserMenu() {
  const { currentUser, userProfile, logout, isAdmin, isUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      navigate('/');
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to log out. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!currentUser || !userProfile) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'user':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-3 h-3" />;
      case 'user':
        return <User className="w-3 h-3" />;
      default:
        return <User className="w-3 h-3" />;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-auto px-2">
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={userProfile.profilePictureUrl || currentUser.photoURL || ''}
                alt={userProfile.displayName}
                className="object-cover"
              />
              <AvatarFallback className="text-xs bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                {getInitials(userProfile.displayName)}
              </AvatarFallback>
            </Avatar>
            <div className="hidden md:flex flex-col items-start">
              <span className="text-sm font-medium truncate max-w-24">
                {userProfile.displayName}
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs h-4 px-1 ${getRoleBadgeColor(userProfile.role)}`}
              >
                <span className="flex items-center gap-1">
                  {getRoleIcon(userProfile.role)}
                  {userProfile.role}
                </span>
              </Badge>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={userProfile.profilePictureUrl || currentUser.photoURL || ''}
                  alt={userProfile.displayName}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                  {getInitials(userProfile.displayName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <p className="text-sm font-medium leading-none">
                  {userProfile.displayName}
                </p>
                <p className="text-xs leading-none text-muted-foreground mt-1">
                  {currentUser.email}
                </p>
              </div>
            </div>
            <Badge 
              variant="outline" 
              className={`w-fit text-xs ${getRoleBadgeColor(userProfile.role)}`}
            >
              <span className="flex items-center gap-1">
                {getRoleIcon(userProfile.role)}
                {userProfile.role.charAt(0).toUpperCase() + userProfile.role.slice(1)}
              </span>
            </Badge>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />

        {/* Student Menu Items */}
        {userProfile.role === 'student' && (
          <>
            <DropdownMenuItem asChild>
              <Link to="/dashboard" className="flex items-center">
                <BookOpen className="mr-2 h-4 w-4" />
                <span>My Dashboard</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/my-courses" className="flex items-center">
                <GraduationCap className="mr-2 h-4 w-4" />
                <span>My Courses</span>
              </Link>
            </DropdownMenuItem>
          </>
        )}

        {/* Admin Menu Items */}
        <AdminOnly>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to="/admin" className="flex items-center">
              <Shield className="mr-2 h-4 w-4" />
              <span>Admin Panel</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => navigate('/admin')}>
            <User className="mr-2 h-4 w-4" />
            <span>Manage Users</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => navigate('/admin')}>
            <BookOpen className="mr-2 h-4 w-4" />
            <span>Manage Courses</span>
          </DropdownMenuItem>
        </AdminOnly>

        <DropdownMenuSeparator />
        
        {/* Common Menu Items */}
        <DropdownMenuItem asChild>
          <Link to="/profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </Link>
        </DropdownMenuItem>
        

        
        <DropdownMenuItem asChild>
          <Link to="/settings" className="flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleLogout}
          className="text-red-600 focus:text-red-600 focus:bg-red-50"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
