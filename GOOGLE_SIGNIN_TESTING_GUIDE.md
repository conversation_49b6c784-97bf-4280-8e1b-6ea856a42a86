# 🧪 Google Sign-In Testing Guide

## Pre-Testing Setup

### 1. **Firebase Console Configuration** (CRITICAL)
Before testing, ensure these are configured in Firebase Console:

#### Firebase Console Steps:
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: **freecodelap**
3. Navigate to **Authentication** → **Settings** → **Authorized domains**
4. Ensure these domains are added:
   ```
   localhost
   localhost:8080
   freecodelap.firebaseapp.com
   ```

#### Google Cloud Console Steps:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: **freecodelap**
3. Go to **APIs & Services** → **Credentials**
4. Find your OAuth 2.0 Client ID and edit it
5. Ensure **Authorized JavaScript origins** includes:
   ```
   http://localhost:8080
   https://freecodelap.firebaseapp.com
   ```
6. Ensure **Authorized redirect URIs** includes:
   ```
   http://localhost:8080/__/auth/handler
   https://freecodelap.firebaseapp.com/__/auth/handler
   ```

### 2. **Start Development Server**
```bash
npm run dev
```
This should start the server on `http://localhost:8080`

### 3. **Open Browser Console**
- Press F12 or right-click → Inspect
- Go to Console tab
- Keep it open during testing to see debug logs

## Testing Steps

### Step 1: **Basic Configuration Check**
1. Open `http://localhost:8080` in your browser
2. Check browser console for these logs:
   ```
   🔐 Google Auth Provider configured with scopes: ['email', 'profile', 'openid']
   🔐 Firebase Auth Domain: freecodelap.firebaseapp.com
   🌐 Current URL: http://localhost:8080/
   🔧 Running on port: 8080
   🔍 Current origin for Google Auth: http://localhost:8080
   ✅ Running on expected port 8080
   ```

### Step 2: **Test Google Sign-In**
1. Click on "Sign In" or "Login" button
2. Click "Continue with Google" button
3. Watch browser console for detailed logs

#### Expected Success Flow:
```
🔐 Starting Google popup login...
🌐 Current origin: http://localhost:8080
🔧 Current port: 8080
🌐 Current domain: localhost
🔐 Google Provider initialized successfully
✅ Google popup login successful: <EMAIL>
✅ User profile created/updated successfully
```

#### Common Error Scenarios:

**Scenario A: Domain Authorization Issue**
```
❌ Google popup login error: Error: GOOGLE_SIGNIN_TIMEOUT
❌ Google Sign-in timeout detected - likely domain authorization issue
```
**Solution**: Add `localhost:8080` to Firebase Console authorized domains

**Scenario B: Popup Blocked**
```
❌ Popup blocked by browser
```
**Solution**: Allow popups for localhost:8080 in browser settings

**Scenario C: Unauthorized Domain**
```
❌ Unauthorized domain: http://localhost:8080
```
**Solution**: Add `http://localhost:8080` to Google Cloud Console JavaScript origins

### Step 3: **Test Different Browsers**
Test in multiple browsers to rule out browser-specific issues:
- Chrome (recommended)
- Firefox
- Edge
- Safari (if on Mac)

### Step 4: **Test Incognito Mode**
1. Open incognito/private window
2. Navigate to `http://localhost:8080`
3. Try Google Sign-In
4. This rules out cache/cookie issues

## Troubleshooting Common Issues

### Issue: "Sign-in Timeout"
**Cause**: Domain not authorized in Firebase Console
**Solution**: 
1. Go to Firebase Console → Authentication → Settings → Authorized domains
2. Add `localhost` and `localhost:8080`

### Issue: "Popup Blocked"
**Cause**: Browser blocking popups
**Solution**: 
1. Allow popups for localhost:8080
2. Or the system will automatically try redirect method

### Issue: "Configuration Error"
**Cause**: OAuth consent screen not properly configured
**Solution**: 
1. Go to Google Cloud Console → APIs & Services → OAuth consent screen
2. Ensure all required fields are filled

### Issue: "Network Error"
**Cause**: Firewall or network blocking Google APIs
**Solution**: 
1. Try different network
2. Check corporate firewall settings
3. Disable VPN temporarily

## Debug Information

### Browser Console Logs to Look For:
- ✅ **Success indicators**: Green checkmarks and "successful" messages
- ❌ **Error indicators**: Red X marks and error codes
- ⚠️ **Warnings**: Yellow warnings about port or configuration

### Network Tab Analysis:
1. Open Network tab in browser dev tools
2. Try Google Sign-In
3. Look for failed requests to:
   - `accounts.google.com`
   - `googleapis.com`
   - Firebase domains

### Firebase Console Logs:
1. Go to Firebase Console → Authentication → Users
2. Check if users are being created after successful sign-in
3. Go to Firebase Console → Analytics → Events (if analytics enabled)

## Success Criteria

✅ **Google Sign-In is working if:**
- Popup opens with Google sign-in page
- User can select/enter Google account
- Popup closes automatically after authentication
- User is logged into the application
- User profile is created in Firestore
- No error messages in console

## If Still Not Working

1. **Double-check Firebase Console configuration**
2. **Verify Google Cloud Console OAuth settings**
3. **Try email/password login as alternative**
4. **Check browser console for specific error codes**
5. **Test on different network/device**
6. **Contact Firebase Support** with specific error codes

## Quick Verification Commands

Run these in browser console to check configuration:
```javascript
// Check current origin
console.log('Current origin:', window.location.origin);

// Check Firebase config
console.log('Firebase config loaded:', !!window.firebase);

// Check if running on expected port
console.log('Running on port 8080:', window.location.port === '8080');
```

Remember: The most common issue is missing domain authorization in Firebase Console!
