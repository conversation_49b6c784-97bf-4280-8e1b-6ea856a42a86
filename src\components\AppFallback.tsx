import React from 'react';
import { <PERSON><PERSON><PERSON>ircle, RefreshCw, Home, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AppFallbackProps {
  error?: Error;
  resetError?: () => void;
}

export const AppFallback: React.FC<AppFallbackProps> = ({ error, resetError }) => {
  const handleRefresh = () => {
    if (resetError) {
      resetError();
    }
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-blue-900 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full bg-white/10 backdrop-blur-lg border-white/20">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-400" />
          </div>
          <CardTitle className="text-white text-2xl">
            Oops! Something went wrong
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-300 mb-4">
              We encountered an unexpected error while loading FreeCodeLap. 
              This might be a temporary issue.
            </p>
            
            <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
              <h4 className="text-white font-semibold mb-2">What you can try:</h4>
              <ul className="text-left text-gray-300 space-y-2 text-sm">
                <li className="flex items-center space-x-2">
                  <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                  <span>Refresh the page</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                  <span>Check your internet connection</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                  <span>Clear your browser cache</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                  <span>Try again in a few minutes</span>
                </li>
              </ul>
            </div>
          </div>

          {error && (
            <details className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <summary className="text-red-300 font-semibold cursor-pointer mb-2">
                Technical Details
              </summary>
              <div className="text-red-200 text-sm font-mono bg-red-900/30 p-3 rounded overflow-auto">
                <div className="mb-2">
                  <strong>Error:</strong> {error.name}
                </div>
                <div className="mb-2">
                  <strong>Message:</strong> {error.message}
                </div>
                {error.stack && (
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 text-xs">{error.stack}</pre>
                  </div>
                )}
              </div>
            </details>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button 
              onClick={handleRefresh}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh Page
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleGoHome}
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Button>
          </div>

          <div className="text-center">
            <Button 
              variant="ghost"
              onClick={() => window.location.href = '/page-test'}
              className="text-gray-400 hover:text-white text-sm"
            >
              <Settings className="w-4 h-4 mr-2" />
              Run Diagnostics
            </Button>
          </div>

          <div className="text-center pt-4 border-t border-white/10">
            <p className="text-xs text-gray-400">
              If this problem continues, please contact our support team.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AppFallback;
