import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, CheckCircle, Info, Shield, CreditCard } from 'lucide-react';
import { paymentService } from '@/services/paymentService';

interface LiveModeStatusProps {
  showDetails?: boolean;
}

const LiveModeStatus: React.FC<LiveModeStatusProps> = ({ showDetails = false }) => {
  const [environmentInfo, setEnvironmentInfo] = useState<any>(null);
  const [validation, setValidation] = useState<any>(null);

  useEffect(() => {
    const envInfo = paymentService.getEnvironmentInfo();
    const validationResult = paymentService.validateLiveMode();
    
    setEnvironmentInfo(envInfo);
    setValidation(validationResult);
  }, []);

  if (!environmentInfo || !validation) return null;

  const isLiveMode = environmentInfo.isLiveMode;
  const isValid = validation.isValid;

  return (
    <div className="space-y-4">
      {/* Status Badge */}
      <div className="flex items-center space-x-3">
        <Badge 
          variant={isLiveMode ? (isValid ? "default" : "destructive") : "secondary"}
          className={`flex items-center space-x-1 ${
            isLiveMode 
              ? (isValid ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700')
              : 'bg-yellow-600 hover:bg-yellow-700'
          }`}
        >
          {isLiveMode ? (
            isValid ? <CheckCircle className="w-3 h-3" /> : <AlertTriangle className="w-3 h-3" />
          ) : (
            <Info className="w-3 h-3" />
          )}
          <span>
            {isLiveMode ? (isValid ? 'LIVE MODE' : 'LIVE MODE (INVALID)') : 'TEST MODE'}
          </span>
        </Badge>
        
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <Shield className="w-4 h-4" />
          <span>Paystack</span>
        </div>
      </div>

      {/* Validation Errors */}
      {!isValid && validation.errors.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="w-4 h-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="font-medium mb-2">Payment Configuration Issues:</div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {validation.errors.map((error: string, index: number) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Test Mode Warning */}
      {!isLiveMode && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Info className="w-4 h-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <div className="font-medium mb-1">Test Mode Active</div>
            <div className="text-sm">
              No real money will be charged. Use test cards for testing.
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Live Mode Success */}
      {isLiveMode && isValid && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="w-4 h-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="font-medium mb-1">Live Mode Active</div>
            <div className="text-sm">
              Real payments are being processed. Ensure all testing is complete.
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed Information */}
      {showDetails && (
        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 flex items-center space-x-2">
              <CreditCard className="w-4 h-4" />
              <span>Payment Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Environment:</span>
                <span className="font-medium">
                  {environmentInfo.isProduction ? 'Production' : 'Development'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Mode:</span>
                <span className="font-medium">
                  {isLiveMode ? 'Live' : 'Test'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Public Key:</span>
                <span className="font-mono text-xs">
                  {environmentInfo.publicKey}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${isValid ? 'text-green-600' : 'text-red-600'}`}>
                  {isValid ? 'Valid' : 'Invalid'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LiveModeStatus;
