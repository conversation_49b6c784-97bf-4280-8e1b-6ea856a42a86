# Google Authentication Troubleshooting Guide

## Current Issue: Google Sign-In Stuck on "Signing In"

This usually indicates one of the following problems:

### 1. **Domain Authorization Issue**
The current domain (`localhost:5173`) might not be authorized in Firebase Console.

**Solution:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `codefreelap`
3. Go to **Authentication** → **Settings** → **Authorized domains**
4. Add these domains:
   - `localhost` (for all localhost ports)
   - `localhost:5173` (Vite development server)
   - `localhost:8080` (if using different port)
   - Your production domain

### 2. **OAuth Consent Screen Configuration**
Google Sign-In requires proper OAuth consent screen setup.

**Solution:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `codefreelap`
3. Go to **APIs & Services** → **OAuth consent screen**
4. Configure:
   - **Application name**: FreeCodeLap
   - **User support email**: <EMAIL>
   - **Developer contact information**: <EMAIL>
   - **Authorized domains**: Add your domains
5. Go to **Credentials** → **OAuth 2.0 Client IDs**
6. Edit your web client and add:
   - **Authorized JavaScript origins**: 
     - `http://localhost:5173`
     - `http://localhost:8080`
     - Your production domain
   - **Authorized redirect URIs**:
     - `http://localhost:5173/__/auth/handler`
     - `http://localhost:8080/__/auth/handler`

### 3. **Browser Issues**
- Clear browser cache and cookies
- Disable ad blockers temporarily
- Try in incognito/private mode
- Check browser console for errors

### 4. **Firebase Configuration**
Verify all environment variables are correct:
```
VITE_FIREBASE_API_KEY=AIzaSyABB72plhO6QQEvbCGoiJnuowDbtjjJ2OM
VITE_FIREBASE_AUTH_DOMAIN=codefreelap.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=codefreelap
```

### 5. **Network/Firewall Issues**
- Check if corporate firewall is blocking Google APIs
- Try different network connection
- Verify internet connectivity

## Testing Steps

1. **Check AuthDebug Panel**: Look at the debug panel in bottom-right corner
2. **Browser Console**: Check for JavaScript errors
3. **Network Tab**: Look for failed requests to Google APIs
4. **Firebase Console**: Check Authentication logs for errors

## Common Error Messages

- **"unauthorized-domain"**: Domain not authorized in Firebase Console
- **"popup-blocked"**: Browser blocked the popup
- **"internal-error"**: Firebase configuration issue
- **"network-request-failed"**: Network connectivity issue

## Quick Fix Checklist

- [ ] Domain authorized in Firebase Console
- [ ] OAuth consent screen configured
- [ ] JavaScript origins and redirect URIs set
- [ ] Browser allows popups
- [ ] No ad blockers interfering
- [ ] Environment variables correct
- [ ] Internet connection stable