/**
 * FreeCodeLap Platform - User Management Component
 * 
 * Production-ready user management interface for admin panel
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  Users,
  Search,
  Filter,
  Plus,
  Download,
  Upload,
  MoreVertical,
  Edit,
  Trash2,
  Shield,
  Ban,
  CheckCircle,
  Mail,
  Eye,
  UserPlus,
  Settings,
  Activity,
  Calendar,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { UserProfile, UserRole } from '@/types/user';
import { userManagementService, UserSearchFilters, UserStatistics } from '@/services/userManagementService';
import { RoleBadge } from '@/components/auth/RoleBasedAccess';
import {
  CreateUserModal,
  EditUserModal,
  UserDetailsModal,
  BulkOperationsModal
} from './UserModals';
import { UserAnalytics } from './UserAnalytics';
import { EmailNotificationButton } from './EmailNotificationButton';
import { SentEmailsHistory } from './SentEmailsHistory';

interface UserManagementProps {
  className?: string;
}

export const UserManagement: React.FC<UserManagementProps> = ({ className }) => {
  const { toast } = useToast();
  
  // State management
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);

  // Tab state
  const [activeTab, setActiveTab] = useState<'users' | 'analytics' | 'emails'>('users');

  // Load users on component mount
  useEffect(() => {
    loadUsers();
    loadStatistics();
  }, []);

  // Load users with current filters
  const loadUsers = async (reset: boolean = true) => {
    try {
      setLoading(true);

      const filters: UserSearchFilters = {};

      if (selectedRole !== 'all') {
        filters.role = selectedRole as UserRole;
      }

      if (selectedStatus !== 'all') {
        filters.status = selectedStatus as any;
      }

      if (searchTerm.trim()) {
        filters.searchTerm = searchTerm.trim();
      }

      console.log('🔍 Loading users with filters:', filters);
      const result = await userManagementService.getUsers(filters, 50);
      console.log('✅ Users loaded successfully:', result.users.length);

      if (reset) {
        setUsers(result.users);
        setCurrentPage(1);
      } else {
        setUsers(prev => [...prev, ...result.users]);
        setCurrentPage(prev => prev + 1);
      }

      setHasMore(result.hasMore);
    } catch (error: any) {
      console.error('❌ Error loading users:', error);

      // More specific error messages
      let errorMessage = "Failed to load users. Please try again.";
      if (error?.code === 'permission-denied') {
        errorMessage = "Permission denied. Please ensure you have admin access.";
      } else if (error?.code === 'unavailable') {
        errorMessage = "Service temporarily unavailable. Please try again later.";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error Loading Users",
        description: errorMessage,
        variant: "destructive",
      });

      // Set empty state on error
      if (reset) {
        setUsers([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Load user statistics
  const loadStatistics = async () => {
    try {
      const stats = await userManagementService.getUserStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  // Handle search
  const handleSearch = () => {
    loadUsers(true);
  };

  // Handle filter changes
  const handleFilterChange = () => {
    loadUsers(true);
  };

  // Toggle user selection
  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  // Select all users
  const toggleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.uid));
    }
  };

  // Handle user actions
  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  const handleViewUser = (user: UserProfile) => {
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      await userManagementService.deleteUser(userId);
      toast({
        title: "Success",
        description: "User deleted successfully.",
      });
      loadUsers(true);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete user.",
        variant: "destructive",
      });
    }
  };

  const handleToggleSuspension = async (userId: string, suspend: boolean) => {
    try {
      await userManagementService.toggleUserSuspension(userId, suspend);
      toast({
        title: "Success",
        description: `User ${suspend ? 'suspended' : 'unsuspended'} successfully.`,
      });
      loadUsers(true);
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${suspend ? 'suspend' : 'unsuspend'} user.`,
        variant: "destructive",
      });
    }
  };

  const handleChangeRole = async (userId: string, newRole: UserRole) => {
    try {
      await userManagementService.changeUserRole(userId, newRole);
      toast({
        title: "Success",
        description: "User role updated successfully.",
      });
      loadUsers(true);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role.",
        variant: "destructive",
      });
    }
  };

  // Export users
  const handleExportUsers = async () => {
    try {
      const filters: UserSearchFilters = {};
      if (selectedRole !== 'all') filters.role = selectedRole as UserRole;
      if (selectedStatus !== 'all') filters.status = selectedStatus as any;
      if (searchTerm.trim()) filters.searchTerm = searchTerm.trim();

      const csvContent = await userManagementService.exportUsersToCSV(filters);
      
      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Users exported successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export users.",
        variant: "destructive",
      });
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const colors = {
      active: 'bg-green-600 text-white',
      inactive: 'bg-gray-600 text-white',
      suspended: 'bg-red-600 text-white',
      deleted: 'bg-black text-white'
    };
    return (
      <Badge className={`${colors[status as keyof typeof colors] || colors.active} text-xs`}>
        {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Active'}
      </Badge>
    );
  };

  // Format date helper
  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">User Management</h2>
          <p className="text-gray-400">Manage platform users, roles, and permissions</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
          <EmailNotificationButton
            selectedUserIds={selectedUsers}
            onEmailSent={() => {
              // Switch to emails tab to show the sent email
              setActiveTab('emails');
              console.log('Email sent successfully - switched to emails tab');
            }}
          />
          <Button
            onClick={handleExportUsers}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('users')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'users'
              ? 'bg-blue-600 text-white'
              : 'text-gray-300 hover:bg-gray-700'
          }`}
        >
          <Users className="h-4 w-4 mr-2" />
          Users
        </button>
        <button
          onClick={() => setActiveTab('analytics')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'analytics'
              ? 'bg-blue-600 text-white'
              : 'text-gray-300 hover:bg-gray-700'
          }`}
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          Analytics
        </button>
        <button
          onClick={() => setActiveTab('emails')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'emails'
              ? 'bg-blue-600 text-white'
              : 'text-gray-300 hover:bg-gray-700'
          }`}
        >
          <Mail className="h-4 w-4 mr-2" />
          Sent Emails
        </button>
      </div>

      {/* Users Tab Content */}
      {activeTab === 'users' && (
        <>
          {/* Statistics Cards */}
          {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Total Users</p>
                  <p className="text-2xl font-bold text-white">{statistics.totalUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Active Users</p>
                  <p className="text-2xl font-bold text-white">{statistics.activeUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">New This Month</p>
                  <p className="text-2xl font-bold text-white">{statistics.newUsersThisMonth}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-yellow-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-400">Admin Users</p>
                  <p className="text-2xl font-bold text-white">{statistics.adminUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Search className="h-5 w-5 mr-2 text-gray-400" />
            Search & Filter Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <Input
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <Select value={selectedRole} onValueChange={(value) => {
              setSelectedRole(value as UserRole | 'all');
              handleFilterChange();
            }}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="student">Student</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={(value) => {
              setSelectedStatus(value);
              handleFilterChange();
            }}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2 mt-4">
            <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button
              onClick={() => {
                setSearchTerm('');
                setSelectedRole('all');
                setSelectedStatus('all');
                loadUsers(true);
              }}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-white">
                {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex gap-2">
                <EmailNotificationButton
                  selectedUserIds={selectedUsers}
                  onEmailSent={() => {
                    setActiveTab('emails');
                    console.log('Email sent to selected users - switched to emails tab');
                  }}
                />
                <Button
                  onClick={() => setShowBulkModal(true)}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Bulk Actions
                </Button>
                <Button
                  onClick={() => setSelectedUsers([])}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Users Table */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            <span className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-gray-400" />
              Users ({users.length})
            </span>
            <Button
              onClick={toggleSelectAll}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              {selectedUsers.length === users.length ? 'Deselect All' : 'Select All'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">Loading users...</div>
            </div>
          ) : users.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">No users found</div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">
                      <input
                        type="checkbox"
                        checked={selectedUsers.length === users.length && users.length > 0}
                        onChange={toggleSelectAll}
                        className="rounded"
                      />
                    </th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Role</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Courses</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Last Login</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.uid} className="border-b border-gray-700 hover:bg-gray-750">
                      <td className="py-3 px-4">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.uid)}
                          onChange={() => toggleUserSelection(user.uid)}
                          className="rounded"
                        />
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <p className="text-white font-medium">{user.displayName}</p>
                          <p className="text-gray-400 text-sm">{user.email}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <RoleBadge role={user.role} />
                      </td>
                      <td className="py-3 px-4">
                        <StatusBadge status={(user as any).status || 'active'} />
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <p className="text-white">{user.enrolledCourses.length} enrolled</p>
                          <p className="text-gray-400">{user.completedCourses.length} completed</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-gray-300 text-sm">{formatDate(user.lastLoginAt)}</p>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-white"
                            onClick={() => handleViewUser(user)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-white"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-red-400"
                            onClick={() => handleDeleteUser(user.uid)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadUsers(false)}
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
        </>
      )}

      {/* Analytics Tab Content */}
      {activeTab === 'analytics' && (
        <UserAnalytics />
      )}

      {/* Sent Emails Tab Content */}
      {activeTab === 'emails' && (
        <SentEmailsHistory />
      )}

      {/* Modals */}
      <CreateUserModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onUserCreated={() => {
          loadUsers(true);
          loadStatistics();
        }}
      />

      <EditUserModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedUser(null);
        }}
        onUserUpdated={() => {
          loadUsers(true);
          loadStatistics();
        }}
        user={selectedUser}
      />

      <UserDetailsModal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
      />

      <BulkOperationsModal
        isOpen={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        selectedUserIds={selectedUsers}
        onOperationComplete={() => {
          setSelectedUsers([]);
          loadUsers(true);
          loadStatistics();
        }}
      />
    </div>
  );
};
