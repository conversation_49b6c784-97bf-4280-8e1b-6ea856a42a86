import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Navigation } from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { courseService } from '@/services/courseService';
import { enrollmentService } from '@/services/enrollmentService';
import { simpleReviewService, SimpleReview } from '@/services/simpleReviewService';
import { Course } from '@/types/course';
import {
  ArrowLeft,
  PlayCircle,
  BookOpen,
  Clock,
  Users,
  Star,
  CheckCircle,
  Download,
  Globe,
  Smartphone,
  Monitor,
  Award,
  Infinity,
  ChevronDown,
  ChevronUp,
  User,
  Target,
  List,
  MessageSquare,
  Lock,
  Unlock,

} from 'lucide-react';



// Module Accordion Component
const ModuleAccordion = ({ module, index, isEnrolled }: { module: any, index: number; isEnrolled: boolean }) => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const { courseId } = useParams();

  const handlePreviewLesson = (lesson: any) => {
    const lessonIndex = module.lessons.findIndex((l: any) => l.id === lesson.id);
    console.log('🎯 Navigating to lesson:', lesson.title, 'Module:', index, 'Lesson:', lessonIndex);

    // Navigate to course content page with specific lesson
    navigate(`/course/${courseId}/content`, {
      state: {
        lesson: lesson,
        moduleIndex: index,
        lessonIndex: lessonIndex
      }
    });
  };

  return (
    <div className="border border-gray-600 rounded-lg bg-gray-800/50 backdrop-blur-sm">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-6 py-5 flex items-center text-left hover:bg-gray-700/50 transition-all duration-200"
      >
        {/* Expandable icon at start - faces down when not expanded */}
        <div className="mr-4 flex-shrink-0">
          {isOpen ? <ChevronUp className="w-5 h-5 text-blue-400" /> : <ChevronDown className="w-5 h-5 text-blue-400" />}
        </div>

        <div className="flex-1">
          <h4 className="font-semibold text-white text-lg font-inter">
            Module {index + 1}: {module?.title || 'Module Title'}
          </h4>
          <p className="text-sm text-gray-400 mt-1 font-inter">
            {module?.lessons?.length || 0} lectures • {module?.duration || '30 min'}
          </p>
        </div>
      </button>

      {isOpen && (
        <div className="px-6 pb-6 border-t border-gray-600/50">
          {module?.lessons && module.lessons.length > 0 ? (
            <div className="space-y-2 mt-4">
              {module.lessons.map((lesson: any, lessonIndex: number) => {
                const isFree = lesson?.isFree || false;
                const isAccessible = isFree || isEnrolled;

                return (
                  <div
                    key={lesson?.id || lessonIndex}
                    className={`flex items-center justify-between py-4 px-4 rounded-lg transition-all duration-200 ${
                      isAccessible
                        ? 'hover:bg-gray-700/30 border border-gray-600/30'
                        : 'border border-gray-700/50 bg-gray-800/30'
                    }`}
                  >
                    <div className="flex items-center flex-1">
                      {/* Video/Lock icon at start of lessons */}
                      {isFree ? (
                        <PlayCircle className="w-5 h-5 mr-4 text-green-400" />
                      ) : isEnrolled ? (
                        <PlayCircle className="w-5 h-5 mr-4 text-blue-400" />
                      ) : (
                        <Lock className="w-5 h-5 mr-4 text-gray-500" />
                      )}

                      <div className="flex-1">
                        <div className="flex items-center gap-3">
                          <span className={`text-base font-medium font-inter ${
                            isAccessible ? 'text-white' : 'text-gray-500'
                          }`}>
                            {lessonIndex + 1}. {lesson?.title || 'Lesson Title'}
                          </span>
                          {isFree && (
                            <Badge variant="secondary" className="bg-green-600/20 text-green-400 text-xs px-2 py-1 border border-green-600/30">
                              FREE PREVIEW
                            </Badge>
                          )}
                        </div>
                        {!isAccessible && (
                          <p className="text-xs text-gray-500 mt-1 font-inter">
                            🔒 Enroll in course to access this lesson
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="flex items-center text-gray-400">
                        <Clock className="w-4 h-4 mr-1" />
                        <span className="text-sm font-inter">{lesson?.duration || '5 min'}</span>
                      </div>

                      {/* Show preview button only for free lessons when user is not enrolled */}
                      {isFree && !isEnrolled && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePreviewLesson(lesson)}
                          className="border-green-600/50 text-green-400 hover:bg-green-600/10 hover:border-green-500 font-inter text-xs px-3 py-1"
                        >
                          <PlayCircle className="w-3 h-3 mr-1" />
                          Preview
                        </Button>
                      )}

                      {/* Show play button for enrolled users on all lessons */}
                      {isEnrolled && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePreviewLesson(lesson)}
                          className="border-blue-600/50 text-blue-400 hover:bg-blue-600/10 hover:border-blue-500 font-inter text-xs px-3 py-1"
                        >
                          <PlayCircle className="w-3 h-3 mr-1" />
                          Play
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-sm text-gray-400 mt-4 px-4 font-inter">Lessons coming soon...</p>
          )}
        </div>
      )}
    </div>
  );
};





export default function CourseDetail() {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [checkingEnrollment, setCheckingEnrollment] = useState(true);

  // Simple review state
  const [reviews, setReviews] = useState<SimpleReview[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [newReview, setNewReview] = useState({ rating: 5, comment: '' });
  const [submittingReview, setSubmittingReview] = useState(false);


  useEffect(() => {
    if (courseId) {
      loadCourse();
      if (currentUser) {
        checkEnrollmentAndRedirect();
      } else {
        setCheckingEnrollment(false);
      }
    }
  }, [courseId, currentUser]);

  // Load reviews
  const loadReviews = async () => {
    if (!courseId) {
      console.log('❌ No courseId provided for loading reviews');
      return;
    }

    console.log('📖 Loading reviews for course:', courseId);
    setReviewsLoading(true);

    try {
      const courseReviews = await simpleReviewService.getCourseReviews(courseId);
      console.log('✅ Loaded reviews from Firestore:', courseReviews.length, courseReviews);
      setReviews(courseReviews);

      // Cache in localStorage
      localStorage.setItem(`simple_reviews_${courseId}`, JSON.stringify(courseReviews));
      console.log('💾 Cached reviews in localStorage');
    } catch (error) {
      console.error('❌ Error loading reviews from Firestore:', error);

      // Try to load from cache
      const cached = localStorage.getItem(`simple_reviews_${courseId}`);
      if (cached) {
        try {
          const cachedReviews = JSON.parse(cached);
          console.log('📦 Loaded reviews from cache:', cachedReviews.length, cachedReviews);
          setReviews(cachedReviews);
        } catch (parseError) {
          console.error('❌ Error parsing cached reviews:', parseError);
        }
      }
    } finally {
      setReviewsLoading(false);
    }
  };

  // Submit review
  const submitReview = async () => {
    if (!currentUser || !courseId || !newReview.comment.trim()) {
      console.log('❌ Cannot submit review - missing data:', {
        currentUser: !!currentUser,
        courseId,
        comment: newReview.comment.trim()
      });
      return;
    }

    console.log('📝 Submitting review:', {
      courseId,
      userId: currentUser.uid,
      userName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Anonymous',
      rating: newReview.rating,
      comment: newReview.comment.trim()
    });

    setSubmittingReview(true);
    try {
      const reviewId = await simpleReviewService.submitReview({
        courseId,
        userId: currentUser.uid,
        userName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Anonymous',
        userEmail: currentUser.email || '',
        rating: newReview.rating,
        comment: newReview.comment.trim()
      });

      console.log('✅ Review submitted successfully with ID:', reviewId);

      // Reset form
      setNewReview({ rating: 5, comment: '' });

      // Wait a moment for Firestore to propagate, then reload reviews
      setTimeout(async () => {
        console.log('🔄 Reloading reviews after submission...');
        await loadReviews();
      }, 1000);

    } catch (error) {
      console.error('❌ Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setSubmittingReview(false);
    }
  };

  // Load reviews when component mounts
  useEffect(() => {
    if (courseId) {
      loadReviews();
    }
  }, [courseId]);

  const checkEnrollmentAndRedirect = async () => {
    if (!currentUser || !courseId) {
      setCheckingEnrollment(false);
      return;
    }

    try {
      const enrolled = await enrollmentService.isUserEnrolled(currentUser.uid, courseId);
      setIsEnrolled(enrolled);

      if (enrolled) {
        // User is enrolled - course content page will be implemented later
        console.log('User is enrolled in course:', courseId);
        return;
      }
    } catch (error) {
      console.error('Error checking enrollment:', error);
    } finally {
      setCheckingEnrollment(false);
    }
  };

  const loadCourse = async () => {
    if (!courseId) {
      setError('No course ID provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const courseData = await courseService.getCourseWithModules(courseId);



      if (courseData) {


        // Ensure all lessons have the isFree property
        const normalizedCourse = {
          ...courseData,
          modules: courseData.modules?.map((module: any) => ({
            ...module,
            lessons: module.lessons?.map((lesson: any) => ({
              ...lesson,
              isFree: lesson.isFree !== undefined ? lesson.isFree : false
            })) || []
          })) || []
        };

        setCourse(normalizedCourse);
      } else {
        setError('Course not found');
      }
    } catch (err: any) {
      console.error('Error loading course:', err);
      setError(`Failed to load course: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = () => {
    if (!currentUser) {
      navigate('/login');
      return;
    }
    navigate(`/checkout/${courseId}`);
  };

  const handlePaymentSuccess = async () => {
    setIsEnrolled(true);

    if (currentUser && courseId) {
      const progressKey = `progress_${currentUser.uid}_${courseId}`;
      const newProgress = {
        id: progressKey,
        userId: currentUser.uid,
        courseId: courseId,
        enrolledAt: new Date().toISOString(),
        lastAccessedAt: new Date().toISOString(),
        completionPercentage: 0,
        completedLessons: [],
        completedAssignments: [],
        completedQuizzes: [],
        totalWatchTime: 0,
        certificateEarned: false
      };

      localStorage.setItem(progressKey, JSON.stringify(newProgress));

      // Redirect to course content page after successful enrollment
      setTimeout(() => {
        navigate(`/course/${courseId}/content`);
      }, 1000);
    }
  };

  const checkLocalEnrollment = () => {
    if (!currentUser || !courseId) return false;
    const progressKey = `progress_${currentUser.uid}_${courseId}`;
    const progressData = localStorage.getItem(progressKey);

    if (progressData) {
      try {
        const progress = JSON.parse(progressData);
        // Only consider enrolled if there's an enrolledAt timestamp (indicating payment completion)
        return progress.enrolledAt &&
          progress.userId === currentUser.uid &&
          progress.courseId === courseId;
      } catch (error) {
        console.error('Error parsing progress data:', error);
        return false;
      }
    }
    return false;
  };

  const enrolled = checkLocalEnrollment() || isEnrolled;

  // Debug enrollment status
  console.log('👤 Enrollment status:', {
    enrolled,
    checkLocalEnrollment: checkLocalEnrollment(),
    isEnrolled,
    currentUser: !!currentUser,
    courseId
  });



  // Checkout flow is now handled by the dedicated CheckoutPage
  // Remove this old checkout flow since we have a new secure checkout page

  if (loading || checkingEnrollment) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">
              {checkingEnrollment ? 'Checking enrollment...' : 'Loading course...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navigation />
        <div className="pt-16 flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">Course Not Found</h2>
            <p className="text-gray-400 mb-6">{error}</p>
            <Button onClick={() => navigate('/')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Homepage
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navigation />
      
      <div className="pt-16">
        {/* Hero Section */}
        <div className="bg-gray-800 text-white">
          <div className="container mx-auto px-4 py-8">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Left Content */}
              <div className="lg:col-span-2">
                <h1 className="text-3xl md:text-4xl font-bold mb-6 font-inter leading-tight">{course?.title || 'Course Title'}</h1>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed font-inter">{course?.shortDescription || course?.description || 'Course description'}</p>

                {/* Course Stats */}
                <div className="flex flex-wrap items-center gap-8 mb-8">
                  <div className="flex items-center text-yellow-400">
                    <span className="font-bold text-lg mr-2 font-inter">{course?.rating || 4.8}</span>
                    <div className="flex mr-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${star <= Math.floor(course?.rating || 4.8) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`}
                        />
                      ))}
                    </div>
                    <span className="text-gray-400 font-inter">
                      ({course?.reviewCount || 0} ratings)
                    </span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <Users className="w-5 h-5 mr-2" />
                    <span className="font-inter">{course?.enrollmentCount || course?.enrolledCount || 0} students</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <Clock className="w-5 h-5 mr-2" />
                    <span className="font-inter">{course?.duration || '8 hours'} total</span>
                  </div>
                </div>

                {/* Created by */}
                <div className="flex items-center mb-4">
                  <span className="text-gray-400">Created by </span>
                  <span className="text-blue-400 ml-1 font-medium">
                    {course?.instructor || 'Ahmed Takal'}
                  </span>
                </div>
              </div>

              {/* Right Sidebar - Price Card */}
              <div className="lg:col-span-1">
                <Card className="bg-white shadow-xl">
                  <CardContent className="p-6">
                    {/* Price */}
                    <div className="mb-4">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-3xl font-bold text-gray-900">
                          {course?.price === 0 ? 'Free' : `KES ${(course?.price || 0).toLocaleString()}`}
                        </span>
                        {course?.originalPrice && course?.originalPrice > (course?.price || 0) && (
                          <span className="text-lg text-gray-500 line-through">
                            KES {course.originalPrice.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* CTA Button */}
                    {enrolled ? (
                      <Button
                        onClick={() => navigate(`/course/${courseId}/content`)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white py-3 mb-3"
                        size="lg"
                      >
                        <PlayCircle className="w-4 h-4 mr-2" />
                        Continue Learning
                      </Button>
                    ) : (
                      <Button
                        onClick={handleEnroll}
                        className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 mb-3"
                        size="lg"
                      >
                        Buy now
                      </Button>
                    )}



                    {/* Course includes */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-3">This course includes:</h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <div className="flex items-center">
                          <Monitor className="w-4 h-4 mr-3" />
                          {course?.duration || 'Self-paced'} on-demand video
                        </div>
                        <div className="flex items-center">
                          <Download className="w-4 h-4 mr-3" />
                          Downloadable resources
                        </div>
                        <div className="flex items-center">
                          <Infinity className="w-4 h-4 mr-3" />
                          Full lifetime access
                        </div>
                        <div className="flex items-center">
                          <Award className="w-4 h-4 mr-3" />
                          Certificate of completion
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="bg-gray-900 text-white">
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto space-y-12">

              {/* About the Course */}
              <section className="space-y-6">
                <div>
                  <h2 className="text-3xl font-bold mb-6 text-white">About this course</h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-300 leading-relaxed mb-4 text-lg">
                      {course?.description || 'Course description coming soon...'}
                    </p>
                    {course?.shortDescription && course.shortDescription !== course?.description && (
                      <p className="text-gray-300 leading-relaxed text-lg">
                        {course.shortDescription}
                      </p>
                    )}
                  </div>
                </div>

                {/* Course Features */}
                <div className="grid md:grid-cols-2 gap-6 mt-8">
                  <div>
                    <h3 className="font-semibold text-xl mb-4 text-white">Course Features</h3>
                    <div className="space-y-3">
                      <div className="flex items-center text-gray-300">
                        <Clock className="w-5 h-5 mr-3 text-blue-400" />
                        <span className="text-lg">{course?.totalDuration ? `${Math.floor(course.totalDuration / 60)} hours` : (course?.duration || 'Self-paced')} of content</span>
                      </div>
                      <div className="flex items-center text-gray-300">
                        <BookOpen className="w-5 h-5 mr-3 text-blue-400" />
                        <span className="text-lg">{course?.modules?.length || 0} modules</span>
                      </div>
                      {(() => {
                        const freeLessons = course?.modules?.reduce((total: number, module: any) =>
                          total + (module?.lessons?.filter((lesson: any) => lesson?.isFree)?.length || 0), 0) || 0;
                        return freeLessons > 0 && (
                          <div className="flex items-center text-gray-300">
                            <Unlock className="w-5 h-5 mr-3 text-green-400" />
                            <span className="text-lg">{freeLessons} free preview lesson{freeLessons !== 1 ? 's' : ''}</span>
                          </div>
                        );
                      })()}
                      <div className="flex items-center text-gray-300">
                        <Award className="w-5 h-5 mr-3 text-blue-400" />
                        <span className="text-lg">Certificate of completion</span>
                      </div>
                      <div className="flex items-center text-gray-300">
                        <Infinity className="w-5 h-5 mr-3 text-blue-400" />
                        <span className="text-lg">Lifetime access</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl mb-4 text-white">Skills You'll Gain</h3>
                    <div className="flex flex-wrap gap-2">
                      {course?.tags?.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-blue-600 text-blue-100 text-sm px-3 py-1">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </section>

              {/* What You'll Learn */}
              <section className="space-y-6">
                <div>
                  <h2 className="text-3xl font-bold mb-8 text-white font-inter">What you'll learn</h2>
                  <div className="grid md:grid-cols-2 gap-6">
                    {course?.learningOutcomes?.map((outcome, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="w-6 h-6 text-green-400 mt-1 mr-4 flex-shrink-0" />
                        <span className="text-gray-300 text-lg leading-relaxed font-inter">{outcome}</span>
                      </div>
                    )) || (
                      <div className="col-span-2 text-gray-400 text-lg font-inter">Learning outcomes will be added soon...</div>
                    )}
                  </div>
                </div>
              </section>

              {/* Curriculum */}
              <section className="space-y-6" data-section="curriculum">
                <div>
                  <h2 className="text-3xl font-bold mb-8 text-white font-inter">Course content</h2>
                  <div className="mb-8 text-lg text-gray-300 font-inter">
                    {course?.modules?.length || 0} modules • {course?.totalDuration ? `${Math.floor(course.totalDuration / 60)} total hours` : 'Self-paced'}
                  </div>
                  <div className="space-y-4">
                    {course?.modules?.map((module, index) => (
                      <ModuleAccordion key={module?.id || index} module={module} index={index} isEnrolled={enrolled} />
                    )) || (
                      <div className="text-gray-400 text-center py-8 text-lg">Course curriculum will be available soon...</div>
                    )}
                  </div>
                </div>
              </section>

              {/* Target Audience */}
              <section className="space-y-6">
                <div>
                  <h2 className="text-3xl font-bold mb-6 text-white">Who this course is for</h2>
                  <div className="space-y-8">
                    <div>
                      <h3 className="font-semibold text-xl mb-4 flex items-center text-white">
                        <Target className="w-6 h-6 mr-3 text-blue-400" />
                        Target Audience
                      </h3>
                      <ul className="space-y-3">
                        {course?.targetAudience?.map((audience, index) => (
                          <li key={index} className="flex items-start">
                            <span className="w-3 h-3 bg-blue-400 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                            <span className="text-gray-300 text-lg">{audience}</span>
                          </li>
                        )) || (
                          <li className="text-gray-400 text-lg">Target audience information will be added soon...</li>
                        )}
                      </ul>
                    </div>

                    <div>
                      <h3 className="font-semibold text-xl mb-4 flex items-center text-white">
                        <List className="w-6 h-6 mr-3 text-green-400" />
                        Prerequisites
                      </h3>
                      <ul className="space-y-3">
                        {course?.requirements?.map((requirement, index) => (
                          <li key={index} className="flex items-start">
                            <span className="w-3 h-3 bg-green-400 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                            <span className="text-gray-300 text-lg">{requirement}</span>
                          </li>
                        )) || (
                          <li className="text-gray-400 text-lg">No specific prerequisites required</li>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              </section>

              {/* Your Instructor */}
              <section className="space-y-6">
                <div>
                  <h2 className="text-3xl font-bold mb-8 text-white font-inter">Your instructor</h2>
                  <div className="flex items-start space-x-8">
                    <div className="w-40 h-40 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-4xl font-bold flex-shrink-0">
                      {(course?.instructor || 'Ahmed Takal').charAt(0)}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-3xl font-bold text-blue-400 mb-3">
                        {course?.instructor || 'Ahmed Takal'}
                      </h3>
                      <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                        {course?.instructorBio || 'Expert instructor with years of experience in technology education and software development.'}
                      </p>

                      {/* Instructor Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                        <div className="text-center">
                          <div className="flex items-center justify-center mb-2">
                            <Star className="w-5 h-5 text-yellow-400 fill-yellow-400 mr-1" />
                            <span className="font-bold text-xl text-white">4.9</span>
                          </div>
                          <p className="text-gray-400">Instructor Rating</p>
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-xl mb-2 text-white">2,847</div>
                          <p className="text-gray-400">Reviews</p>
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-xl mb-2 text-white">15,432</div>
                          <p className="text-gray-400">Students</p>
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-xl mb-2 text-white">12</div>
                          <p className="text-gray-400">Courses</p>
                        </div>
                      </div>

                      {/* Instructor Expertise */}
                      <div>
                        <h4 className="font-semibold text-xl mb-3 text-white">Areas of Expertise</h4>
                        <div className="flex flex-wrap gap-3">
                          {course?.instructorExpertise?.map((expertise, index) => (
                            <Badge key={index} variant="outline" className="bg-blue-600 text-blue-100 border-blue-500 px-3 py-1 text-sm">
                              {expertise}
                            </Badge>
                          )) || (
                            <>
                              <Badge variant="outline" className="bg-blue-600 text-blue-100 border-blue-500 px-3 py-1 text-sm">FlutterFlow</Badge>
                              <Badge variant="outline" className="bg-blue-600 text-blue-100 border-blue-500 px-3 py-1 text-sm">No-Code Development</Badge>
                              <Badge variant="outline" className="bg-blue-600 text-blue-100 border-blue-500 px-3 py-1 text-sm">Mobile Apps</Badge>
                              <Badge variant="outline" className="bg-blue-600 text-blue-100 border-blue-500 px-3 py-1 text-sm">Web Development</Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Simple Reviews - Temporarily showing for all users for testing */}
              {true && (
                <section className="space-y-6">
                  <div>
                    <div className="flex items-center justify-between mb-8">
                      <h2 className="text-3xl font-bold text-white font-inter">Student Reviews</h2>
                      <div className="flex gap-2">
                        <Button
                          onClick={loadReviews}
                          variant="outline"
                          size="sm"
                          disabled={reviewsLoading}
                          className="text-xs"
                        >
                          {reviewsLoading ? 'Loading...' : 'Refresh Reviews'}
                        </Button>
                        {process.env.NODE_ENV === 'development' && (
                          <div className="flex items-center gap-2">
                            <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded">
                              Reviews: {reviews.length} | Loading: {reviewsLoading.toString()} | Course: {courseId}
                            </div>
                            <Button
                              onClick={async () => {
                                console.log('🔍 Testing Firestore connection...');
                                try {
                                  const allReviews = await simpleReviewService.getCourseReviews('test-course-video-player');
                                  console.log('🔍 All reviews for test course:', allReviews);
                                  alert(`Found ${allReviews.length} reviews in Firestore`);
                                } catch (error) {
                                  console.error('🔍 Test failed:', error);
                                  alert('Test failed: ' + error);
                                }
                              }}
                              variant="outline"
                              size="sm"
                              className="text-xs"
                            >
                              Test DB
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Review Stats */}
                    {reviews.length > 0 && (
                      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-600/30 mb-8">
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <div className="text-4xl font-bold text-white mb-2 font-inter">
                              {simpleReviewService.getReviewStats(reviews).averageRating}
                            </div>
                            <div className="flex justify-center mb-2">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className={`w-5 h-5 ${star <= Math.floor(simpleReviewService.getReviewStats(reviews).averageRating) ? 'fill-orange-400 text-orange-400' : 'text-gray-500'}`}
                                />
                              ))}
                            </div>
                            <p className="text-gray-400 font-inter">
                              {reviews.length} review{reviews.length !== 1 ? 's' : ''}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Add Review Form - Only for enrolled users */}
                    {currentUser && isEnrolled ? (
                      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-600/30 mb-8">
                        <h3 className="text-xl font-semibold text-white mb-4 font-inter">Share your experience</h3>

                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2 font-inter">Rating</label>
                            <div className="flex space-x-1">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <button
                                  key={star}
                                  type="button"
                                  onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                                  className="focus:outline-none transition-transform hover:scale-110"
                                  disabled={submittingReview}
                                >
                                  <Star
                                    className={`w-6 h-6 transition-colors ${
                                      star <= newReview.rating ? 'fill-orange-400 text-orange-400' : 'text-gray-500 hover:text-orange-300'
                                    }`}
                                  />
                                </button>
                              ))}
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2 font-inter">Your review</label>
                            <textarea
                              value={newReview.comment}
                              onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                              placeholder="Share your thoughts about this course..."
                              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-inter resize-none"
                              rows={4}
                              disabled={submittingReview}
                              maxLength={500}
                            />
                            <p className="text-xs text-gray-400 mt-1 font-inter">
                              {newReview.comment.length}/500 characters
                            </p>
                          </div>

                          <Button
                            onClick={submitReview}
                            disabled={submittingReview || !newReview.comment.trim()}
                            className="bg-blue-600 hover:bg-blue-700 text-white font-inter"
                          >
                            {submittingReview ? (
                              <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                Submitting...
                              </>
                            ) : (
                              'Submit Review'
                            )}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-600/30 mb-8">
                        <h3 className="text-xl font-semibold text-white mb-4 font-inter">Share your experience</h3>
                        <div className="text-center py-8">
                          <div className="text-gray-400 mb-4">
                            {!currentUser ? (
                              <>
                                <p className="mb-2">Please sign in to leave a review</p>
                                <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white">
                                  <Link to="/login">Sign In</Link>
                                </Button>
                              </>
                            ) : (
                              <>
                                <p className="mb-2">Only enrolled students can leave reviews</p>
                                <p className="text-sm text-gray-500">Enroll in this course to share your experience</p>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Reviews List */}
                    <div className="space-y-6">
                      {reviewsLoading ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                          <span className="text-gray-400 font-inter">Loading reviews...</span>
                        </div>
                      ) : reviews.length > 0 ? (
                        reviews.map((review) => (
                          <div key={review.id} className="bg-gray-800/30 rounded-lg p-6 border border-gray-600/20">
                            <div className="flex items-start space-x-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                                {review.userName.charAt(0).toUpperCase()}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h4 className="font-semibold text-white font-inter">{review.userName}</h4>
                                  <div className="flex">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <Star
                                        key={star}
                                        className={`w-4 h-4 ${star <= review.rating ? 'fill-orange-400 text-orange-400' : 'text-gray-500'}`}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-sm text-gray-400 font-inter">
                                    {review.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}
                                  </span>
                                </div>
                                <p className="text-gray-300 leading-relaxed font-inter">{review.comment}</p>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-12">
                          <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <MessageSquare className="w-8 h-8 text-gray-400" />
                          </div>
                          <h3 className="text-xl font-semibold text-white mb-2 font-inter">No reviews yet</h3>
                          <p className="text-gray-400 font-inter">Be the first to share your experience!</p>
                        </div>
                      )}
                    </div>
                  </div>
                </section>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
